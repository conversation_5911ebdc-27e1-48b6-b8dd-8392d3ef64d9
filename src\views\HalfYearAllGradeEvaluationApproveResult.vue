<template>
  <div class="deputy-director-container">
    <div class="row">
      <div class="col-xs-12 text-left">
        <h4 class="col-xs-6">【{{ getCompanyName() }}】{{ $route.name }}</h4>
        <div class="col-xs-6 text-right" style="line-height: 40px">
          <div style="display: inline-block; margin: 0 10px">
            <label>审批状态：{{ approveStatus }}</label>
          </div>
          <button class="btn btn-primary btn-xs" @click="showDescrDialog">
            评定说明
          </button>
          <button
            class="btn btn-primary btn-xs"
            :disabled="disabledButton"
            @click="_updateAdjustLevel"
            v-if="
              $store.state.doubleCol.arrAuths.point_ablity_updateAdjustLevel
            "
          >
            保存录入
          </button>
          <button
            class="btn btn-primary btn-xs"
            :disabled="disabledButton"
            @click="_updateAdjustLevelSendOA"
            v-if="
              $store.state.doubleCol.arrAuths
                .point_ablity_updateAdjustLevelSendOA
            "
          >
            提交OA
          </button>
          <button
            class="btn btn-primary btn-xs"
            @click="$router.push(`/${$route.params.year}/6/menu/1`)"
          >
            返回目录
          </button>
        </div>
        <span style="margin-left: 24px; color: red; font-size: 16px"
          ><strong> 提醒：</strong>每个职等的 “本期合议调整”
          栏数据录入完成后，记得点击 “保存录入” 一次，以免数据丢失。</span
        >
      </div>
    </div>
    <div class="row">
      <div class="col-xs-12" style="width: auto; padding: 0">
        <div class="list-table">
          <div class="scroll-table" style="height: 100%; overflow-x: hidden">
            <table class="scroll-table-header table-bordered">
              <colgroup>
                <col width="45" />
                <col width="100" />
                <col width="70" />
                <col width="45" />
                <col width="120" />
                <col width="80" />
                <col width="70" />
                <col width="70" />
                <col width="70" />
                <col width="70" />
                <col width="70" />
                <col width="50" />
                <col width="50" />
                <col width="50" />
                <col width="50" />
                <col width="80" />
                <col width="80" />
                <col width="40" />
                <col width="100" />
              </colgroup>
              <thead>
                <tr>
                  <th colspan="2">系统维护人员：</th>
                  <th colspan="17" style="text-align: left; padding-left: 10px">
                    章坤、慕军隆
                  </th>
                </tr>
                <tr>
                  <th rowspan="2">序号</th>
                  <th rowspan="2">厂/部门</th>
                  <th rowspan="2">姓名</th>
                  <th rowspan="2">性别</th>
                  <th rowspan="2">岗位</th>
                  <th rowspan="2">职等</th>
                  <th rowspan="2">职等工龄</th>
                  <th rowspan="2">华劲工龄</th>
                  <th colspan="3">评定及审核审批人</th>
                  <th colspan="2">上期评定结果</th>
                  <th colspan="2">本期评定结果</th>
                  <th colspan="2">本期合议调整</th>
                  <th rowspan="2">工作亮点</th>
                  <th rowspan="2">备注</th>
                </tr>
                <tr>
                  <th>评定人</th>
                  <th>审核人</th>
                  <th>审批人</th>
                  <th>级别</th>
                  <th>职级</th>
                  <th>级别</th>
                  <th>职级</th>
                  <th>级别</th>
                  <th>职级</th>
                </tr>
              </thead>
            </table>
            <div
              class="scroll-table-body"
              :style="{
                overflow: 'auto',
                maxHeight: body_height - 210 + 'px',
                'overflow-y': 'scroll',
                'overflow-x': 'hidden',
              }"
            >
              <table
                v-for="(data, index) in tableData"
                :key="index"
                style="
                  margin: 0px 10px 0 0;
                  position: relative;
                  font-size: 13px;
                  float: left;
                  border: none;
                "
                class="table table-bordered table-hover table-striped"
              >
                <colgroup>
                  <col width="45" />
                  <col width="100" />
                  <col width="70" />
                  <col width="45" />
                  <col width="120" />
                  <col width="80" />
                  <col width="70" />
                  <col width="70" />
                  <col width="70" />
                  <col width="70" />
                  <col width="70" />
                  <col width="50" />
                  <col width="50" />
                  <col width="50" />
                  <col width="50" />
                  <col width="80" />
                  <col width="80" />
                  <col width="40" />
                  <col width="100" />
                </colgroup>
                <thead>
                  <tr>
                    <td
                      style="background: #fff; font-weight: bold"
                      colspan="20"
                    >
                      <div
                        style="
                          text-align: left;
                          width: 50%;
                          float: left;
                          display: inline-block;
                          vertical-align: bottom;
                        "
                      >
                        <br />{{ data.gradeName }}
                      </div>
                      <div
                        style="
                          text-align: left;
                          width: 420px;
                          float: right;
                          display: inline-block;
                        "
                      >
                        {{ data.rateControl }}<br />{{ data.rateControl2 }}
                      </div>
                    </td>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(item, i_index) in data.list" :key="i_index">
                    <td>{{ i_index + 1 }}</td>
                    <td>{{ item.dept_name || "" }}</td>
                    <td>{{ item.user_name || "" }}</td>
                    <td>{{ item.gender || "" }}</td>
                    <td>{{ item.job_name || "" }}</td>
                    <td>{{ item.grade_name || "" }}</td>
                    <td>
                      {{
                        Math.floor(item.work_long / 12) <= 0
                          ? ""
                          : Math.floor(item.work_long / 12) + "年"
                      }}{{
                        item.work_long % 12 === 0
                          ? ""
                          : (item.work_long % 12) + "个月"
                      }}
                    </td>
                    <td>
                      {{
                        Math.floor(item.hwagain_long / 12) <= 0
                          ? ""
                          : Math.floor(item.hwagain_long / 12) + "年"
                      }}{{
                        item.hwagain_long % 12 === 0
                          ? ""
                          : (item.hwagain_long % 12) + "个月"
                      }}
                    </td>
                    <td
                      :title="
                        item.leader_name
                          ? `职务:${item.leader_result || '-'},评定职务:${
                              item.leader_grade_name || '-'
                            }`
                          : ''
                      "
                    >
                      {{ item.leader_name || "" }}
                    </td>
                    <td
                      :title="
                        item.checker_name
                          ? `职务:${item.checker_result || '-'},评定职务:${
                              item.checker_grade_name || '-'
                            }`
                          : ''
                      "
                    >
                      {{ item.checker_name || "" }}
                    </td>
                    <td
                      :title="
                        item.approve_name
                          ? `职务:${item.approve_result || '-'},评定职务:${
                              item.approve_grade_name || '-'
                            }`
                          : ''
                      "
                    >
                      {{ item.approve_name || "" }}
                    </td>

                    <td>{{ item.pre_meeting_level || "" }}</td>
                    <td>{{ item.pre_last_result || "" }}</td>
                    <td>
                      {{ item.status == 30 ? item.meeting_level || "" : "" }}
                    </td>
                    <td>
                      {{ item.status == 30 ? item.last_result || "" : "" }}
                    </td>

                    <td>
                      <select
                        v-if="item.adjust_status != 30"
                        :disabled="item.adjust_status == 30"
                        style="width: 70px; height: 22px"
                        v-model="item.adjustLevel"
                      >
                        <option value>请选择</option>
                        <option
                          v-for="(item_a, index_a) in item.baseLevel"
                          :key="index_a"
                        >
                          {{ item_a.level }}
                        </option>
                      </select>
                      <span v-if="item.adjust_status == 30">{{
                        item.adjustLevel
                      }}</span>
                    </td>
                    <td style="padding: 0">
                      <select
                        v-if="item.adjust_status != 30"
                        :disabled="item.adjust_status == 30"
                        style="width: 70px; height: 22px"
                        v-model="item.adjustResult"
                      >
                        <option value>请选择</option>
                        <option
                          v-for="(item_g, index_g) in selectIndex(
                            item.adjustLevel,
                            item.baseLevel
                          )"
                          :key="index_g"
                        >
                          {{ item_g.levelGrade }}
                        </option>
                      </select>
                      <span v-if="item.adjust_status == 30">{{
                        item.adjustResult
                      }}</span>
                    </td>
                    <td>
                      <el-button
                        @click="
                          $router.push(
                            'positionGradeId/summary?fdId=' +
                              item.fdId +
                              '&name=' +
                              item.user_name
                          )
                        "
                        type="text"
                        size="small"
                        >查看</el-button
                      >
                    </td>
                    <td>{{ item.studentJobCategory || "" }}</td>
                  </tr>
                </tbody>
              </table>
              <span v-if="tableDataLength <= 0">
                {{ tableDataLength == -1 ? "加载中..." : "暂无数据" }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  findCompanyLastResultHalfYear,
  findDetailsListByCompanyGroupId,
  updateAdjustLevel,
  updateAdjustLevelSendOA,
} from "@/api";
export default {
  data() {
    return {
      isLoading: true,
      tableData: [],
      companyGroupId: "",
      year: "",
      month: "6",
      body_height: 0,
      tableDataLength: -1,
    };
  },
  computed: {
    approveStatus() {
      if (this.tableData && this.tableData.length != 0) {
        if (
          this.tableData[0].list[0].adjust_status == "" ||
          this.tableData[0].list[0].adjust_status == 10
        ) {
          return "未提交";
        } else if (this.tableData[0].list[0].adjust_status == "11") {
          return "提交OA";
        } else if (this.tableData[0].list[0].adjust_status == "20") {
          return "审批中";
        } else if (this.tableData[0].list[0].adjust_status == "30") {
          return "已审批";
        }
      } else {
        return "未知状态";
      }
    },
    disabledButton() {
      if (this.isLoading) {
        return true;
      } else if (this.tableData && this.tableData.length != 0) {
        return this.tableData[0].list[0].adjust_status != ""
          ? this.tableData[0].list[0].adjust_status != "10"
            ? true
            : false
          : false;
      }
      return true;
    },
  },
  methods: {
    selectIndex(val, val2) {
      let arr = [];
      if (val === "优") {
        if (val2[0]) {
          arr = val2[0].detailsList;
        }
      } else if (val === "A") {
        if (val2[1]) {
          arr = val2[1].detailsList;
        }
      } else if (val === "B") {
        if (val2[2]) {
          arr = val2[2].detailsList;
        }
      } else if (val === "C") {
        if (val2[3]) {
          arr = val2[3].detailsList;
        }
      } else {
        arr = [];
      }
      return arr;
    },
    getCompanyName() {
      return Utils.getCompanyNameByGroupId(this.companyGroupId);
    },
    getCommitData() {
      let arr = [];
      this.tableData.forEach((data) => {
        arr = arr.concat(data.list);
      });
      return arr;
    },
    _updateAdjustLevelSendOA() {
      if (this.disabledButton) {
        return;
      }
      layer.confirm(
        "是否确定提交OA？",
        {
          title: "提示",
          btn: ["确定", "取消"], //按钮
        },
        (index) => {
          this.isLoading = true;
          updateAdjustLevelSendOA(this.getCommitData()).then((res) => {
            let result = res.success;
            result ? this._findCompanyLastResultHalfYear() : null;
            layer.msg(`提交OA${result ? "成功" : "失败"}`, {
              icon: result ? 1 : 2,
              shade: 0.3,
              shadeClose: true,
            });
          });
        }
      );
    },
    _updateAdjustLevel() {
      if (this.disabledButton) {
        return;
      }
      this.isLoading = true;
      this.getCommitData();
      updateAdjustLevel(this.getCommitData()).then((res) => {
        let result = res.success;
        result ? this._findCompanyLastResultHalfYear() : null;
        layer.msg(`提交${result ? "成功" : "失败"}`, {
          icon: result ? 1 : 2,
          shade: 0.3,
          shadeClose: true,
        });
      });
    },
    itemGradeLevel(dataList, gradeLvelList) {
      let checkGradeExist = function (arr, item) {
        return arr.find((arr_item) =>
          arr_item.levelGrade === item.levelGrade ? true : false
        );
      };
      dataList.forEach((data) => {
        let gradeName = data.gradeName ? data.gradeName.split("：")[0] : "";
        data.list.forEach((data1, index) => {
          data1.gradeLvelList = [];
          let gradeLevels = [];
          gradeLvelList.forEach((grade) => {
            if (
              grade.positionGradeName == gradeName &&
              !checkGradeExist(gradeLevels, grade) &&
              grade.level === (data1.meeting_level || data1.pre_meeting_level)
            ) {
              gradeLevels.push(grade);
            }
          });
          data1.gradeLvelList = gradeLevels;
        });
      });
      this.tableData = dataList;
      this.tableDataLength = this.tableData.length;
      this.tableData.forEach((item) => {
        item.list.forEach((item1) => {
          if (!item1.adjustLevel) {
            item1.adjustLevel = item1.meeting_level;
          }
          if (!item1.adjustResult) {
            item1.adjustResult = item1.last_result;
          }
        });
      });
    },
    _findCompanyLastResultHalfYear() {
      /*先判断有权限才可以查询(modify by huangdh@2019-05-24)*/
      if (
        this.$store.state.doubleCol.arrAuths.point_ablity_findCompanyLastResult
      ) {
        /*有年度查询权限，则自动就在半年度查询，只是查询方法名不一样 */
        findCompanyLastResultHalfYear({
          companyGroupId: this.companyGroupId,
          year: this.year,
          month: this.$route.meta.month,
        }).then((res) => {
          if (!res.success) return;
          this._findDetailsListByCompanyGroupId(res.data ? res.data : []);
        });
      } else {
        layer.msg(`对不起，您没有权限查询本界面.`, {
          icon: 2,
          shade: 0.3,
          shadeClose: true,
        });
      }
    },
    _findDetailsListByCompanyGroupId(list) {
      findDetailsListByCompanyGroupId({
        CompanyGroupId: this.companyGroupId,
        Year: this.year,
        Month: 6,
      }).then((res) => {
        this.isLoading = false;
        this.itemGradeLevel(list, res.data || []);
      });
    },
    showDescrDialog() {
      Utils.layerBox.layerDialogOpen({
        title: "评定说明",
        area: ["760px", "570px"],
        btn: [],
        content: `<div style="padding:10px 20px 0 20px;margin:0 auto;">
          <p style="line-height:24px;">
          <b>半年度综合能力评定说明</b><br/>
            <b>一、综合能力级别与职级定义</b><br/>
            1、级别：体现管理人员在同职等人员中的综合能力水平，根据不同职等人员分为A、B、C三级或A、B两级，每年评定1次。<br/>
            2、职级：体现管理人员在半年时间工作中的工作态度、工作投入程度以及工作效率，在个人综合能力级别基础上，由直接领导进行评定，每半年评定1次。<br/>
            <b>二、半年度评定（6月份）</b><br/>
            1、半年度评定内容：当年上半年综合能力职级评定（属于在年度评定的综合能力级别（上年度12月份）的基础上上，由直接领导进行职级评定）。<br/>
            2、半年度评定目的：当年下半年薪酬职级的执行依据。<br/>
            3、评定人员：评定对象的直接领导。<br/>
            4、评定依据：根据评定对象在工作中的工作态度、工作投入程度以及工作效率进行评定。<br/>
            5、评定流程：<br/>
            （1）评定人确定：人资部在《综合能力评定系统》上确定各评定对象的直接领导。<br/>
            （2）职级评定：直接领导确定后，推送职级评定表到各评定人的OA工作台，各评定人进行职级评定。<br/>
            （3）职级评定结果审核审定：直接领导评定完成后，传审核人审核、审定人审定。<br/>
            6、评定最终结果的合议调整及报批<br/>
            （1）合议人员：以公司为单位进行汇总，由人资部负责人组织公司各体系领导进行合议。<br/>
            （2）合议原则：通过对同一级别人员的综合能力进行横向对比，对于能力评定不合理或职级比例控制不合理的部分人员进行合议调整，调整完成后报董事长审批。<br/>
          </p></div>`,
      });
    },
    initPage() {
      this.body_height = $(window).height();
      const { companyGroupId, year } = this.$route.params;
      this.companyGroupId = companyGroupId;
      this.year = year;
      this._findCompanyLastResultHalfYear();
    },
  },
  watch: {
    $route: function () {
      this.initPage();
    },
  },
  mounted() {
    this.initPage();
    $(window).resize(() => {
      this.body_height = $(window).height();
    });
  },
};
</script>

<style scoped>
select[disabled="disabled"] {
  background: #e1e1e1;
}
/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}
.list-year-data {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
.scroll-table-body {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
</style>
