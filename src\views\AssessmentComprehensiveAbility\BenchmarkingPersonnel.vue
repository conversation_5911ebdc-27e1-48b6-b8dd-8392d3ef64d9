<template>
  <div class="benchmarking-personnel-container">
      <div class="row">
        <div class="col-xs-12 text-left">
          <h4 class="col-xs-6">{{this.$route.params.year}}{{this.month==`6`?`半`:``}}年度{{getCompanyName()}}（{{getGradeName()}}）{{$route.name}}</h4>
          <div class="col-xs-6 text-right" style="line-height:40px;">
            <button type="button" class="btn btn-primary btn-xs" @click="showDescrDialog">评定说明</button>
            <button type="button" class="btn btn-primary btn-xs" :disabled="disabledButton" @click="_updateStandardEmployee(0)"
                v-if="$store.state.doubleCol.arrAuths.point_ablity_updateStandardEmployee">保存录入</button>
            <button type="button" class="btn btn-primary btn-xs" :disabled="disabledButton" @click="_updateStandardEmployee(1)"
                v-if="$store.state.doubleCol.arrAuths.point_ablity_updateStandardEmployee">无标杆评定完成</button>
            <button type="button" class="btn btn-primary btn-xs" :disabled="disabledButton" @click="_updateStandardEmployee(2)"
                v-if="$store.state.doubleCol.arrAuths.point_ablity_updateStandardEmployee">有标杆评定完成</button>
            <button class="btn btn-primary btn-xs" @click="$router.push(`/${$route.params.year}/12/menu/1`)">返回目录</button>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-xs-12">
          <div class="list-table">
            <div class="scroll-table" style="height:100%;overflow-x:hidden;">
                <table class="scroll-table-header table-bordered">
                  <colgroup>
                    <col style="width:50px"/>
                    <col style="width:100px"/>
                    <col style="width:70px"/>
                    <col style="width:40px"/>
                    <col style="width:40px"/>
                    <col style="width:50px"/>
                    <col style="width:130px"/>
                    <col style="width:100px"/>
                    <col style="width:90px"/>
                    <col style="width:90px"/>
                    <col style="width:220px"/>
                  </colgroup>
                  <thead>
                    <tr>
                      <th>评定人：</th>
                      <th colspan="10" style="text-align:left;text-indent:5px;">{{assessorList.map(item=> item.userName).join(";\n")}}</th>
                    </tr>
                    <tr>
                      <th>序号</th>
                      <th>厂部</th>
                      <th>姓名</th>
                      <th>性别</th>
                      <th>年龄</th>
                      <th>学历</th>
                      <th>岗位</th>
                      <th>职等</th>
                      <th>优级、标杆人员</th>
                      <th>综合能力分值</th>
                      <th>评定事例依据</th>
                    </tr>
                  </thead>
                </table>
                <div class="scroll-table-body" :style="{overflow:'auto',maxHeight:body_height - 160 + 'px','overflow-y':'scroll','overflow-x':'hidden'}">
                    <table style="margin:0px 10px 0 0;position: relative;font-size:13px;float:left;border:none;" class="table table-bordered table-hover table-striped">
                        <colgroup>
                          <col style="width:50px"/>
                          <col style="width:100px"/>
                          <col style="width:70px"/>
                          <col style="width:40px"/>
                          <col style="width:40px"/>
                          <col style="width:50px"/>
                          <col style="width:130px"/>
                          <col style="width:100px"/>
                          <col style="width:90px"/>
                          <col style="width:90px"/>
                          <col style="width:220px"/>
                        </colgroup>
                        <tbody>
                          <tr v-for="(data,$index) in tableData" :key="$index">
                            <td>{{$index+1}}</td>
                            <td>{{data.deptName}}</td>
                            <td>{{data.userName}}</td>
                            <td>{{data.gender}}</td>
                            <td>{{data.age}}</td>
                            <td>{{data.degree}}</td>
                            <td>{{data.jobName}}</td>
                            <td>{{data.gradeName}}</td>
                            <td>
                              <select :disabled="!checkStandardSize()&&!data.standardLevel" v-model="data.standardLevel" @change="showStandardName(data)" style="width:90%;height:24px;line-height:28px;">
                                <option value="" selected>请选择优级、标杆</option>
                                <option value="优" selected>优</option>
                                <option value="标杆" selected>标杆</option>
                              </select>
                            </td>
                            <td>
                              <span v-if="data.standardLevel==''"></span>
                              <span v-else-if="data.standardLevel=='优'">90</span>
                              <select v-else :disabled="!checkStandardSize()&&!data.standardPoint" v-model="data.standardPoint" style="width:90%;height:24px;line-height:28px;">
                                <option value="" selected>请选择分值</option>
                                <option v-for="(item,index) in [85,80,75,70,65,60]" :key="index" :value="item">{{item}}</option>
                              </select>
                            </td>
                            <td>
                              <a @click="editRemark(data)" >{{(data.bakRemark || "")==""?"录入":data.bakRemark}}</a>
                              <!-- <a @click="editRemark(data)" ><img src="../../assets/images/action_dtl.png" alt=""/></a> -->
                            </td>
                          </tr>
                      </tbody>
                    </table>
                </div>
            </div>
          </div>
        </div>
      </div>
  </div>
</template>

<script>
import { findDetailsListByYearMonth,findMergeListByCompanyGroupId,updateStandardEmployee,updateMeetingRemark } from '@/api'
export default {
  data(){
    return {
      isLoading:true,
      body_height: 0,
      tableData:[],
      assessorList:[],
      year:"",
      month:"12",//固定年度，半年不显示此页面
      companyGroupId:"",
      positionGradeId:"",
      deptArea:Utils.getQueryParams('deptArea')
    }
  },
  computed:{
    disabledButton(){
      if(this.isLoading || this.tableData.length==0){
        return true;
      }
      else if(this.tableData&&this.tableData.length!=0){
        return !(this.tableData[0].confirmLeaderId!=null&&this.tableData[0].confirmLeaderStatus==30&&(this.tableData[0].isFinishConfirm==null || this.tableData[0].isFinishConfirm==0))
      }
      return true;
    }
  },
  methods:{
    getCompanyName(){
      let companyName=Utils.getCompanyNameByGroupId(this.companyGroupId);
      if (this.positionGradeId=="0006" || this.positionGradeId=="0007" || this.positionGradeId=="0032" || this.positionGradeId=="0033"){
        companyName="全集团";
      }
      return companyName;
    },
    getGradeName(){
      let name=Utils.getGradeLevlByGradeId(this.positionGradeId);
      if (this.deptArea=="A"){
        name=name+"-生产一线"
      }
      else if (this.deptArea=="B"){
        name=name+"-生产辅助"
      }
      else if (this.deptArea=="C"){
        name=name+"-生产支持"
      }
      return name;
    },
    checkStandardSize(){
      let count = 0;
      for(let i=0;i<this.tableData.length;i++){
        let data = this.tableData[i];
        if(data.standardPoint){
          count ++;
        }
      }
      return true;
    },
    showDescrDialog(){
      Utils.layerBox.layerDialogOpen({
          title:'评定说明',
          area:['700px','400px'],
          btn:['关闭'],
          content: `<div style="padding:10px 20px;margin:0 auto;">
          <p style="line-height:24px;">
          1、评定办法：现场合议评定，原则上每个职等至少确定1个标杆，若是情况特殊，不需设定标杆，点击“无标杆评定完成”，若是正常情况下设定了标杆，则点击“有标杆评定完成”。<br/>
          2、优级人员评定：根据总目录《评定说明》的优级人员的条件评定，有符合条件的则在符合条件人员的综合能力分值栏选择录入“90分”，在 “优级、标杆人员”栏自动显示“优”，没有符合优级条件的就不需要评定。条件及要求简写如下：<br/>
          （1）特殊贡献：创新、创造与突破的业绩，每年必须有3件以上（3件以上是初定标准）。<br/>
          （2）业务能力：优于上一等级30%以上的人员（初定标准）。<br/>
          （3）特别要求：自己的责任范围当年度没有出现重大的设备事故、质量事故、安全事故和渎职行为。<br/>
          （4）有人员符合条件的：在同一评定单位原则上不超过15%，特殊情况超过15%的要特别说明。<br/>
          3、标杆人员评定：必须评出至少1名标杆人员，标杆人员原则上为本评定单位综合能力最好的人员，合议确定后在综合能力分值栏录入相应的分
          数（低于90分）在“优级、标杆人员”栏自动显示“标杆”字样。<br/>
          </p></div>`});
    },
    showStandardName(data){
       if (data.standardLevel=="优"){
         data.standardPoint =90;
       }
       else if (data.standardLevel==""){
         data.standardPoint =null;
       }
       console.log(data);
    },
    _updateStandardEmployee(isFinishConfirm){
      if(this.disabledButton){
        return;
      }
      let alertInfo="确定要录入保存？请确认。";
      if (isFinishConfirm==1){
        alertInfo="确定不设定标杆人员？请确认。";
      }
      else if (isFinishConfirm==2){
        alertInfo="确定已设定标杆人员？请确定。";
      }
      layer.confirm(alertInfo, {
          title:'提示',
          btn: ['确定','取消'] //按钮
        }, (index)=>{
          this.isLoading = true;
          let arr = this.tableData.map(data=> {
            data.fdYear = this.year;
            return data;
          });
          updateStandardEmployee(arr,{isFinishConfirm:isFinishConfirm}).then(res=>{
            let result = res.success;
            let saveInfo="【保存录入】";
            if (isFinishConfirm==1){
              saveInfo="【无标杆评定完成】";
            }
            else if (isFinishConfirm==2){
              saveInfo="【有标杆评定完成】";
            }
            layer.msg((saveInfo+`${result ? '成功' : '失败'}`), {icon: result ? 1 : 2,shade:0.3,shadeClose:true});
            this._findMergeListByCompanyGroupId();
            layer.close(index)
          }).catch(err=>{
            layer.msg(`${err.msg}`, {icon: 2,shade:0.3,shadeClose:true});
            layer.close(index)
          })
        });
    },
    _findMergeListByCompanyGroupId(){
      /*先判断有权限才可以查询(modify by huangdh@2019-05-24)*/
      if (this.$store.state.doubleCol.arrAuths.point_ablity_findMergeListByCompanyGroupId)
      {
          findMergeListByCompanyGroupId({CompanyGroupId:this.companyGroupId,Year:this.year,Month:this.month,PositionGradeId:this.positionGradeId,DeptArea:this.deptArea}).then(res=>{
            if(!res.success) return;
            this.tableData = (res.data ? res.data : []).sort((a,b) => a.deptName.localeCompare(b.deptName),'zh-CN')
          }).catch(err=>{
            layer.msg(`${err.msg}`, {icon: 2,shade:0.3,shadeClose:true});
          })
      }
      else {
          layer.msg(`对不起，您没有权限查询本界面.`, {icon: 2,shade:0.3,shadeClose:true});
      }
    },
    _findDetailsListByYearMonth(){
      findDetailsListByYearMonth({companyGroupId:this.companyGroupId,year:this.year,month:this.month,positionGradeId:this.positionGradeId,deptArea:this.deptArea}).then(res=>{
        this.assessorList = res.data || [];
        this.isLoading = false;
      })
    },
    editRemark(item){
        if (item.isFinishConfirm!=null && item.isFinishConfirm>0){
          return;
        }
        var fdId=item.fdId;
        var remark=item.bakRemark||"";
        var index=100;

        Utils.layerBox.layerDialogOpen({
        title:'评定事例依据',
        area:['600px','300px'],
        content: `<div style="padding:10px 20px;margin:0 auto;">
        <textarea id="text_remark" type="text" style="width:100%;height:170px;">`+remark+`</textarea></div>`,
        btn1:function(index){
            remark = $("textarea[id='text_remark']").val();
            updateMeetingRemark({fdId:fdId,remark:remark}).then(res=>{
              let result = res.success;
              layer.msg(`保存${result?'成功':'失败'}！${res.msg||''}`, {icon: result ? 1 : 2,shade:0.3,shadeClose:true});
              item.bak_remark=remark;
            }).catch(err=>{
              layer.msg('保存失败！'+(err.msg||''), {icon: 2,shade:0.3,shadeClose:true});
            });
            layer.close(index);
          }
        });
    },
    initPage(){
      this.pageResize();
      const {companyGroupId,year,month,positionGradeId} = this.$route.params;
      this.companyGroupId = companyGroupId;
      this.year = year;
      this.positionGradeId = positionGradeId;
      this._findDetailsListByYearMonth();
      this._findMergeListByCompanyGroupId();
    },
    pageResize(){
      this.body_height = $(window).height();
    }
  },
  watch:{
    "$route":function(){
      this.initPage();
    }
  },
  created(){
    this.initPage();
    $(window).resize(this.pageResize);
  }
}
</script>
<style scoped>
/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}
.list-year-data {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
.scroll-table-body {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
</style>

