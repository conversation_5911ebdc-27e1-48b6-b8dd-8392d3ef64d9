<template>
  <div class="rank-assessor-container">
    <div class="row" style="line-height:40px;width:1140px">
      <div class="col-xs-12 text-left" >
        <h4 class="col-xs-6" style="padding: 0;">
          {{ this.$route.params.year }}{{ this.month == `6` ? `半` : `` }}年度{{ getCompanyName() }}（{{ getGradeName()
          }}）{{ $route.name }}
        </h4>
        <div class="col-xs-6 text-right" style="padding: 0;">
          <div class="butFlex">
            <el-upload :show-file-list="false" action="" :headers="headers" :file-list="fileList"
              :before-upload="BeforeUpload" :http-request="Upload">
              <button type="button" class="btn btn-primary btn-xs">导入模板</button>
            </el-upload>
            <button type="button" class="btn btn-primary btn-xs" @click="sheetIt">下载模板</button>
            <button class="btn btn-primary btn-xs" @click="showDescription()">评定人说明</button>
            <button class="btn btn-primary btn-xs" :disabled="disabledButton" @click="doStoraging()"
              v-if="$store.state.doubleCol.arrAuths.point_ablity_updateNotCommitSave">保存录入</button>
            <button class="btn btn-primary btn-xs" :disabled="disabledButton" @click="_updateConfirmLeader()"
              v-if="$store.state.doubleCol.arrAuths.point_ablity_updateConfirmLeader">{{ this.gradeRank == 'grade' ?
            '提交OA' : '提交OA' }}</button>
            <button class="btn btn-primary btn-xs"
              @click="$router.push(`/${$route.params.year}/${month}/menu/1`)">返回目录</button>
          </div>
        </div>
      </div>
    </div>
    <div id="table" class="row">
      <div class="col-xs-12" style="width:auto">
        <div class="list-table">
          <div class="scroll-table" style="height:100%;overflow-x:hidden;">
            <table class="scroll-table-header table-bordered">
              <colgroup>
                <col style="width:40px" />
                <col style="width:120px" />
                <col style="width:70px" />
                <col style="width:40px" />
                <col style="width:40px" />
                <col style="width:50px" />
                <col style="width:160px" />
                <col style="width:160px" />

                <col style="width:100px" />
                <col style="width:100px" />
                <col style="width:100px" />
                <col style="width:100px" />
                <col style="width:90px" v-if="month == 12 && positionGradeId == '0006'" />
                <col style="width:90px" v-if="month == 12 && positionGradeId == '0006'" />
                <!-- <col style="width:90px" v-if="month == 6" />
                <col style="width:90px" v-if="month == 6" />
                <col style="width:90px" v-if="month == 6" />
                <col style="width:120px" v-if="month == 6" /> -->
                <col />
              </colgroup>
              <thead>
                <tr>
                  <th rowspan="2">序号</th>
                  <th rowspan="2">厂部</th>
                  <th colspan="6">评定对象</th>
                  <th :colspan="positionGradeId == '0006' ? '6' : '4'" v-if="month == 12">评定人</th>
                  <th rowspan="2" v-if="month == 6">评定人</th>
                  <th rowspan="2" v-if="month == 6">审核人</th>
                  <th rowspan="2" v-if="month == 6">审定人</th>
                  <th rowspan="2" v-if="month == 6">调整说明</th>
                </tr>
                <tr>
                  <th>姓名</th>
                  <th>性别</th>
                  <th>年龄</th>
                  <th>学历</th>
                  <th>岗位</th>
                  <th>职等</th>
                  <th v-if="month == 12">直接领导</th>
                  <th v-if="month == 12">间接领导</th>
                  <th v-if="month == 12">关联业务领导</th>
                  <th v-if="month == 12">关联业务领导</th>
                  <th v-if="month == 12 && positionGradeId == '0006'">关联业务领导</th>
                  <th v-if="month == 12 && positionGradeId == '0006'">关联业务领导</th>
                </tr>
              </thead>
            </table>
            <div class="scroll-table-body"
              :style="{ overflow: 'auto', maxHeight: body_height - 160 + 'px', 'overflow-y': 'scroll', 'overflow-x': 'hidden' }">
              <table style="margin:0;position: relative;font-size:13px;float:left;border:none;"
                class="table table-bordered table-hover table-striped">
                <colgroup>
                  <col style="width:40px" />
                  <col style="width:120px" />
                  <col style="width:70px" />
                  <col style="width:40px" />
                  <col style="width:40px" />
                  <col style="width:50px" />
                  <col style="width:160px" />
                  <col style="width:160px" />


                  <col style="width:100px" />
                  <col style="width:100px" />
                  <col style="width:100px" />
                  <col style="width:100px" />
                  <col style="width:90px" v-if="month == 12 && positionGradeId == '0006'" />
                  <col style="width:90px" v-if="month == 12 && positionGradeId == '0006'" />
                  <!-- <col style="width:90px" v-if="month == 6" />
                  <col style="width:90px" v-if="month == 6" />
                  <col style="width:90px" v-if="month == 6" />
                  <col style="width:120px" v-if="month == 6" /> -->
                </colgroup>
                <tbody>
                  <tr v-for="(data, $index) in tableData" :key="$index">
                    <td>{{ $index + 1 }}</td>
                    <td>{{ data.deptName }}</td>
                    <td>{{ data.userName }}</td>
                    <td>{{ data.gender }}</td>
                    <td>{{ data.age }}</td>
                    <td>{{ data.degree }}</td>
                    <td>{{ data.jobName }}</td>

                    <td>{{ data.gradeName }}</td>
                    <td v-if="month == 12">
                      <input v-if="isPrint" type="text" v-model="data.relationLeader3" class="form-control input-sm" />
                      <span v-else>{{ data.relationLeader3 }}</span>
                    </td>
                    <td v-if="month == 12">
                      <input v-if="isPrint" type="text" v-model="data.relationLeader4" class="form-control input-sm" />
                      <span v-else>{{ data.relationLeader4 }}</span>

                    </td>
                    <td v-if="month == 12">
                      <input v-if="isPrint" type="text" v-model="data.relationLeader1" class="form-control input-sm" />
                      <span v-else>{{ data.relationLeader1 }}</span>

                    </td>
                    <td v-if="month == 12">
                      <input v-if="isPrint" type="text" v-model="data.relationLeader2" class="form-control input-sm" />
                      <span v-else>{{ data.relationLeader2 }}</span>

                    </td>
                    <td v-if="month == 12 && positionGradeId == '0006'">
                      <input v-if="isPrint" type="text" v-model="data.relationLeader5" class="form-control input-sm" />
                      <span v-else>{{ data.relationLeader5 }}</span>

                    </td>
                    <td v-if="month == 12 && positionGradeId == '0006'">
                      <input v-if="isPrint" type="text" v-model="data.relationLeader6" class="form-control input-sm" />
                      <span v-else>{{ data.relationLeader6 }}</span>

                    </td>

                    <td v-if="month == 6">
                      <input v-if="isPrint" type="text" v-model="data.leaderName" class="form-control input-sm" />
                      <span v-else>{{ data.leaderName }}</span>

                    </td>
                    <td v-if="month == 6">
                      <input v-if="isPrint" type="text" v-model="data.checkerName" class="form-control input-sm" />
                      <span v-else>{{ data.checkerName }}</span>

                    </td>
                    <td v-if="month == 6">
                      <input v-if="isPrint" type="text" v-model="data.approveName" class="form-control input-sm" />
                      <span v-else>{{ data.approveName }}</span>

                    </td>
                    <td v-if="month == 6">
                      <input v-if="isPrint" type="text" v-model="data.remark" class="form-control input-sm" />
                      <span v-else>{{ data.remark }}</span>

                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import GradeEvaluationFlowQueryDialog from './GradeEvaluationFlowQueryDialog';
import { findMergeListByCompanyGroupId, updateConfirmLeader, updateDirectLeaderByHalfYear, updateNotCommitSave, importAssessor } from '@/api'
import { getCookie, TokenKey } from '@/utils/Cookie'

export default {
  data() {
    return {
      headers: {
        [TokenKey]: getCookie(TokenKey)
      },
      isLoading: true,
      body_height: 0,
      originData: '',
      tableData: [],
      year: "",
      month: "",
      companyGroupId: "",
      positionGradeId: "",
      deptArea: Utils.getQueryParams('deptArea'),
      newFile: new FormData(),
      isPrint: true
    }
  },
  watch: {
    originData(val) {
      let data = JSON.parse(val);
      this.tableData = data.sort((a, b) => {
        return parseInt(b.deptId) - parseInt(a.deptId) || b.jobName.localeCompare(a.jobName, 'zh');
      });
    },
    "$route": function () {
      this.initPage();
    }
  },
  computed: {
    disabledButton() {  /*提交OA或职级评定发起 */
      if (this.isLoading || this.tableData.length == 0) {
        return true;
      }
      else if (this.tableData && this.tableData.length != 0) {
        return this.tableData[0].confirmLeaderStatus > 10 ? true : false;
      }
      return true;
    },
    gradeRank() {
      return this.$route.meta.gradeRank;
    }
  },
  methods: {

    BeforeUpload(file) {
      // const fileSuffix = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()
      // const whiteList = ['csv']
      if (file) {
        this.newFile.append('file', file)
      } else {
        return false
      }
    },
    Upload() {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      this.newFile.append('companyGroupId', this.companyGroupId)
      this.newFile.append('year', this.year)
      this.newFile.append('month', this.month)
      this.newFile.append('userType', '管理人员')
      const newData = this.newFile
      let params = newData
      importAssessor(params).then(res => {
        loading.close();
        layer.msg(res.data, { icon: 1, shade: 0.3, shadeClose: true });
        this._findMergeListByCompanyGroupId();
      }).catch(err => {
        loading.close();
        layer.msg(`${err.msg}`, { icon: 2, shade: 0.3, shadeClose: true });
      })
    },
    sheetIt() {
      this.isPrint = false
      setTimeout(() => {
        this.exportExcelOne.exportExcel(`【${this.getCompanyName()}】管理人员【评定人确认】表.xlsx`, '#table')
        this.isPrint = true
      })
    },
    getCompanyName() {
      let companyName = Utils.getCompanyNameByGroupId(this.companyGroupId);
      if (this.positionGradeId == "0006" || this.positionGradeId == "0007" || this.positionGradeId == "0032" || this.positionGradeId == "0033") {
        companyName = "全集团";
      }
      return companyName;
    },
    getGradeName() {
      let name = Utils.getGradeLevlByGradeId(this.positionGradeId);
      if (this.deptArea == "A") {
        name = name + "-生产一线"
      }
      else if (this.deptArea == "B") {
        name = name + "-生产辅助"
      }
      else if (this.deptArea == "C") {
        name = name + "-生产支持"
      }
      return name;
    },
    _updateConfirmLeader() {
      if (this.disabledButton) {
        return;
      }
      let that = this;
      layer.confirm(`请确定${this.gradeRank == 'grade' ? '职级' : '级别'}评定人是否已确定完成?`, {
        title: '提示',
        btn: ['确定', '取消'] //按钮
      }, (index) => {
        this.isLoading = true;
        let func = null;
        if (that.month == 6) {
          //func = updateDirectLeaderByHalfYear;
          func = updateConfirmLeader;   /*改为与年度评定人一样，先确认评定人*/
        } else {
          func = updateConfirmLeader;
        }
        if (!func) return;
        that.tableData.forEach(item => {
          item.fdYear = that.year;
        })
        func(that.tableData, { positionGradeId: that.positionGradeId }).then(res => {
          let result = res.success;
          layer.msg(`提交OA${result ? '成功' : '失败'}`, { icon: result ? 1 : 2, shade: 0.3, shadeClose: true });
          this._findMergeListByCompanyGroupId();
          layer.close(index)
        }).catch(err => {
          layer.msg(`${err.msg}`, { icon: 2, shade: 0.3, shadeClose: true });
          layer.close(index)
        })
      });
    },
    //暂存
    doStoraging() {
      if (this.disabledButton) {
        return;
      }
      updateNotCommitSave(this.tableData, { positionGradeId: this.positionGradeId }).then(res => {
        let result = res.success;
        layer.msg(`评价人暂存${result ? '成功' : '失败'}`, { icon: result ? 1 : 2, shade: 0.3, shadeClose: true });
        this._findMergeListByCompanyGroupId();
      }).catch(err => {
        layer.msg(`${err.msg}`, { icon: 2, shade: 0.3, shadeClose: true });
      })
      // this.tableData = JSON.parse(this.originData)
    },
    _findMergeListByCompanyGroupId() {
      /*先判断有权限才可以查询(modify by huangdh@2019-05-24)*/
      if (this.$store.state.doubleCol.arrAuths.point_ablity_findMergeListByCompanyGroupId) {
        findMergeListByCompanyGroupId({ CompanyGroupId: this.companyGroupId, Year: this.year, Month: this.month, PositionGradeId: this.positionGradeId, DeptArea: this.deptArea }).then(res => {
          this.isLoading = false;
          if (!res.success) return;
          this.originData = JSON.stringify(res.data ? res.data : []);
        }).catch(err => {
          layer.msg(`${err.msg}`, { icon: 2, shade: 0.3, shadeClose: true });
        })
      }
      else {
        layer.msg(`对不起，您没有权限查询本界面.`, { icon: 2, shade: 0.3, shadeClose: true });
      }

    },
    showDialog(leader) {
      let layerDialog = Utils.layerBox.layerDialogOpen({
        title: "管理人员综合能力职级评定流程节点查询",
        btn: [],
        maxmin: false, //开启最大化最小化按钮
        area: ["760px", "500px"],
        btn1: (index, layero) => {
        },
        btn2: (index, layero) => {
        }
      });
      this.dialogComponent = Utils.layerBox.layerLoadVueComponent({
        layer: layerDialog,
        component: GradeEvaluationFlowQueryDialog,
        methods: {
          doConfirm: (params) => {
            layer.close(layerDialog);
          },
          doClose: () => {
            layer.close(layerDialog); //关闭对话框
          }
        },
        props: {
          confirmLeaderId: this.tableData[0].confirmLeaderId
        }
      });
    },
    showDescription() {
      Utils.layerBox.layerDialogOpen({
        title: '评定人说明',
        area: ['670px', this.gradeRank == 'grade' ? '350px' : '330px'],
        btn: ['关闭'],
        content: `<div style="padding:10px 20px;margin:0 auto;">
          ${this.gradeRank == 'grade' ? `
          <p style="line-height:24px;">
            1、由人资部直接根据组织架构确定评定人、审核人、审定人，评定人必须为直接领导，直接领导审核后，按照组织架构关系逐级往上提交审核，若是评定人/审核人/审定人是总经理助理级以上人员时，可终止评定，不需要往上提交审核审定，以便减轻领导工作量，最终会组织各领导合议。<br/>
            2、系统会自动根据组织架构系统的汇报关系生成评定人（直接领导），需要调整的，可人工自行修改，若是调整后的上下级关系与组织架构不符的，需写出调整说明。<br/>
            3、若评定人、审核人、审定人入职不满3个月，原则上不能评定，人资部需组织相关人员合议调整。<br/>
            4、评定人确定后，点击“保存录入”，数据自动保存。<br/>
            5、在评定人确定后，点击“提交OA键”，提交至操作人员个人工作台，最终由各公司人资体系领导审批。<br/>
          </p>` :
            `<p>
              1、评定人资格：<br/>
              （1）专业领导：一定是对评定对象了解的领导，优先考虑业务技术层面的领导；<br/>
              （2）关联业务领导：一定是对评定对象了解的领导；<br/>
              （3）直接领导：原则上是部门经理；<br/>
              （4）间接领导：原则上是体系领导。<br/>
              2、评定人人数：原则上3人，最多4人，不足3人的可以为2人，极个别情况下也可以为1人。<br/>
              3、评定人确定办法：由人资部组织体系领导等相关人员确定。<br/>
              4、OA审核审批流程：发起人OA工作台发起-->人资部门主管审核-->核签人审核-->公司总经理审批（集团为行政人资副总裁、赣纸为副总裁）。
              <br/>
            </p>`}
          </div>`});
    },
    initPage() {
      this.pageResize();
      const { companyGroupId, year, positionGradeId } = this.$route.params;
      this.companyGroupId = companyGroupId;
      this.year = year;
      this.month = this.$route.meta.month;
      this.positionGradeId = positionGradeId;
      // this.deptArea = "";
      document.getElementsByClassName('el-upload__input')[0].style = 'display:none'
      this._findMergeListByCompanyGroupId();
    },
    pageResize() {
      this.body_height = $(window).height();
    }
  },
  mounted() {
    this.initPage();
    $(window).resize(this.pageResize);
  }
}
</script>

<style scoped>
.rank-setting-container .input-sm {
  height: 24px !important;
}

.butFlex {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: flex-end;
}

.butFlex>button {
  margin-left: 10px;
}
/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}
.list-year-data {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
.scroll-table-body {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
.scroll-table-header{
  width: auto !important;
}
.table{
  width: auto !important;
  max-width: auto !important;
}
.scroll-table{
  width: auto !important;
}
</style>