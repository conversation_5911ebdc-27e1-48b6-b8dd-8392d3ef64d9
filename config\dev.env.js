'use strict'
const merge = require('webpack-merge')
const prodEnv = require('./prod.env')

// 单点登陆测试环境
module.exports = merge(prodEnv, {
  NODE_ENV: '"development"',
  BASE_API:'"http://hwtest8.hwagain.com:60000/ablity"',
  // BASE_API:'"http://huangdh.hwagain.com:8080/ablity"',  
  STRUCTURE_BASE_URL:'"http://console-sit.hwagain.com:60000/hwagain"',
  LOGIN_PATH:'"http://console-sit.hwagain.com:10000/#/login?url="',
  VUE_APP_TOKEN_KEY: "'hwagain_sso_token_test'",
  PROJECT_CODE:'"mpcaa"'
  // MENU_PATH:'"http://hwenter.hwagain.com/enter/index.html?systemCode=audit"'
})

// 门户登陆测试环境
// module.exports = merge(prodEnv, {
//   NODE_ENV: '"development"',
//   BASE_API:'"http://hwtest8.hwagain.com:60000/ablity"',
//   // BASE_API: '"http://hwtest4.hwagain.com:10008/api/ablity"',
//   // BASE_API:'"http://java-sit.hwagain.com:60000/ablity"',
//   // BASE_API:'"http://huangdh.hwagain.com:8080/ablity"',  
//   STRUCTURE_BASE_URL:'"http://console-sit.hwagain.com:60000/hwagain"',
//   LOGIN_PATH: "'http://hwtest8.hwagain.com:86/#/login?url='",
//   VUE_APP_TOKEN_KEY: "'hwagain_sso_token_test'",
//   PROJECT_CODE:'"mpcaa"'
//   // MENU_PATH:'"http://hwenter.hwagain.com/enter/index.html?systemCode=audit"'
// })

// 门户登陆正式环境
// module.exports = {
//   NODE_ENV: '"development"',
//   BASE_API:'"http://platform.hwagain.com/api/ablity"',
//   LOGIN_PATH:'"http://portal.hwagain.com/#/login?url="',
//   PROJECT_CODE:'"mpcaa"',
//   VUE_APP_TOKEN_KEY: "'hwagain_sso_token'"
// }
