<template>
  <div class="staff-people-detail-container">
      
        <div class="col-xs-12" style="margin-left:10px;width:630px;">
          <div>
            1、每个职等的<strong>优级</strong>不能超过该职等人数的15%，可以为0人。<br>
            2、每个职等必须平均分配。
          </div>
            <div class="list-year-head-table" >
              <table class="list-year-head table table-bordered" :style="`margin-bottom:0;width:580px;margin:0 auto 0 0;`">
                <colgroup>
                    <col width='50'/>
                    <col width='120'/>
                    <col width='80'/>
                    <col width='80'/>
                    <col width='120'/>
                </colgroup>
                <thead>
                  <tr style="background:#b7d8dc;">
                    <th style="text-align:center;">序号</th>
                    <th style="text-align:center;">职等</th>
                    <th style="text-align:center;">级别</th> 
                    <th style="text-align:center;">占比</th>
                    <th style="text-align:center;">职级</th>
                  </tr>
                </thead>
            </table>
          </div>
          <div class="list-year-data"  :style="{maxHeight:'400px','overflow-y':'scroll','overflow-x':'hidden',width:'597px','border-bottom':'1px solid #ccc'}">
              <table style="margin-bottom:0;text-align:center;" class="table table-bordered">
                <colgroup>
                    <col width='50'/>
                    <col width='120'/>
                    <col width='80'/>
                    <col width='80'/>
                    <col width='120'/>
                </colgroup>
                <tbody>
                    <tr v-for="(data,$index) in tableData" :key="$index">
                      <td v-if="data.itemIndex==1" :rowspan="data.itemCount" style="vertical-align:middle;">{{data.gradeIndex}}</td>
                      <td v-if="data.itemIndex==1" :rowspan="data.itemCount" style="vertical-align:middle;">{{data.positionGradeName}}</td>
                      <td>{{data.level}}</td>
                      <td>{{data.level=="优"? "--":((data.rate||'')+'%')}}</td>
                      <td style="text-align:left;">{{data.levelGrade||''}}</td>
                    </tr>
                </tbody>
            </table>
          </div>
        </div>
      </div>

</template>

<script>
import { findListByCompanyId } from '@/api'
export default {
  props: {
    params: {
      type: Object,
      required: true
    }
  },
  data(){
    return {
      body_height:0,
      tableData:[],
      remarks:[],
    }
  },
  methods:{
    _findListByCompanyId(){ 
        findListByCompanyId({companyGroupId:this.params.companyGroupId,year:this.params.year,month:this.params.month}).then(res=>{
            this.tableData = res.data || [];
        })
    },
    initPage(){
      this.body_height = $(window).height();
      this._findListByCompanyId();
    }
  },
  watch:{
    "$route":function(){
      this.initPage();
    }
  },
  mounted(){
    this.initPage();
    $(window).resize(()=>{
      this.body_height = $(window).height();
    });
  }
}
</script>

<style>

</style>
