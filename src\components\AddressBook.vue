<template>
    <div class='ab-container'>
        <div class='ab-left'>
            <div class=' ab-header'>
                <h3 class='ab-name'><i class='glyphicon glyphicon-book'></i>华劲通讯录</h3>
                <i class='glyphicon glyphicon-search search-icon'></i>
                <input class='layui-input ab-search-input' type='text' v-model="keyWord" @keydown="searchKeyEnter($event)" placeholder='请输入关键字搜索部门/员工' autocomplete='off'>
            </div>
            <div class=' ab-left-menu-items'>
                <div class='left-nav'>
                    <div class='ab-left-menu-item structure-tree'>
                        <span><i class='glyphicon glyphicon-object-align-vertical'></i>组织架构</span>
                        <ul class='ztree beauty-scroll-bar' id='address_book_tree'></ul>
                    </div>
                    <!-- <div class='ab-left-menu-item common-address'>
                        <span><i class='glyphicon glyphicon-star'></i>常用人员</span>
                    </div>
                    <div class='ab-left-menu-item recent-contacts'>
                        <span><i class='glyphicon glyphicon-dashboard'></i>最近联系</span>
                    </div> -->
                </div>
                <div class='ab-show-content-wrap'>
                    <ul id='ab-show-content-box' class='beauty-scroll-bar'>
                        <li :class="{'grey-bg':$index%2==0}" v-for="(item,$index) in showList" :key="$index">
                            <input @click="checkboxChange2(item,$event)" :checked="checkSelectIsExist(item)" type='checkbox' :value='item.id'>
                            <span :class='{"depart-name":item.type=="dept","user-name":item.type=="user"}'>
                                <i v-if="item.type=='dept'" class="ab-avatar glyphicon glyphicon-folder-close blue-bg"></i>
                                <i v-else-if="item.sex == 'M'" class="ab-avatar glyphicon glyphicon-user blue-bg"></i>
                                <i v-else-if="item.sex == 'F'" class="ab-avatar glyphicon glyphicon-user pink-bg"></i>
                                <i v-else class="ab-avatar"></i>
                                {{item.name}}
                            </span>
                            <span v-if="!!item.job" class='job-name' :title='item.job'>{{item.job}}</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- <div class='ab-right'>
            <div class='ab-btns-wrap'>
                <div class='ab-opt-btns'>
                    <button class="btn btn-default input-sm" type="submit" @click="addBtnHandle">全部添加</button>
                    <button class="btn btn-default input-sm" type="submit" @click="cleanBtnHandle">全部删除</button>
                </div>
            </div>
            <div class='ab-selected-wrap'>
                <h3 class='ab-selected-count'>已选 <b>{{selectedList.length}}</b></h3>
                <ul id='ab-selected-items' class='ab-selected-items beauty-scroll-bar'>
                    <li class='ab-selected-item' :class="{'grey-bg':$index%2==0}" v-for="(item,$index) in selectedList" :key="$index">
                        <i v-if="item.type=='dept'" class="ab-avatar glyphicon glyphicon-folder-close blue-bg"></i>
                        <i v-else-if="item.sex === 'M'" class="ab-avatar glyphicon glyphicon-user blue-bg"></i>
                        <i v-else-if="item.sex === 'F'" class="ab-avatar glyphicon glyphicon-user pink-bg"></i>
                        <i v-else class="ab-avatar"></i>
                        <span>{{item.name}}</span>
                        <i class='glyphicon glyphicon-remove ab-delete-icon' @click="deleteHandle(item.id)"></i>
                    </li>
                </ul>
            </div>
        </div> -->
    </div>
</template>

<script>
import 'static/zTree_v3/js/jquery-1.4.4.min'
import 'static/zTree_v3/js/jquery.ztree.core'
import 'static/zTree_v3/css/metroStyle/metroStyle.css'
import qs from 'qs'
import axios from 'axios'
const fetch = axios.create({
    timeout: 5000,
    withCredentials:true
});
export default {
    props:['params'],
    data(){
        const that = this;
        const url = process.env.STRUCTURE_BASE_URL+'/sys/org';
        return {
            ids:'',
            names:'',
            showList:[],
            selectedList:[],
            preffix_url:url,
            keyWord:'',
            setting:{
                async: {
                    enable: true,
                    type:'get',
                    dataType:'jsonp',
                    url:`${url}/getPersonsByParentId`,
                    autoParam:["id=parentId"],
                    otherParam:{"selectType":3},
                    dataFilter(treeId, parentNode, childNodes) {
                        var data = JSON.parse(childNodes.data||'[]')||[];
                        if (!data) return null;
                        var departments = [];
                        for (var i=0, l=data.length; i<l; i++) {
                            if(data[i].type == "user"){
                                break;
                            }
                            data[i].name = data[i].name||"";
                            data[i].id = data[i].code;
                            data[i].pid = data[i].pid || '';
                            data[i].pId = data[i].pid.replace(/\d_/,'') || 0;
                            data[i].isParent = true;
                            departments.push(data[i]);
                        }
                        return departments;
                    }
                },
                view: {
                    expandSpeed:"fast",
                    selectedMulti: false
                },
                data: {
                    simpleData: {
                        enable: true
                    }
                },
                callback:{
                    onClick(event,treeId,treeNode) {
                        that.showList = [];
                        if(treeNode.level == 0){
                            return;
                        }
                        that.loadNodes({parentId:treeNode.code||'',"selectType":3},(data)=> {
                            data.forEach((item,index)=>{
                                if(!that.params.type){
                                    that.showList.push(item)
                                }else {
                                    if(that.params.type == 1 && item.type =='dept'){
                                        that.showList.push(item)
                                    }
                                    else if(that.params.type == 2 && item.type =='user'){
                                        that.showList.push(item)
                                    }
                                    else if(that.params.type == 3){
                                        that.showList.push(item)
                                    }
                                }
                            });
                        })
                    }
                }
            }
        }
    },
    methods:{
        checkboxChange(item,e){
            if(!this.checkSelectIsExist(item)){
                    this.selectedList.push(item);
            }else{
                this.deleteHandle(item.id);
            }
        },
        checkboxChange2(item,e){
            this.selectedList = []
            this.selectedList.push(item);
            // if(!this.checkSelectIsExist(item)){
            //         this.selectedList.push(item);
            // }else{
            //     this.deleteHandle(item.id);
            // }
        },
        loadTree(zNodes) {
            jQuery.fn.zTree.init($("#address_book_tree"), this.setting, zNodes);
        },
        loadNodes(params,cb) {
            fetch.get(`${this.preffix_url}/getPersonsByParentId`,{params}).then(res=>{
                cb&&cb(JSON.parse(res.data.data)||[]);
            })
        },
        searchHandle(){
          var params = {
              selectArea:'n',
              keyWord:this.keyWord,
              selectType: 3
          }
          this.findByParams(params,(data)=> {
              this.showList = data;
          })
        },
        deleteHandle(id) {
            var selectedList = [];
            this.selectedList.forEach((item,index)=> {
                if(item.id != id){
                    selectedList.push(item);
                }
            });
            this.selectedList = selectedList;
        },
        bindNodesDataHandle(data) {
            var arr = [];
            $.each(data,(index,item)=> {
                var obj = {};
                obj.id = item.code;
                item.pid = item.pid || '';
                obj.pId = item.pid.replace(/\d_/,'') || 0;
                obj.name = item.name;
                obj.isParent = true;
                arr.push(obj);
            })
            this.loadTree(arr);
        },
        findByParams(params,cb) {
            fetch.get(`${this.preffix_url}/getPersonsByKeyWord`,{params}).then(res=>{
                let data = JSON.parse(res.data.data||'[]')||[];
                cb&&cb(data);
            });
        },
        findSelectedPersionByIds(ids){
             fetch.post(`${this.preffix_url}/getPersonAndDeptByIds`,ids).then(res=>{
                let data = res.data.data||[];
                this.selectedList = data;
            });
        },
        checkSelectIsExist(data) {
            for(var i = 0;i<this.selectedList.length;i++){
                var item = this.selectedList[i];
                if(data.id == item.id){
                    return true;
                }
            }
            return false;
        },
        addBtnHandle() {
            this.showList.forEach((item)=>{
                if(this.checkSelectIsExist(item)) return;
                this.selectedList.push(item);
            })
        },
        cleanBtnHandle() {
            this.selectedList = [];
        },
        searchKeyEnter(e){
            if(e.keyCode === 13){
                this.searchHandle();
            }
        },
        doSubmit(){
            let ids = [];
            let names = [];
            this.selectedList.forEach(r=>{
                ids = ids.concat(r.id);
                names = names.concat(r.name);
            });
            this.params.getIds&&this.params.getIds(ids.join(";"))
            this.params.getNames&&this.params.getNames(names.join(";"))
            this.$emit('submit-click',this.selectedList)
        },
        doCancel(){
            this.$emit('cancel-click')
        }
    },
    mounted(){
        this.params.selectedIds&&this.params.selectedIds.length>0&&this.findSelectedPersionByIds(this.params.selectedIds);
        this.loadNodes({parentId:'',selectType:1},this.bindNodesDataHandle);
    }
};
</script>

<style lang="scss">
.ab-container {
    width:860px;
    height:440px;
    margin: 0;
    padding: 10px 15px;
    box-sizing: border-box;
    position: relative;
    .grey-bg {
        background: #fafafa;
    }
    i.blue-bg {
        color: #169ef4;
    }
    i.pink-bg {
        color: #fc81ae;
    }
    .glyphicon {
        color:#929292;
        margin-right:5px;
    }
    .ab-left {
        display: inline-block;
        width:100%;
        float: left;
        .ab-header {
            width: 100%;
            height: 36px;
            padding: 5px;
            position: relative;
            background: #eee;
            .ab-name {
                margin: 0;
                font-size: 16px;
                width:54%;
                display: inline-block;
            }
            .ab-search-input {
                width: 45%;
                height: 26px;
                padding: 5px 10px;
                font-size: 12px;
                line-height: 1.5;
                border-radius: 3px;
                color:#555;
                background: #fff;
                border:1px solid #ccc;
                box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
            }
            .search-icon {
                position: absolute;
                top: 11px;
                right: 15px;
            }
        }
        .ab-left-menu-items {
            width: 100%;
            height: 440px;
            padding: 15px 0;
            box-sizing: border-box;
            .left-nav {
                width:360px;
                height: 100%;
                display: inline-block;
                float: left;
                .ab-left-menu-item {
                    margin-bottom: 8px;
                    &:not(:first-child) {
                        line-height: 30px;
                        height: 30px;
                        border-bottom: 1px solid #eee;
                    }
                    &> span {
                        padding-left: 5px;
                        &> i {
                            margin-right: 5px;
                            color:#929292;
                            font-size: 14px;
                        }
                    }
                    &:first-child {
                        &> span {
                            color: #169ef4;
                            &> i{
                                color:#169ef4;
                            }
                        }
                    }
                }
                .structure-tree #address_book_tree {
                    height: 388px;
                    border: 1px solid #c1c1c1;
                    margin: 5px 0;
                    overflow: auto;
                }
                .ztree i {
                    color: #61b5ef;
                }
            }
            
            .ab-show-content-wrap {
                padding-left: 15px;
                box-sizing: border-box;
                width:350px;
                height:410px;
                display: inline-block;
                #ab-show-content-box {
                    height: 100%;
                    overflow-x: hidden;
                    overflow-y: auto;
                    margin-bottom: 0;
                    padding: 0;
                    &> li {
                        height: 30px;
                        line-height: 30px;
                        &> input[type='checkbox'] {
                            margin: 0 10px;
                            float: left;
                            position: relative;
                            top: 10px;
                        }
                        &> span.depart-name {
                            width: 210px;
                            display: inline-block;
                        }
                        &> span.depart-name > i {
                            padding-right: 10px;
                        }
                        &> span.user-name {
                            width: 95px;
                            display: inline-block;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            height: 100%;
                        }
                        &> span.user-name > i {
                            padding-right: 10px;
                        }
                        &> span.job-name {
                            width: 150px;
                            display: inline-block;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            height: 100%;
                        }
                    }
                }
            }
        }
    }
    .ab-right {
        display: inline-block;
        width:360px;
        height:450px;
        .ab-btns-wrap {
            width:100px;
            float: left;
            display: inline-block;
            .ab-opt-btns {
                margin: 180px 0;
                width: 100%;
                display: inline-block;
                & > button {
                    display: block;
                    margin: 10px auto;
                }
            }
        }
        .ab-selected-wrap {
            width:260px;
            height: 100%;
            display: inline-block;
            .ab-selected-count {
                height: 36px;
                line-height: 36px;
                background: #eee;
                padding: 0 10px;
                box-sizing: border-box;
                font-size: 14px;
                margin:0;
            }
            .ab-selected-items {
                padding: 15px 0;
                box-sizing: border-box;
                height: 425px;
                overflow: auto;
            }
            .ab-selected-items .ab-selected-item {
                height: 30px;
                line-height: 30px;
                position: relative;
            }
            .ab-selected-items .ab-selected-item > i.ab-avatar {
                margin: 0 10px;
            }
            .ab-selected-items .ab-selected-item > i.ab-delete-icon {
                position: absolute;
                right: 10px;
                cursor: pointer;
                color:#929292;
                top: 8px;
            }
        }
    }
}
</style>
