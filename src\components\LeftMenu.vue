<template>
    <div class="app-left-menu-wrap">
        <ul class="menu-items">
            <li v-for="(menu,i) in menus" :key="i">
                <p @click="menu.children?showChild($event):pageTo($event,menu.path,true)">
                    <i class="glyphicon glyphicon-list"></i>
                    <i class="glyphicon glyphicon-chevron-right"></i>
                    <span>{{menu.name}}</span>
                </p>
                <ul class="menu-item-child" v-if="menu.children">
                    <li @click="pageTo($event,child.path)" v-for="(child,c_idx) in menu.children" :key="c_idx">{{child.name}}</li>
                </ul>
            </li>
        </ul>
    </div>
</template>

<script>
    export default {
        data(){
            let menus = this.$router.options.routes[0].children || [];
            console.log();
            return {
                menus,
                routerTarget:null,
            }
        },
        methods: {
            showChild(e,isFirstLevel) {
                $(".app-left-menu-wrap .menu-item-child").slideUp();
                if($(e.currentTarget).children("i:not('.glyphicon-list')").hasClass('glyphicon-chevron-down')){
                    $(e.currentTarget).children("i:not('.glyphicon-list')").removeClass('glyphicon-chevron-down').addClass('glyphicon-chevron-right');
                }else{
                    $(".app-left-menu-wrap i.glyphicon-chevron-down").removeClass('glyphicon-chevron-down').addClass('glyphicon-chevron-right');
                    $(e.currentTarget).children("i:not('.glyphicon-list')").removeClass('glyphicon-chevron-right').addClass('glyphicon-chevron-down');
                }
                // $(e.currentTarget).parent().siblings().children('p').removeClass('active');
                this.routerTarget = e.currentTarget;
                if(isFirstLevel){
                    $(e.currentTarget).addClass('active');
                }
                $(e.currentTarget).next("ul").stop(true).slideToggle();
            },
            pageTo(e, url, isFirstLevel) {
                if(isFirstLevel){
                    this.showChild(e,true)
                }
                $(".app-left-menu-wrap .menu-item-child li").removeClass('active');
                $(e.currentTarget).addClass('active'); //增加选中样式
                this.$parent.leftMenuVisible(false); //隐藏菜单
                e.currentTarget.tagName === 'P'&&$(e.currentTarget).parent().siblings().children('p').removeClass('active');
                e.currentTarget.tagName === 'LI'&&$(e.currentTarget).parent().parent().siblings().children('p').removeClass('active');
                url && this.$router.push(url); //跳转路由
                // if(isFirstLevel){
                //     $(e.currentTarget).addClass('active');
                // }
            }
        }
    }
</script>

<style lang="scss" scoped>
    .app-left-menu-wrap {
        position: fixed;
        width: 200px;
        height: 100%;
        overflow-x: hidden;
        overflow-y: auto;
        left: -200px;
        border-right: 1px solid #333;
        z-index: 1001;
        background: linear-gradient(to top, #42517d, #425068, #42517d);
        filter: progid:DXImageTransform.Microsoft.gradient(startcolorstr=#42517d, endcolorstr=#425068, gradientType=0);
        .menu-items {
            margin: 0;
            padding: 0;
            &>li {
                padding: 0;
                text-align: left;
                color: #fff;
                font-size: 14px;
                position: relative;
                cursor: pointer;
                p {
                    padding: 12px 20px;
                    display: inline-block;
                    position: relative;
                    width: 100%;
                    margin: 0;
                    i {
                        position: relative;
                        top: 2px;
                        // transition: .3s all;
                    }
                    i.glyphicon-chevron-right,
                    i.glyphicon-chevron-down {
                        position: absolute;
                        right: 10px;
                        top: 14px;
                    }
                    &:hover {
                        background: #202631;
                        color: #0099ff;
                    }
                    &.active {
                        background: #354162;
                        color: #0099ff;
                    }
                }
                .menu-item-child {
                    display: none;
                    list-style: none;
                    padding-left: 0px;
                    background: #354162;
                    li {
                        padding: 10px 20px 10px 40px;
                        &:hover {
                            background: #202631;
                            color: #0099ff;
                        }
                        &.active {
                            background: #354162;
                            color: #0099ff;
                        }
                    }
                }
            }
        }
    }
</style>
