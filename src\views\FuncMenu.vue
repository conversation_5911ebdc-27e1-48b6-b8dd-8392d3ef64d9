<template>
  <div class="function-menu">
    <div class="panel-body-wrap">
      <div class="left-level-menus">
        <h3>目录</h3>
        <template v-if="mIndex == 1" v-for="(item, index) in navList">
          <div @click="getLv1(index)" class="list1">
            <i class="iconfont icon-jiantouyou"></i>
            {{ item.title }}
          </div>
          <div
            :class="gLv1 == index ? 'closer isOpen' : 'closer'"
            v-if="item.lv1"
          >
            <div
              v-if="item.lv1"
              v-for="(item2, index2) in item.lv1"
              :key="index2"
              class="bt1"
            >
              <div
              v-if="(month == 6 && !item2.year) || (month == 12 && item2.year != 6) || (month == 6 && item2.year == 6)"
                @click="getLv2(item2, index2)"
                style="padding: 4px 0 4px 30px"
              >
                <i v-if="item2.lv2" class="iconfont icon-jiantouyou"></i
                >{{ item2.title }}
              </div>
              <div
                :class="gLv2 == index2 ? 'closer isOpen' : 'closer'"
                v-if="item.lv1"
              >
                <div
                  v-if="item2.lv2"
                  v-for="(item3, index3) in item2.lv2"
                  :key="index3"
                  class="bt2"
                >
                  <div @click="goPage(item3)" style="padding: 4px 0 4px 50px">
                    {{ item3.title }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
        <ul></ul>
      </div>
      <div class="other-level-menus">
        <h3></h3>
        <ul></ul>
        <template v-if="nav2Show">
          <div v-for="item in navList2">
            <div class="nav2List" v-if="month == 6 && item.url2">
              <div @click="goPage2(navType, item)">
                {{ navType }}{{ item.title }}
              </div>
            </div>
            <div class="nav2List" v-if="month == 12">
              <div @click="goPage2(navType, item)">
                {{ navType }}{{ item.title }}
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import {
  findAllCompanyAndPositionGrade,
  findSchoolLevel,
  getAuthGroup,
} from "@/api";
import { getCookie, FromUrlKey, UserKey } from "@/utils/Cookie";
import { list, list2 } from "@/utils/navList";
export default {
  data() {
    return {
      loginUser: {},
      companyData: [],
      otherMenuMap: [],
      schoolLevel: [],
      navList: list,
      navList2: list2,
      navType: "",
      managerAuth: false,
      traineeAuth: false,
      mIndex: 1,
      gLv1: null,
      gLv2: null,
      nav2Show: false,
      year: "",
      month: "",
    };
  },
  watch: {
    companyData(val) {
      //console.log("test mIndex:" + this.mIndex);
      this.initMenu();
    },
  },
  methods: {
    getLv1(index) {
      this.nav2Show = false;
      if (this.gLv1 == index) {
        this.gLv1 = null;
        this.gLv2 = null;
        return;
      }
      this.gLv1 = index;
    },
    getLv2(item, index) {
      // console.log(item);
      if (item.title != "管理人员评定说明") {
        let arr = ["覃丽", "武鹏飞", "卢业辉"];
        if (!arr.includes(this.loginUser.name)) {
          this.$message("您没有权限访问该功能");
          return;
        }
      }
      this.nav2Show = false;
      if (this.gLv2 == index) {
        this.gLv2 = null;
        return;
      }
      if (!item.lv2 && item.url) {
        this.$router.push(item.url);
        return;
      }
      this.gLv2 = index;
    },
    goPage(item) {
      this.nav2Show = false;
      if (item.url) {
        if (item.url == "职等") {
          $(".other-level-menus>ul>li").remove();
          this.nav2Show = true;
          this.navType = item.title;
        } else {
          this.$router.push(item.url);
        }
      }
    },
    goPage2(type, item) {
      let types = {
        总经理职等: "0006",
        副总经理职等: "0007",
        总经理助理职等: "0033",
        高级经理职等: "0032",
      };
      this.$router.push(
        `../1/${types[type]}${this.month == 12 ? item.url : item.url2}`
      );
    },
    initMenu() {
      let that = this;
      that.otherMenuMap = [];
      let currentMenuId = [];
      /**
       * 左边菜单方法开始
       */
      var menuSlideBind = function () {
        //当最后一层的时且没有url则调用接口查询右边菜单
        if ($(this).children("a").attr("data-type") === "url") {
          // let arr = ["覃丽", "卢业辉"];
          // if (arr.includes(that.loginUser.name)) {
          //   that.$message("您没有权限访问该功能");
          //   console.log(123132)
          //   return;
          // }
          let url = $(this).children("a").attr("data-url");
          // console.log(url.substr(url.indexOf("#") + 1))
          that.$router.push(url.substr(url.indexOf("#") + 1));
          return;
        }
        if ($(this).children("a").attr("data-type") === "rightShow") {
          // let arr = ["覃丽", "卢业辉"];
          // if (arr.includes(that.loginUser.name)) {
          //   that.$message("您没有权限访问该功能");
          //   return;
          // }
          $(".other-level-menus>ul>li").remove();
          var _children = that.otherMenuMap[$(this).children("a").attr("id")];
          $.each(_children, function (index, item) {
            $(".other-level-menus>ul").append(getOtherLevelLiHtml(item));
          });
          return;
        }
        $(this).parents("ul").addClass("open");
        $(this).next("ul").addClass("open");
        $($(this).parents("li")[$(this).parents("li").length - 1])
          .siblings()
          .find("ul")
          .removeClass("open");
        $(this).next("ul").slideToggle();
        $(".left-level-menus ul:not(.open)").slideUp();
        that.nav2Show = false;
      };
      var getLevelLiHtml = function (data, $id) {
        if (!data) return;
        if (
          data.name ===
            "年度高级经理职等以上管理人员综合能力【评定结果审批】表" &&
          data.parent.name !== "集团总部"
        ) {
          return;
        }
        var _html = "";
        if (data.type === "url") {
          _html = `<li>
                    <div>
                      <i class="iconfont"></i>
                      <a data-type="${data.type}" data-url="${
            data.url
          }" class='url'>${data.name || ""}</a>
                    </div>
                  </li>`;
        } else if (data.type === "rightShow") {
          that.otherMenuMap[data.id] = data.children || [];
          _html = `<li>
                    <div>
                      <i class="iconfont"></i>
                      <a data-type="${data.type}" id="${
            data.id
          }" href="javascript:void(0);">${data.name || ""}</a>
                    </div>
                  </li>`;
        } else {
          _html = `<li>
                    <div>
                      <i class="iconfont icon-jiantouyou"></i>
                      <a data-type="${data.type}" href="javascript:void(0);">${
            data.name || ""
          }</a>
                    </div>
                  </li>`;
        }
        if (
          data.type === "menu" &&
          data.children &&
          data.children.length != 0
        ) {
          var _children = getLevelUlHtml(data);
          var result = $(_html).find(">div").after(_children);
          return result.parent()[0];
        } else {
          return $(_html);
        }
      };
      var getLevelUlHtml = function (data) {
        if (!data) return;
        var id = new Date().getTime() + parseInt(Math.random(10000) * 10000);
        var _html = $(
          "<ul id='" +
            id +
            "' style='display:" +
            (currentMenuId.indexOf(data.id) === -1 ? "none" : "block") +
            ";'></ul>"
        );
        $.each(data.children, function (index, item) {
          _html.append(getLevelLiHtml(item, id));
        });
        return _html;
      };
      var bindEvent = function () {
        $(".left-level-menus ul>li>div")
          .off("click")
          .on("click", menuSlideBind);
      };
      /**
       * 左边菜单方法结束
       */
      var getOtherLevelLiHtml = function (data) {
        if (!data) return;
        var _html = `<li>
                      <div>
                        <a ${data.url ? `href="${data.url}"` : ""}>${
          data.name || ""
        }</a>
                      </div>
                    </li>`;
        if (data.children && data.children.length != 0) {
          var _children = getOtherLevelUlHtml(data);
          var result = $(_html).find(">div").after(_children);
          return result.parent()[0];
        } else {
          return $(_html);
        }
      };
      var getOtherLevelUlHtml = function (data) {
        var _html = $("<ul></ul>");
        $.each(data.children, function (index, item) {
          _html.append(getOtherLevelLiHtml(item));
        });
        return _html;
      };
      const generateId = function () {
        return new Date().getTime() + parseInt(Math.random(10000) * 10000);
      };
      const generateMenuUrl = function (menu_url) {
        return (
          `${window.location.origin}${window.location.pathname}#` + menu_url
        );
      };
      const createMenuObject = function (
        menu_type = "menu",
        menu_name,
        link_url = "",
        parent = null,
        children = []
      ) {
        return {
          name: menu_name,
          type: menu_type,
          url: link_url,
          id: generateId(),
          children,
          parent,
        };
      };
      var initPage = () => {
        // $(".panel-body-wrap").css(
        //   "height",
        //   window.screen.availHeight - 33 + "px"
        // );
        let companyMenus = [];
        /*mIndex==1表示管理人员 */
        if (this.managerAuth && this.mIndex == 1) {
          companyMenus = this.companyData.map((comp, comp_index) => {
            const comp_menu = createMenuObject("menu", comp.name); //顶级公司菜单

            let children = this.$router.options.routes[0].children.map(
              (f_route, i) => {
                if (
                  !f_route.hidden &&
                  !(
                    (f_route.meta &&
                      f_route.meta.fullYear &&
                      this.month == 6) ||
                    (f_route.meta && f_route.meta.halfYear && this.month == 12)
                  )
                ) {
                  /**
                   * @desc 单独控制单个人权限
                   * @date 2018年12月28日17:51:36
                   */
                  if (
                    f_route.meta.personHasPermission &&
                    f_route.meta.personHasPermission["company" + comp.code] &&
                    this.loginUser.loginName
                  ) {
                    if (
                      f_route.meta.personHasPermission[
                        "company" + comp.code
                      ].indexOf(this.loginUser.loginName) == -1
                    ) {
                      return;
                    }
                  }

                  let _menu_url =
                    f_route.meta.type === "url"
                      ? f_route.path
                          .replace(/\:year/g, this.year)
                          .replace(/\:month/g, this.month)
                          .replace(/\:companyGroupId/g, comp.code)
                      : "";
                  //功能菜单(二级)
                  const func_menu = createMenuObject(
                    f_route.meta.type,
                    f_route.name,
                    generateMenuUrl(_menu_url),
                    comp_menu
                  );

                  if (
                    f_route.children &&
                    f_route.children.length != 0 &&
                    f_route.meta.gradeMenu &&
                    !f_route.hidden
                  ) {
                    if (
                      f_route.meta.companyHasProd &&
                      f_route.meta.companyHasProd.indexOf(comp.code) !== -1
                    ) {
                      let children = comp.children.map((grade, grade_index) => {
                        /**
                         * @desc 单独控制单个人权限
                         * @date 2018年12月28日17:51:36
                         */
                        if (
                          // this.loginUser.loginName &&
                          // f_route.meta.personNoPermission &&
                          // f_route.meta.personNoPermission[
                          //   "company" + comp.code
                          // ] &&
                          // f_route.meta.personNoPermission[
                          //   "company" + comp.code
                          // ][this.loginUser.loginName]
                          this.loginUser.loginName &&
                          f_route.meta.personNoPermission &&
                          f_route.meta.personNoPermission.company3
                        ) {
                          if (
                            f_route.meta.personNoPermission.company3.indexOf(
                              grade.code
                            ) != -1
                          ) {
                            return;
                          }
                          // if (
                          //   f_route.meta.personNoPermission[
                          //     "company" + comp.code
                          //   ][this.loginUser.loginName].indexOf(grade.code) !=
                          //   -1
                          // ) {
                          //   return;
                          // }
                        }
                        //各职等菜单(三级)
                        const grade_menu = createMenuObject(
                          "rightShow",
                          grade.name,
                          "",
                          func_menu
                        );

                        if (
                          /*不区分生产一线、生产辅助、生产支持的职等*/
                          (f_route.meta.hasNoProGrade &&
                            f_route.meta.hasNoProGrade.indexOf(grade.code) !==
                              -1) ||
                          comp.code == "1" ||
                          comp.code == "3" ||
                          comp.code == "4"
                        ) {
                          let children = f_route.children.map(
                            (f_child_route, i_child) => {
                              if (
                                !(
                                  f_child_route.meta.fullYear && this.month == 6
                                ) &&
                                !(
                                  f_child_route.meta &&
                                  f_child_route.meta.halfYear &&
                                  this.month == 12
                                )
                              ) {
                                //右侧职等功能菜单(四级)
                                let _menu_url =
                                  f_child_route.meta.type === "url"
                                    ? f_child_route.path
                                        .replace(/\:year/g, this.year)
                                        .replace(/\:month/g, this.month)
                                        .replace(/\:companyGroupId/g, comp.code)
                                        .replace(
                                          /\:positionGradeId/g,
                                          grade.code
                                        )
                                    : "";
                                const f_child_menu = createMenuObject(
                                  "url",
                                  grade.name + f_child_route.name,
                                  generateMenuUrl(_menu_url),
                                  grade_menu
                                );
                                return f_child_menu;
                              }
                            }
                          );
                          grade_menu.children = children;
                          return grade_menu;
                        } else {
                          //区域分类对象(工厂)
                          let outerMenu = [];
                          outerMenu.push({
                            name: "生产一线",
                            deptArea: "A",
                          });
                          outerMenu.push({
                            name: "生产辅助",
                            deptArea: "B",
                          });
                          outerMenu.push({
                            name: " 生产支持",
                            deptArea: "C",
                          });
                          let children = outerMenu.map((outer, outer_i) => {
                            //右侧菜单(四级)
                            const outer_menu = createMenuObject(
                              "menu",
                              outer.name,
                              "",
                              grade_menu
                            );

                            let children = f_route.children.map(
                              (f_child_route, i_child) => {
                                if (
                                  !(
                                    f_child_route.meta.fullYear &&
                                    this.month == 6
                                  ) &&
                                  !(
                                    f_child_route.meta &&
                                    f_child_route.meta.halfYear &&
                                    this.month == 12
                                  )
                                ) {
                                  //右侧菜单(五级)
                                  let _menu_url =
                                    (f_child_route.meta.type === "url"
                                      ? f_child_route.path
                                          .replace(/\:year/g, this.year)
                                          .replace(/\:month/g, this.month)
                                          .replace(
                                            /\:companyGroupId/g,
                                            comp.code
                                          )
                                          .replace(
                                            /\:positionGradeId/g,
                                            grade.code
                                          )
                                      : "") +
                                    "?deptArea=" +
                                    outer.deptArea;
                                  const f_child_menu = createMenuObject(
                                    "url",
                                    grade.name + f_child_route.name,
                                    generateMenuUrl(_menu_url),
                                    outer_menu
                                  );
                                  return f_child_menu;
                                }
                              }
                            );
                            outer_menu.children = children;
                            return outer_menu;
                          });
                          grade_menu.children = children;
                          return grade_menu;
                        }
                      });
                      func_menu.children = children;
                    } else {
                      let children = comp.children.map((grade, grade_index) => {
                        //各职等菜单(三级)
                        const grade_menu = createMenuObject(
                          "rightShow",
                          grade.name,
                          "",
                          func_menu
                        );

                        let children = f_route.children.map(
                          (f_child_route, i_child) => {
                            if (
                              !(
                                f_child_route.meta.fullYear && this.month == 6
                              ) &&
                              !(
                                f_child_route.meta &&
                                f_child_route.meta.halfYear &&
                                this.month == 12
                              )
                            ) {
                              //右侧职等功能菜单(四级)
                              let _menu_url =
                                f_child_route.meta.type === "url"
                                  ? f_child_route.path
                                      .replace(/\:year/g, this.year)
                                      .replace(/\:month/g, this.month)
                                      .replace(/\:companyGroupId/g, comp.code)
                                      .replace(/\:positionGradeId/g, grade.code)
                                  : "";
                              const f_child_menu = createMenuObject(
                                "url",
                                grade.name + f_child_route.name,
                                generateMenuUrl(_menu_url),
                                grade_menu
                              );
                              return f_child_menu;
                            }
                          }
                        );
                        grade_menu.children = children;
                        return grade_menu;
                      });
                      func_menu.children = children;
                    }
                  } else if (f_route.children && f_route.children.length != 0) {
                    let children = f_route.children.map(
                      (f_child_route, i_child) => {
                        //非职等菜单(三级)
                        let _menu_url =
                          f_child_route.meta.type === "url"
                            ? f_child_route.path
                                .replace(/\:year/g, this.year)
                                .replace(/\:month/g, this.month)
                                .replace(/\:companyGroupId/g, comp.code)
                            : "";
                        const f_child_menu = createMenuObject(
                          f_child_route.meta.type,
                          f_child_route.name,
                          generateMenuUrl(_menu_url),
                          func_menu
                        );
                        return f_child_menu;
                      }
                    );
                    func_menu.children = children;
                  }
                  return func_menu;
                }
              }
            );
            comp_menu.children = children;
            return comp_menu;
          });
        }

        //其他顶层菜单
        let top_menu_index = 0;
        let traineeMenuObj = {};
        let topLevelMenu = this.$router.options.routes.map((route) => {
          if (route.meta && route.meta.type) {
            /*管培人员不显示【各公司管培生】模块（huangdh@2022-05-15）*/
            if (this.mIndex == 1 && route.name == "各公司管培生") {
              return;
            }
            if (this.mIndex == 1 && route.name == "评定规则") {
              return;
            }
            if (this.mIndex == 2 && route.name == "全集团高级经理职等以上") {
              return;
            }
            //第一层(管培生和评定规则菜单)
            const topLevelMenu = createMenuObject(route.meta.type, route.name);
            //console.log("this.mIndex:"+this.mIndex+"，route.meta.type:"+route.meta.type+"，route.name:"+route.name);

            if (route.children) {
              let children = route.children.map((child_r, child_i) => {
                if (!!child_r.hidden) {
                  return;
                }
                //第二层：
                let _menu_url = child_r.path
                  .replace(/\:year/g, this.year)
                  .replace(/\:month/g, this.month);
                const topLevelChildMenu = createMenuObject(
                  child_r.meta.type,
                  child_r.name,
                  generateMenuUrl(_menu_url),
                  topLevelMenu
                );
                //console.log("第二层：child_r.name:"+child_r.name);
                /*管理人员、管培生显示各自的评定规则（huangdh@2022-05-15）*/
                if (this.mIndex == 1 && route.name == "评定规则") {
                  if (child_r.name != "管理人员评定说明") {
                    return;
                  }
                }
                if (this.mIndex == 2 && route.name == "评定规则") {
                  if (child_r.name != "管培生评定说明") {
                    return;
                  }
                }

                if (child_r.children && route.meta.traineeMenu) {
                  let years = this.schoolLevel.map((item) => {
                    return item.year;
                  });
                  if (child_r.meta && child_r.meta.traineeLevel) {
                    //岗位
                    let levels = ["1", "2", "3"];
                    let children = levels.map((level) => {
                      //岗位菜单(三级)
                      const schoolLevelMenu = createMenuObject(
                        "rightShow",
                        Utils.number2ChNum(level) + "级岗位",
                        "",
                        topLevelChildMenu
                      );

                      let children = years.map((year, y_index) => {
                        let schoolLevelArr =
                          this.yearSchoolLevelGetSchoolLevelByYear(year);
                        //右侧届别菜单(四级)
                        const yearRightMenu = createMenuObject(
                          "menu",
                          `${year}届`,
                          "",
                          schoolLevelMenu
                        );

                        if (child_r.meta.hasCompanys) {
                          //职务规划表
                          let children = child_r.children.map(
                            (trainee_r, trainee_i) => {
                              if (schoolLevelArr.indexOf(level) === -1) {
                                return;
                              }
                              let level_children_name =
                                level === 1
                                  ? [
                                      { name: "市场销售类", code: "scxs" },
                                      { name: "行政人资类", code: "xzrz" },
                                    ]
                                  : [{ name: "", code: "" }];
                              //右侧届别职务规划菜单(五级)
                              yearRightMenu.children = level_children_name.map(
                                (type_name, comm_item_index) => {
                                  let _menu_name = `${Utils.number2ChNum(
                                    level
                                  )}级岗位${
                                    type_name.name
                                      ? `【${type_name.name}】`
                                      : ``
                                  }${trainee_r.name}`;
                                  let _menu_url =
                                    trainee_r.path
                                      .replace(/\:year/g, this.year)
                                      .replace(/\:graduateYear/g, year)
                                      .replace(/\:schoolLevel/g, level) +
                                    "?type=" +
                                    type_name.code;
                                  const yearRightGradeMenu = createMenuObject(
                                    trainee_r.meta.type,
                                    _menu_name,
                                    generateMenuUrl(_menu_url),
                                    yearRightMenu
                                  );
                                  return yearRightGradeMenu;
                                }
                              );
                            }
                          );
                        }
                        return yearRightMenu.children.length === 0
                          ? null
                          : yearRightMenu;
                      });
                      schoolLevelMenu.children = children;
                      topLevelChildMenu.children.push(schoolLevelMenu);
                    });
                  } else if (child_r.meta && child_r.meta.hasCompanys) {
                    //管培生明细
                    let children = child_r.children.map(
                      (trainee_r, trainee_i) => {
                        if (trainee_r.hidden) {
                          return;
                        }
                        //第三层
                        topLevelChildMenu.children = route.meta.companys.map(
                          ({ name, code }, comm_item_index) => {
                            let _menu_url = trainee_r.path
                              .replace(/\:year/g, this.year)
                              .replace(/\:month/g, this.month)
                              .replace(/\:companyGroupId/g, code);
                            const companyMenu = createMenuObject(
                              trainee_r.meta.type,
                              name + trainee_r.name,
                              generateMenuUrl(_menu_url),
                              topLevelChildMenu
                            );
                            return companyMenu;
                          }
                        );
                        if (child_r.meta && child_r.meta.appendMenu) {
                          child_r.meta.appendMenu.forEach(
                            (append, append_index) => {
                              let _menu_url = append.path
                                .replace(/\:year/g, this.year)
                                .replace(/\:month/g, this.month);
                              const companyDetailMenu = createMenuObject(
                                append.meta.type,
                                append.name,
                                generateMenuUrl(_menu_url),
                                topLevelChildMenu
                              );
                              topLevelChildMenu.children.push(
                                companyDetailMenu
                              );
                            }
                          );
                        }
                      }
                    );
                  } else {
                    let children = child_r.children.map(
                      (trainee_r, trainee_i) => {
                        if (trainee_r.meta.hidden) {
                          return;
                        }
                        //第三层
                        let _menu_url = trainee_r.path
                          .replace(/\:year/g, this.year)
                          .replace(/\:month/g, this.month);
                        const otherTypeMenu = createMenuObject(
                          trainee_r.meta.type,
                          trainee_r.name,
                          generateMenuUrl(_menu_url),
                          topLevelChildMenu
                        );
                        if (
                          trainee_r.children &&
                          trainee_r.meta.hasCompanyNameMenu
                        ) {
                          let children = route.meta.companys.map(
                            ({ name, code }, comm_item_index) => {
                              trainee_r.children.forEach(
                                (other_type_child, other_type_child_index) => {
                                  //得分明细
                                  let _menu_url = other_type_child.path
                                    .replace(/\:year/g, this.year)
                                    .replace(/\:month/g, this.month)
                                    .replace(/\:companyGroupId/g, code);
                                  const companyScoreMenu_ = createMenuObject(
                                    "url",
                                    name + other_type_child.name,
                                    generateMenuUrl(_menu_url),
                                    otherTypeMenu
                                  );
                                  otherTypeMenu.children.push(
                                    companyScoreMenu_
                                  );
                                }
                              );
                            }
                          );
                          return otherTypeMenu;
                        } else if (
                          trainee_r.children &&
                          trainee_r.meta.hasCompanys
                        ) {
                          let children = route.meta.companys.map(
                            ({ name, code }, comm_item_index) => {
                              //评价合议公司
                              let _menu_url = trainee_r.path
                                .replace(/\:year/g, this.year)
                                .replace(/\:month/g, this.month)
                                .replace(/\:companyGroupId/g, code);
                              const companyMenu_ = createMenuObject(
                                "url",
                                name,
                                generateMenuUrl(_menu_url),
                                otherTypeMenu
                              );
                              // trainee_r.children.forEach((other_type_child,other_type_child_index)=>{
                              //   if(other_type_child.meta.noSchoolLevel === true){
                              //     let _menu_url = other_type_child.path.replace(/\:year/g,this.year).replace(/\:companyGroupId/g,code);
                              //     const noSchoolLevelMenu = createMenuObject('url',`${name}${other_type_child.name}`,generateMenuUrl(_menu_url),companyMenu_);
                              //     companyMenu_.children.push(noSchoolLevelMenu)
                              //   }
                              // })
                              // let companyYears = this.yearSchoolLevelGetYearByCompanyGroupId(code);
                              // let gradeLevel = ['1','2','3']
                              // let children = gradeLevel.map((level,level_i)=>{
                              //   //等级岗位
                              //   const gradeLevelMenu = createMenuObject('menu',`${name}${Utils.number2ChNum(level)}级岗位管培生`,'',companyMenu_);
                              //   companyYears.forEach((year,y_index)=>{//届别
                              //     let children = trainee_r.children.forEach((other_type_child,other_type_child_index)=>{
                              //       if(other_type_child.meta.noSchoolLevel) return;
                              //       //评价合议
                              //       let yearSchoolLevels = this.companyGroupsGetSchoolLevelsByCompanyGroupId(this.yearSchoolLevelGetCompanyGroupsByYear(year),code)
                              //       yearSchoolLevels.forEach(grade_level=>{
                              //         if(grade_level==level){
                              //           let _menu_url = other_type_child.path.replace(/\:year/g,this.year).replace(/\:companyGroupId/g,code).replace(/\:schoolLevel/g,grade_level).replace(/\:graduateYear/g,year);
                              //           const otherTypeChildMenu = createMenuObject('url',`${year}届${Utils.number2ChNum(grade_level)}${other_type_child.name}`,generateMenuUrl(_menu_url),gradeLevelMenu);
                              //           gradeLevelMenu.children.push(otherTypeChildMenu)
                              //         }
                              //       })
                              //     })
                              //   });
                              //   if(gradeLevelMenu.children.length!=0){
                              //     return gradeLevelMenu;
                              //   }
                              // })
                              // companyMenu_.children = companyMenu_.children.concat(children);
                              return companyMenu_;
                            }
                          );
                          otherTypeMenu.children = children;
                        }
                        return otherTypeMenu;
                      }
                    );
                    topLevelChildMenu.children = children;
                  }
                } else {
                }
                return topLevelChildMenu;
              });
              topLevelMenu.children = children;
            }
            top_menu_index++;
            if (route.meta.traineeMenu) {
              traineeMenuObj = topLevelMenu;
            } else {
              return topLevelMenu;
            }
          }
        });
        let traineeMenuArr = [];
        if (this.mIndex == 2) {
          //console.log(traineeMenuObj.children)
          traineeMenuObj.children.map((m) => {
            if (m) {
              let _obj = Object.assign({}, m);
              _obj.parent = null;
              traineeMenuArr.push(_obj);
            }
          });
        }
        companyMenus = [].concat(topLevelMenu).concat(companyMenus); //合并所有顶层级菜单
        // !this.traineeAuth || Object.keys(traineeMenuObj).length === 0
        //   ? null
        //   : companyMenus.push(traineeMenuObj); //加入管培生菜单
        !this.traineeAuth || Object.keys(traineeMenuObj).length === 0
          ? null
          : (companyMenus = [...companyMenus, ...traineeMenuArr]); //加入管培生菜单

        let currentMenu = null;
        const whereUrl = function (data, url) {
          data.forEach((item) => {
            if (item && item.url !== url && item.children) {
              whereUrl(item.children, url);
            } else if (item && item.url === url) {
              currentMenu = item;
            }
          });
        };
        whereUrl(companyMenus, getCookie(FromUrlKey) || "");
        currentMenuId = [];
        if (currentMenu) {
          let rightShowArr = [];
          let pushMenu = function (menu) {
            if (menu) {
              if (menu.type == "rightShow") {
                rightShowArr = menu.children;
              }
              currentMenuId.push(menu.id);
              pushMenu(menu.parent);
            }
          };
          pushMenu(currentMenu);
          if (rightShowArr) {
            $(".other-level-menus>ul>li").remove();
            $.each(rightShowArr, function (index, item) {
              if (item) {
                $(".other-level-menus>ul").append(getOtherLevelLiHtml(item));
              }
            });
          }
        }
        $.each(companyMenus, function (index, item) {
          if (!item) return;
          var id = new Date().getTime() + parseInt(Math.random(10000) * 10000);
          $(".left-level-menus>ul").attr("id", id);
          $(".left-level-menus>ul").append(getLevelLiHtml(item, id));
          if (index == companyMenus.length - 1) {
            bindEvent();
          }
        });
      };
      initPage();
    },
    yearSchoolLevelGetCompanyGroupsByYear(year) {
      let result = this.schoolLevel.find((item) => item.year == year);
      return result ? result.company_groups : [];
    },
    yearSchoolLevelGetSchoolLevelByYear(year) {
      let yearObj = this.schoolLevel.find((item) => item.year == year);
      if (!yearObj) {
        return [];
      }
      let result = [];
      yearObj.company_groups.forEach((item) => {
        let levels = item.schoolLevel.split(",");
        levels.forEach((level) => {
          result.indexOf(level) === -1 ? result.push(level) : null;
        });
      });
      return result.sort();
    },
    yearSchoolLevelGetYearByCompanyGroupId(companyGroupId) {
      let yearResult = [];
      this.schoolLevel.forEach((item) => {
        item.company_groups.forEach((companyGroup) => {
          if (companyGroup.companyGroupId == companyGroupId) {
            yearResult.indexOf(item.year) === -1
              ? yearResult.push(item.year)
              : null;
          }
        });
      });
      return yearResult;
    },
    companyGroupsGetSchoolLevelsByCompanyGroupId(
      companyGroups,
      companyGroupId
    ) {
      let resultCompanyGroup = companyGroups.find(
        (item) => item.companyGroupId == companyGroupId
      );
      return resultCompanyGroup
        ? resultCompanyGroup.schoolLevel.split(",")
        : [];
    },
    initMenuData() {
      findAllCompanyAndPositionGrade().then((res) => {
        let data = res.data;
        let company_position_data = [];
        data.forEach((comp) => {
          if (comp) {
            let keys = Object.keys(comp);
            keys.forEach((key) => {
              let compObj = comp[key];
              let comp_prop_arr = key.split("\|");
              company_position_data.push({
                code: comp_prop_arr[0],
                name: comp_prop_arr[1],
                children: compObj.map((grade) => {
                  return {
                    name: grade.menu_name,
                    code: grade.fd_id,
                  };
                }),
              });
            });
          }
        });
        this.companyData = company_position_data;
      });
    },
  },
  async mounted() {
    // this.navList = list;
    const loading = this.$loading({
      lock: true,
      text: "Loading",
      spinner: "el-icon-loading",
      background: "rgba(0, 0, 0, 0.7)",
    });
    let loginUser = JSON.parse(getCookie(UserKey) || "{}");
    this.loginUser = loginUser;
    const { year, month, mIndex } = this.$route.params;
    this.year = year;
    this.month = month;
    this.mIndex = mIndex;
    //console.log("mIndex:"+mIndex);
    await getAuthGroup().then((res) => {
      let result = false;
      /*if(loginUser&&loginUser.loginName){
          if(res.managerGroup.indexOf(loginUser.loginName)!== -1){
            this.managerAuth = true;
          }
          if(res.traineeGroup.indexOf(loginUser.loginName)!== -1){
            this.traineeAuth = true;
          }
      }*/
      /*去掉权限校验，用下面两代码替代 */
      this.managerAuth = true;
      this.traineeAuth = true;

      if (this.month == 6 && this.traineeAuth) {
        findSchoolLevel({ year: this.year }).then((res) => {
          this.schoolLevel = res.data || [];
          this.$nextTick(() => {
            this.initMenuData();
          });
          loading.close();
        });
      } else {
        loading.close();
        this.initMenuData();
      }
    });
  },
};
</script>

<style lang="scss">
@import url("../assets/css/iconfont.css");
</style>
<style>
#app .panel-body-wrap h3,
#app .panel-body-wrap p,
#app .panel-body-wrap ul {
  margin: 0;
  padding: 0;
  list-style: none;
  text-align: left;
}

#app .panel-body-wrap > ul {
  height: 100%;
  list-style: none;
  width: 100%;
  padding: 0 20px;
  margin: 0;
  box-sizing: border-box;
}

#app .panel-body-wrap > ul li {
  cursor: pointer;
  padding: 0 10px;
  display: inline-block;
  height: 100%;
  line-height: 32px;
  color: blue;
}

#app .panel-body-wrap {
  padding: 20px;
}

/**
注释样式移至index.html，在此处写无背景效果？
*/
/* #app .panel-body-wrap .left-level-menus {
  color: #fff;
  width: 548px;
  float: left;
  display: inline-block;
  border: solid 1px #676d6e;
  background: -webkit-gradient(linear, left bottom, left top, from(#dc9870), to(#7e3813));
  background: linear-gradient(bottom, #dc9870, #7e3813);
  -ms-filter: "progid:DXImageTransform.Microsoft.Shadow(Strength=4, Direction=135, Color='#676d6e')";
  filter: progid:DXImageTransform.Microsoft.Shadow(Strength=4, Direction=135, Color='#676d6e') progid:DXImageTransform.Microsoft.gradient(startcolorstr=#dc9870,endcolorstr=#7e3813,gradientType=0);
  -webkit-box-shadow: 1px 2px 8px black;
  box-shadow: 2px 2px 4px #676d6e;
  padding-bottom: 30px;
} */
#app .panel-body-wrap .left-level-menus > h3 {
  margin: 0;
  padding: 10px 20px 0px;
  font-size: 25px;
  text-align: center;
}

#app .panel-body-wrap .left-level-menus > ul > li {
  cursor: pointer;
  font-size: 16px;
  padding: 0;
  border-bottom: 1px solid #ccc;
}

#app .panel-body-wrap .left-level-menus > ul > li .iconfont {
  font-size: 14px;
  position: relative;
  top: -2px;
  height: 14px;
  width: 14px;
  display: inline-block;
}

#app .panel-body-wrap .left-level-menus > ul > li div {
  height: 44px;
  line-height: 44px;
  padding: 0 20px;
}

#app .panel-body-wrap .left-level-menus ul > li > div > span,
#app .panel-body-wrap .left-level-menus ul > li > div > a {
  color: #000;
}

#app .panel-body-wrap .left-level-menus > ul > li > div > span {
  margin-left: 0;
  color: #fff;
}

#app .panel-body-wrap .left-level-menus > ul > li > div > a {
  color: #fff;
  text-decoration: none;
}

#app .panel-body-wrap .left-level-menus > ul > li > ul {
  background: #9fccd4;
  color: #000;
}

#app .panel-body-wrap .left-level-menus > ul > li > ul > li {
  font-size: 15px;
}

#app .panel-body-wrap .left-level-menus > ul > li > ul > li > div {
  padding-left: 30px;
  height: 30px;
  line-height: 30px;
}

#app .panel-body-wrap .left-level-menus > ul > li > ul > li > div > a {
  color: #000;
}

#app .panel-body-wrap .left-level-menus > ul > li > ul > li > ul {
  background: #cbe2e6;
  color: #000;
}

#app .panel-body-wrap .left-level-menus > ul > li > ul > li > ul > li {
  font-size: 14px;
}

#app .panel-body-wrap .left-level-menus > ul > li > ul > li > ul > li div {
  padding-left: 50px;
  height: 30px;
  line-height: 30px;
}

#app .panel-body-wrap .other-level-menus {
  position: absolute;
  left: 590px;
}

#app .panel-body-wrap .other-level-menus > h3 {
  font-size: 18px;
}

#app .panel-body-wrap .other-level-menus ul {
  padding: 0px 20px 10px 20px;
  margin: 0;
}

#app .panel-body-wrap .other-level-menus ul li {
  line-height: 26px;
}

#app .panel-body-wrap .other-level-menus ul li a {
  text-decoration: none;
  color: #444;
}

#app .panel-body-wrap .other-level-menus ul li a:hover {
  text-decoration: underline;
  color: rgb(6, 6, 43);
}
.list1 {
  border-bottom: 1px solid #ccc;
  text-align: left;
  padding: 10px 0 10px 20px;
  font-size: 17px;
  cursor: pointer;
}
.bt1 {
  background: #9fccd4;
  color: #000;
  text-align: left;
  font-size: 16px;
  cursor: pointer;
  overflow: hidden;
}
.bt1:hover {
  background: #87c0ca;
}
.bt2 {
  background: #cbe2e6;
  color: #000;
  text-align: left;
  font-size: 15px;
  cursor: pointer;
  overflow: hidden;
}
.bt2 > div {
  padding: 2px 0 2px 30px;
}
.bt2:hover {
  background: #b1c4c7;
}
.closer {
  max-height: 0;
  overflow: hidden;
  transition: 0.3s;
}
.isOpen {
  max-height: 600px;
}
.nav2List {
  text-align: left;
  padding-top: 8px;
}
.nav2List:hover {
  border-bottom: 1px solid#666;
  cursor: pointer;
}
</style>
