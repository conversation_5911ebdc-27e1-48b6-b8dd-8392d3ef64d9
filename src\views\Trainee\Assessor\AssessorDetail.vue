<template>
  <div class="assessor-detail-container">
    <div class="row">
      <div class="col-xs-12 text-left">
        <h4 class="col-xs-6">【{{ getCompanyName() }}】管培生评定人明细表</h4>

        <div class="col-xs-6" style="line-height: 40px">
          <div class="butFlex">
            <el-upload
              :show-file-list="false"
              action=""
              :headers="headers"
              :file-list="fileList"
              :before-upload="BeforeUpload"
              :http-request="Upload"
            >
              <button type="button" class="btn btn-primary btn-xs">
                导入模板
              </button>
            </el-upload>
            <button
              type="button"
              class="btn btn-primary btn-xs"
              @click="sheetIt"
            >
              下载模板
            </button>
            <button
              type="button"
              class="btn btn-primary btn-xs"
              @click="showDescrDialog"
            >
              评定人说明
            </button>
            <button
              class="btn btn-primary btn-xs"
              @click="doStoraging"
              :disabled="disabledButton"
              v-if="
                $store.state.doubleCol.arrAuths.point_ablity_updateNotCommitSave
              "
            >
              保存录入
            </button>
            <button
              class="btn btn-primary btn-xs"
              @click="_updateConfirmLeader"
              :disabled="disabledButton"
              v-if="
                $store.state.doubleCol.arrAuths
                  .point_ablity_updateConfirmLeaderByStudent
              "
            >
              提交OA
            </button>
            <button
              class="btn btn-primary btn-xs"
              @click="
                $router.push(
                  `/${$route.params.year}/${$route.params.month}/menu/2`
                )
              "
            >
              返回目录
            </button>
          </div>
        </div>
      </div>
    </div>
    <div id="table" class="row" style="min-width:1436px">
      <div class="col-xs-12">
        <div class="list-table">
          <div class="scroll-table" style="height: 100%; overflow-x: hidden">
            <table class="scroll-table-header table-bordered">
              <colgroup>
                <col style="width: 45px" />
                <col style="width: 100px" />
                <col style="width: 40px" />
                <col style="width: 45px" />
                <col style="width: 55px" />
                <col style="width: 100px" />
                <col style="width: 90px" />
                <col style="width: 70px" />
                <col style="width: 100px" />
                <col style="width: 70px" />
                <col style="width: 100px" />
                <col style="width: 70px" />
                <col style="width: 100px" />
                <col style="width: 70px" />
                <col style="width: 100px" />
                <col style="width: 70px" />
                <col style="width: 100px" />
                <col style="width: 80px" />
              </colgroup>
              <thead>
                <tr>
                  <th rowspan="2">序号</th>
                  <th colspan="6">评定对象</th>
                  <th colspan="2">直接领导</th>
                  <th colspan="2">间接领导</th>
                  <th colspan="2">关联业务领导1</th>
                  <th colspan="2">关联业务领导2</th>
                  <th colspan="2">人资领导（上海专用）</th>
                  <th rowspan="2">备注</th>
                </tr>
                <tr>
                  <th>部门</th>
                  <th>岗位级别</th>
                  <th>届别</th>
                  <th>姓名</th>
                  <th>岗位</th>
                  <th>职务等级</th>
                  <th>姓名</th>
                  <th>评定方式</th>
                  <th>姓名</th>
                  <th>评定方式</th>
                  <th>姓名</th>
                  <th>评定方式</th>
                  <th>姓名</th>
                  <th>评定方式</th>
                  <th>姓名</th>
                  <th>评定方式</th>
                </tr>
              </thead>
            </table>
            <div
              class="scroll-table-body"
              :style="{
                overflow: 'auto',
                maxHeight: body_height - 160 + 'px',
                'overflow-y': 'scroll',
                'overflow-x': 'hidden',
              }"
            >
              <table
                style="
                  margin: 0px 10px 0 0;
                  position: relative;
                  font-size: 13px;
                  float: left;
                  border: none;
                "
                class="table table-bordered table-hover table-striped"
              >
                <colgroup>
                  <col style="width: 45px" />
                  <col style="width: 100px" />
                  <col style="width: 40px" />
                  <col style="width: 45px" />
                  <col style="width: 55px" />
                  <col style="width: 100px" />
                  <col style="width: 90px" />
                  <col style="width: 70px" />
                  <col style="width: 100px" />
                  <col style="width: 70px" />
                  <col style="width: 100px" />
                  <col style="width: 70px" />
                  <col style="width: 100px" />
                  <col style="width: 70px" />
                  <col style="width: 100px" />
                  <col style="width: 70px" />
                  <col style="width: 100px" />
                  <col style="width: 80px" />
                </colgroup>
                <tbody>
                  <tr v-for="(data, $index) in tableData" :key="$index">
                    <td>{{ $index + 1 }}</td>
                    <td v-if="!data.useDay1" :rowspan="data.useDay2">
                      {{ data.deptName }}
                    </td>
                    <td v-if="!data.useDay3" :rowspan="data.sortIndex">
                      {{ getSchoolLevel(data.schoolLevel) || "" }}
                    </td>
                    <td>{{ data.graduateYear }}</td>
                    <td>{{ data.userName }}</td>
                    <td>{{ data.jobName }}</td>
                    <td>{{ data.gradeName }}</td>
                    <td>
                      <input
                        v-if="isPrint"
                        type="text"
                        v-model="data.relationLeader3"
                        class="form-control input-sm"
                      />
                      <span v-else>{{ data.relationLeader3 }}</span>
                    </td>
                    <td>
                      <select
                        v-if="isPrint"
                        v-model="data.leader3JudgeType"
                        style="width: 90%; height: 24px; line-height: 28px"
                      >
                        <option :value="null" selected></option>
                        <option
                          v-for="(item, index) in judgeTypes"
                          :key="index"
                          :value="item"
                        >
                          {{ item }}
                        </option>
                      </select>
                      <span v-else>{{ data.leader3JudgeType }}</span>
                    </td>
                    <td>
                      <input
                        v-if="isPrint"
                        type="text"
                        v-model="data.relationLeader4"
                        class="form-control input-sm"
                      />
                      <span v-else>{{ data.relationLeader4 }}</span>
                    </td>
                    <td>
                      <select
                        v-if="isPrint"
                        v-model="data.leader4JudgeType"
                        style="width: 90%; height: 24px; line-height: 28px"
                      >
                        <option :value="null" selected></option>
                        <option
                          v-for="(item, index) in judgeTypes"
                          :key="index"
                          :value="item"
                        >
                          {{ item }}
                        </option>
                      </select>
                      <span v-else>{{ data.leader4JudgeType }}</span>
                    </td>
                    <td>
                      <input
                        v-if="isPrint"
                        type="text"
                        v-model="data.relationLeader1"
                        class="form-control input-sm"
                      />
                      <span v-else>{{ data.relationLeader1 }}</span>
                    </td>
                    <td>
                      <select
                        v-if="isPrint"
                        v-model="data.leader1JudgeType"
                        style="width: 90%; height: 24px; line-height: 28px"
                      >
                        <option :value="null" selected></option>
                        <option
                          v-for="(item, index) in judgeTypes"
                          :key="index"
                          :value="item"
                        >
                          {{ item }}
                        </option>
                      </select>
                      <span v-else>{{ data.leader1JudgeType }}</span>
                    </td>
                    <td>
                      <input
                        v-if="isPrint"
                        type="text"
                        v-model="data.relationLeader2"
                        class="form-control input-sm"
                      />
                      <span v-else>{{ data.relationLeader2 }}</span>
                    </td>
                    <td>
                      <select
                        v-if="isPrint"
                        v-model="data.leader2JudgeType"
                        style="width: 90%; height: 24px; line-height: 28px"
                      >
                        <option :value="null" selected></option>
                        <option
                          v-for="(item, index) in judgeTypes"
                          :key="index"
                          :value="item"
                        >
                          {{ item }}
                        </option>
                      </select>
                      <span v-else>{{ data.leader2JudgeType }}</span>
                    </td>
                    <td>
                      <input
                        v-if="isPrint"
                        type="text"
                        v-model="data.relationLeader5"
                        class="form-control input-sm"
                      />
                      <span v-else>{{ data.relationLeader5 }}</span>
                    </td>
                    <td>
                      <select
                        v-if="isPrint"
                        v-model="data.leader5JudgeType"
                        style="width: 90%; height: 24px; line-height: 28px"
                      >
                        <option :value="null" selected></option>
                        <option
                          v-for="(item, index) in judgeTypes"
                          :key="index"
                          :value="item"
                        >
                          {{ item }}
                        </option>
                      </select>
                      <span v-else>{{ data.leader5JudgeType }}</span>
                    </td>
                    <td>{{ data.remark }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  findStudentListByCompanyGroupId,
  updateNotCommitSave,
  updateConfirmLeaderByStudent,
  importAssessor,
} from "@/api";
import { getCookie, TokenKey } from "@/utils/Cookie";
export default {
  data() {
    return {
      headers: {
        [TokenKey]: getCookie(TokenKey),
      },
      isLoading: true,
      body_height: 0,
      tableData: [],
      fileList: [],
      companyGroupId: "",
      judgeTypes: ["综合分", "成功要素"],
      year: "",
      month: "",
      newFile: new FormData(),
      isPrint: true,
    };
  },
  computed: {
    disabledButton() {
      /*提交OA*/
      if (this.isLoading) {
        return true;
      } else if (this.tableData == null || this.tableData.length == 0) {
        return true;
      } else if (this.tableData && this.tableData.length != 0) {
        //return this.tableData[0].confirmLeaderId ? true : false;
        return this.tableData[0].confirmLeaderStatus > 10 ? true : false;
      }
      return false;
    },
  },
  methods: {
    BeforeUpload(file) {
      // const fileSuffix = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()
      // const whiteList = ['csv']
      if (file) {
        this.newFile.append("file", file);
      } else {
        return false;
      }
    },
    Upload() {
      const loading = this.$loading({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      this.newFile.append("companyGroupId", this.companyGroupId);
      this.newFile.append("year", this.year);
      this.newFile.append("month", this.month);
      this.newFile.append("userType", "管培生");
      const newData = this.newFile;
      let params = newData;
      importAssessor(params)
        .then((res) => {
          loading.close();
          layer.msg(res.data, { icon: 1, shade: 0.3, shadeClose: true });
          this._findStudentListByCompanyGroupId();
        })
        .catch((err) => {
          loading.close();
          layer.msg(`${err.msg}`, { icon: 2, shade: 0.3, shadeClose: true });
        });
    },
    sheetIt() {
      this.isPrint = false;
      setTimeout(() => {
        this.exportExcelOne.exportExcel(
          `【${this.getCompanyName()}】管培生评定人明细表.xlsx`,
          "#table"
        );
        this.isPrint = true;
      });
    },
    getSchoolLevel(level) {
      return Utils.number2ChNum(level) + "级";
    },
    getCompanyName() {
      return Utils.getCompanyNameByGroupId(this.companyGroupId);
    },
    _updateConfirmLeader() {
      if (this.disabledButton) {
        return;
      }
      layer.confirm(
        "是否确定提交OA？",
        {
          title: "提示",
          btn: ["确定", "取消"], //按钮
        },
        (index) => {
          this.isLoading = true;
          updateConfirmLeaderByStudent(this.tableData, { PositionGradeId: "" })
            .then((res) => {
              let result = res.success;
              layer.msg(`提交OA${result ? "成功" : "失败"}`, {
                icon: result ? 1 : 2,
                shade: 0.3,
                shadeClose: true,
              });
              this._findStudentListByCompanyGroupId();
              layer.close(index);
            })
            .catch((err) => {
              layer.msg(`${err.msg}`, {
                icon: 2,
                shade: 0.3,
                shadeClose: true,
              });
            });
        }
      );
    },
    //暂存
    doStoraging() {
      if (this.disabledButton2) {
        return;
      }
      //this.isLoading = true;
      updateNotCommitSave(this.tableData, { PositionGradeId: "" })
        .then((res) => {
          let result = res.success;
          layer.msg(`评价人暂存${result ? "成功" : "失败"}`, {
            icon: result ? 1 : 2,
            shade: 0.3,
            shadeClose: true,
          });
          this._findStudentListByCompanyGroupId();
        })
        .catch((err) => {
          layer.msg(`${err.msg}`, { icon: 2, shade: 0.3, shadeClose: true });
        });
    },
    _findStudentListByCompanyGroupId() {
      /*先判断有权限才可以查询(modify by huangdh@2019-05-24)*/
      if (
        this.$store.state.doubleCol.arrAuths
          .point_ablity_findStudentListByCompanyGroupId
      ) {
        findStudentListByCompanyGroupId({
          companyGroupId: this.companyGroupId,
          year: this.year,
          month: this.month,
        }).then((res) => {
          this.isLoading = false;
          this.tableData = res.data || [];
        });
      } else {
        layer.msg(`对不起，您没有权限查询本界面.`, {
          icon: 2,
          shade: 0.3,
          shadeClose: true,
        });
      }
    },
    showDescrDialog() {
      Utils.layerBox.layerDialogOpen({
        title: "<b>评定人说明：</b>",
        area: ["850px", "540px"],
        btn: ["关闭"],
        content: `<div style="padding:10px 20px;margin:0 auto;">
          <p style="padding: 0 20px;">
              <strong>1、评定人资格：</strong><br>
              （1）关联业务领导：指的是与评定对象有关联业务接触的部门领导。<br>
              （2）直接领导：指的是评定对象的部门主管，直接汇报上级。<br>
              （3）间接领导：指的是评定对象的体系领导。<br>
              （4）人资领导：指的是了解人资对象的人资体系领导。评定人数少于5人时，则不选。<br>
              <strong>2、评定人人数及权重设置</strong><br>
              （1）评定人人数：2-5人，不允许1个人进行评定。<br>
              （2）评定人数为5人的，间接领导权重为30%，直接领导权重为20%，人资领导权重为20%，业务关联领导均为15%；<br>
              （3）评定人数为4人的，间接领导权重为40%，直接领导权重为30%，业务关联领导均为15%；<br>
              （4）评定人数为3人的<br>
              ①有间接领导的：间接领导权重为50%，直接领导和业务关联领导均为25%；<br>
              ②没有间接领导的：直接领导权重为50%，2个关联业务领导各占25%； <br>
              （5）评定人数为2人的 <br>
              ①有间接领导的：间接领导权重为60%，业务关联领导或直接领导为40%； <br>
              ②没有间接领导的：直接领导权重为60%，业务关联领导为40%。 <br>
              <strong>3、评定方式确定</strong><br>
              （1）成功要素：指的是运用成功要素（九大要素）对评定对象进行逐项评定，原则上熟悉成功要素的评定人采用“成功要素”进行评定。<br>
              （2）综合分：指的是根据日常工作中评定对象的工作表现，直接用综合分进行评定。评定人属于首次接触成功要素，尚不能完全掌握其核心要点，采用“综合分”进行评定。<br>

        </p></div>`,
      });
    },
    initPage() {
      this.pageResize();
      const { companyGroupId, year, month } = this.$route.params;
      this.companyGroupId = companyGroupId;
      this.year = year;
      this.month = month;
      this._findStudentListByCompanyGroupId();

      document.getElementsByClassName("el-upload__input")[0].style =
        "display:none";
    },

    pageResize() {
      // this.body_height = $(window).height();
    },
  },
  watch: {
    $route: function () {
      this.initPage();
    },
  },
  mounted() {
    this.initPage();
    $(window).resize(this.pageResize);
  },
};
</script>

<style scoped>
.butFlex {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: flex-end;
}

.butFlex > button {
  margin-left: 10px;
}
/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}
.scroll-table-body {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
th {
  padding: 2px !important;
}
td {
  padding: 2px !important;
}
.col-xs-12 {
  padding: 0 !important;
}
</style>
