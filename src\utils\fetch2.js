import axios from 'axios';
import {getCookie, <PERSON><PERSON><PERSON><PERSON>} from './Cookie'
// console.log(getCookie())
// axios.defaults.withCredentials = true
// 创建axios实例
const service = axios.create({
    baseURL: process.env.BASE_API, // api的base_url
    timeout: 25000,
    // 请求超时时间
    withCredentials: process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'sit',
    responseType: 'blob'
});

// request拦截器
if (process.env.NODE_ENV !== 'development') {
    service.interceptors.request.use(config => {
        //     // Do something before request is sent
        // if (store.getters.token) {
        config.headers['Authorization'] = getCookie(TokenKey);
        // 让每个请求携带token--['X-Token']为自定义key 请根据实际情况自行修改
        // }
        return config;
        // }, error => {
        //     // Do something with request error
        //     console.log(error); // for debug
        //     Promise.reject(error);
    })
}

// respone拦截器
service.interceptors.response.use(response => { /**
         * 下面的注释为通过response自定义code来标示请求状态，当code返回如下情况为权限有问题，登出并返回到登录页
        * 如通过xmlhttprequest 状态码标识 逻辑可写在下面error中
        */
    const res = response.data;
    if (! res.success) { // 50008:非法的token; 50012:其他客户端登录了;  50014:Token 过期了;
        if (res.code === 401) {
            layer.confirm("Token已失效，请重新登录.", {
                title: '提示',
                btn: ['确定', '取消'] // 按钮
            }, (index) => {
                window.location.href = process.env.LOGIN_PATH + window.location.href;
                layer.close(index)
            })
        } else {}
        return Promise.reject(response.data);
    } else {
        return response.data;
    }
}, error => {
    const {status} = error.response || {}
    if (status === 401) {
        layer.confirm("Token已失效，请重新登录.", {
            title: '提示',
            btn: ['确定', '取消'] // 按钮
        }, (index) => {
            window.location.href = process.env.LOGIN_PATH + window.location.href;
            layer.close(index)
        })
    }
    console.log('err' + error); // for debug
    return Promise.reject(error);
})

export default service;
