<template>
  <div class="result-detail-container">
    <div class="row">
      <div
        class="col-xs-12"
        style="font-size:20px;font-weight:bold;text-center"
      >
        {{ $route.name }}
      </div>
      <div class="col-xs-12 text-left">
        <div class="col-xs-5 text-left" style="line-height: 40px">
          <label>评定年度：</label>
          <label style="width: 140px; font-weight: normal; text-align: left">{{
            checkYear
          }}</label>
          <label>审批状态：</label>
          <label style="width: 100px; font-weight: normal; text-align: left">{{
            approveStatus
          }}</label>
        </div>
        <div class="col-xs-7 text-right" style="line-height: 40px">
          <!-- <button
            class="btn btn-primary btn-xs"
            @click="_findListByFdYear('1')"
          >
            {{ buttonQueryText }}
          </button> -->
          <button class="btn btn-primary btn-xs" @click="$router.go(-1)">
            返回上级
          </button>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-xs-12">
        <div class="list-table">
          <div class="table-container">
            <div class="table-header-wrapper">
              <table class="scroll-table-header table-bordered">
                <colgroup>
                  <col width="80" />
                  <col width="40" />
                  <col width="55" />
                  <col width="45" />
                  <col width="75" />
                  <col width="70" />
                  <col width="90" />
                  <col width="90" />
                  <col width="80" />
                  <col width="50" />
                  <col width="75" />
                  <col width="40" />
                  <col width="75" />
                  <col width="75" />
                  <col width="40" />
                  <col width="80" />
                </colgroup>
                <thead>
                  <tr>
                    <th rowspan="2">公司</th>
                    <th rowspan="2">序号</th>
                    <th colspan="8">评定对象</th>
                    <th colspan="2">本期评定</th>
                    <th colspan="2">本期合议</th>
                    <th colspan="2">本期审批</th>
                    <th rowspan="2">备注</th>
                  </tr>
                  <tr>
                    <th>姓名</th>
                    <th>届别</th>
                    <th>岗位级别</th>
                    <th>学历</th>
                    <th>部门</th>
                    <th>职务</th>
                    <th>职等</th>
                    <th>年份</th>
                    <th>得分</th>
                    <th>能力等级</th>
                    <th>得分</th>
                    <th>能力等级</th>
                    <th>能力等级</th>
                    <th>留任结论</th>
                  </tr>
                </thead>
              </table>
            </div>
            <div
              class="table-body-wrapper"
              :style="{
                maxHeight: body_height - 190 + 'px',
              }"
            >
              <table class="table table-bordered table-hover table-striped">
                <colgroup>
                  <col width="80" />
                  <col width="40" />
                  <col width="55" />
                  <col width="45" />
                  <col width="75" />
                  <col width="70" />
                  <col width="90" />
                  <col width="90" />
                  <col width="80" />
                  <col width="50" />
                  <col width="75" />
                  <col width="40" />
                  <col width="75" />
                  <col width="75" />
                  <col width="40" />
                  <col width="80" />
                </colgroup>
                <tbody>
                  <template v-for="(item, index) in tableData">
                    <template v-if="isPositionLevel(item.companyName)">
                      <template
                        v-for="(company, cIndex) in getUniqueCompanies(
                          item.list
                        )"
                      >
                        <tr :key="`level-${index}-${cIndex}`">
                          <template
                            v-if="
                              isFirstPositionLevelForCompany(company, index)
                            "
                          >
                            <td :rowspan="getCompanyTotalRows(company)">
                              {{ company }}
                            </td>
                          </template>

                          <td
                            style="
                              text-align: left;
                              font-size: 14px;
                              font-weight: bold;
                            "
                            colspan="15"
                          >
                            {{ item.companyName }}
                          </td>
                        </tr>

                        <template
                          v-for="(student, sIndex) in getStudentsByCompany(
                            item.list,
                            company
                          )"
                        >
                          <tr :key="`student-${index}-${cIndex}-${sIndex}`">
                            <td>{{ sIndex + 1 }}</td>
                            <td>{{ student.userName || "" }}</td>
                            <td>
                              {{
                                student.graduateYear == null
                                  ? ""
                                  : student.graduateYear
                              }}
                            </td>
                            <td>
                              {{
                                student.schoolLevel == 1
                                  ? "一级"
                                  : student.schoolLevel == 2
                                  ? "二级"
                                  : student.schoolLevel == 3
                                  ? "三级"
                                  : ""
                              }}
                            </td>
                            <td>{{ student.degree || "" }}</td>
                            <td>{{ student.deptName || "" }}</td>
                            <td>{{ student.jobName || "" }}</td>
                            <td>{{ student.gradeName || "" }}</td>
                            <td>
                              {{
                                student.useDay1 == null
                                  ? ""
                                  : (student.fdMonth == 6 ? "满" : "第") +
                                    student.useDay1 +
                                    "年"
                              }}
                            </td>

                            <td
                              :style="{
                                color:
                                  student.avgPoint == student.meetingPoint
                                    ? ''
                                    : 'red',
                              }"
                            >
                              {{ toFixed2(student.avgPoint) }}
                            </td>
                            <td
                              :style="{
                                color:
                                  student.ablityLevel == student.meetingLevel
                                    ? ''
                                    : 'red',
                              }"
                            >
                              {{ student.ablityLevel || "" }}
                            </td>
                            <td
                              :style="{
                                color:
                                  student.avgPoint == student.meetingPoint
                                    ? ''
                                    : 'red',
                              }"
                            >
                              {{ toFixed2(student.meetingPoint) }}
                            </td>
                            <td
                              :style="{
                                color:
                                  student.ablityLevel == student.meetingLevel
                                    ? ''
                                    : 'red',
                              }"
                            >
                              {{ student.meetingLevel || "" }}
                            </td>
                            <td>
                              {{
                                ((student.adjustLevel || "") == ""
                                  ? student.meetingLevel
                                  : student.adjustLevel) || ""
                              }}
                            </td>
                            <td>{{ student.adjustResult }}</td>
                            <td>{{ student.bakRemark || "" }}</td>
                          </tr>
                        </template>
                      </template>
                    </template>
                    <template v-else>
                      <tr>
                        <td :rowspan="item.list ? item.list.length : 1">
                          {{ item.companyName }}
                        </td>
                        <td
                          colspan="15"
                          style="text-align: left; font-weight: bold"
                        >
                          {{ item.title || "" }}
                        </td>
                      </tr>
                      <tr v-for="(data, $index) in item.list">
                        <td>{{ $index + 1 }}</td>
                        <td>{{ data.userName || "" }}</td>
                        <td>
                          {{
                            data.graduateYear == null ? "" : data.graduateYear
                          }}
                        </td>
                        <td>
                          {{
                            data.schoolLevel == 1
                              ? "一级"
                              : data.schoolLevel == 2
                              ? "二级"
                              : data.schoolLevel == 3
                              ? "三级"
                              : ""
                          }}
                        </td>
                        <td>{{ data.degree || "" }}</td>
                        <td>{{ data.deptName || "" }}</td>
                        <td>{{ data.jobName || "" }}</td>
                        <td>{{ data.gradeName || "" }}</td>
                        <td>
                          {{
                            data.useDay1 == null
                              ? ""
                              : (data.fdMonth == 6 ? "满" : "第") +
                                data.useDay1 +
                                "年"
                          }}
                        </td>
                        <td
                          :style="{
                            color:
                              data.avgPoint == data.meetingPoint ? '' : 'red',
                          }"
                        >
                          {{ toFixed2(data.avgPoint) }}
                        </td>
                        <td
                          :style="{
                            color:
                              data.ablityLevel == data.meetingLevel
                                ? ''
                                : 'red',
                          }"
                        >
                          {{ data.ablityLevel || "" }}
                        </td>
                        <td
                          :style="{
                            color:
                              data.avgPoint == data.meetingPoint ? '' : 'red',
                          }"
                        >
                          {{ toFixed2(data.meetingPoint) }}
                        </td>
                        <td
                          :style="{
                            color:
                              data.ablityLevel == data.meetingLevel
                                ? ''
                                : 'red',
                          }"
                        >
                          {{ data.meetingLevel || "" }}
                        </td>
                        <td>
                          {{
                            ((data.adjustLevel || "") == ""
                              ? data.meetingLevel
                              : data.adjustLevel) || ""
                          }}
                        </td>
                        <td>{{ data.adjustResult }}</td>
                        <td>{{ data.bakRemark || "" }}</td>
                      </tr>
                    </template>
                  </template>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { findListByFdYear, findGraduateYear, commitStudentToOA } from "@/api";
export default {
  data() {
    return {
      tableData: [],
      companyGroupId: "1",
      schoolLevel: "",
      graduate_year: "",
      year: "",
      month: "",
      checkYear: "",
      approveStatus: "",
      body_height: 0,
      mySort: "",
      buttonQueryText: "按届别显示",
      authCompanyName: "",
    };
  },
  computed: {
    disabledButton() {
      if (this.isLoading) {
        return true;
      } else if (
        this.tableData == null ||
        this.authCompanyName === "您无权限查阅"
      ) {
        return true;
      } else if (
        this.tableData &&
        this.tableData[0] &&
        this.tableData.length != 0
      ) {
        return this.tableData[0].list[0].studentStatus > 11 ? true : false;
      }
      return false;
    },
  },
  methods: {
    _findListByFdYear(queryType) {
      /*先判断有权限才可以查询(modify by huangdh@2019-05-24)*/
      if (this.$store.state.doubleCol.arrAuths.point_ablity_findListByFdYear) {
        findListByFdYear({
          fdYear: this.$route.params.year,
          fdMonth: this.$route.params.month,
          sortType: this.mySort,
        }).then((res) => {
          this.tableData = res.data || [];
          let status = this.tableData[0].list[0].studentStatus;
          this.authCompanyName = this.tableData[0].companyName;
          if (status == 30) {
            this.approveStatus = "已审批";
          } else if (status == 20) {
            this.approveStatus = "审批中";
          } else if (status == 11 || status == 12) {
            this.approveStatus = "已提交";
          } else {
            this.approveStatus = "待提交";
          }
          if (queryType == "1") {
            if (this.mySort == "1") {
              this.mySort = "2";
              this.buttonQueryText = "按届别显示";
            } else {
              this.mySort = "1";
              this.buttonQueryText = "按任职公司显示";
            }
          }
          this.$nextTick(() => {
            this.adjustTableWidth();
          });
        });
      } else {
        layer.msg(`对不起，您没有权限查询本界面.`, {
          icon: 2,
          shade: 0.3,
          shadeClose: true,
        });
      }
    },
    _commitStudentToOA() {
      console.log(this.month);
      if (this.disabledButton) {
        return;
      }
      layer.confirm(`确认要提交OA审批吗?`, { icon: 3 }, (index) => {
        this.isLoading = true;
        let arr = [];
        this.tableData.forEach((item) => {
          item.list.forEach(({ fdId, fdYear }) => {
            arr.push({ fdId: fdId, fdYear: fdYear, fdMonth: this.month });
          });
        });

        commitStudentToOA(arr)
          .then((res) => {
            let result = res.success;
            layer.msg(`提交OA${result ? "成功" : "失败"}！${res.msg || ""}`, {
              icon: result ? 1 : 2,
              shade: 0.3,
              shadeClose: true,
            });
            this._findListByFdYear("0");
          })
          .catch((err) => {
            layer.msg("提交OA失败！" + (err.msg || ""), {
              icon: 2,
              shade: 0.3,
              shadeClose: true,
            });
          });
      });
    },
    viewPointDetails(data) {
      /*查看得分明细*/
      this.$router.push({
        path:
          "/" +
          this.year +
          "/" +
          this.month +
          "/Trainee/Evaluation/ScoreDetail_all",
      });
    },
    viewGoodsAbsence(fdId) {
      var url =
        "/" +
        this.year +
        "/" +
        this.month +
        "/Trainee/Evaluation/" +
        this.companyGroupId +
        "/addGoodsAbsence";
      this.$router.push({
        path: url,
        query: {
          ablityId: fdId,
          isadd: 0 /*查看*/,
          year: this.year,
          month: this.month,
          companyGroupId: this.companyGroupId,
        },
      });
    },
    toGoodAbsence(item) {
      const { companyGroupId, year, month } = this.$route.params;
      this.companyGroupId = companyGroupId;
      this.year = year;
      this.month = month;
      var ablityId = item.fdId;
      var url =
        "/" +
        this.year +
        "/" +
        this.month +
        "/Trainee/Staff/" +
        this.companyGroupId +
        "/viewGoodAbsence";

      this.$router.push({
        path: url,
        query: {
          ablityId: ablityId,
          year: this.year,
          month: this.month,
          companyGroupId: this.companyGroupId,
        },
      });
    },
    toFixed2(num) {
      if (num) {
        let num_str = num.toString().split(".");
        if (num_str.length == 1) {
          return num_str[0] + ".0";
        } else {
          return num_str[0] + "." + num_str[1].substring(0, 1);
        }
      } else {
        return "";
      }
    },
    initPage() {
      this.body_height = $(window).height();
      const { year, month } = this.$route.params;
      this.mySort = "1";
      this._findListByFdYear("1");
      this.year = year;
      this.month = month;
      if (month == 6) {
        this.checkYear = parseInt(year) - 1 + "-" + year + "年度";
      } else {
        this.checkYear = year + "-" + (parseInt(year) + 1) + "半年度";
      }
    },
    // 新增方法处理公司和职级数据
    isPositionLevel(name) {
      if (!name) return false;

      return (
        name.includes("级") || name.includes("人") || name.includes("经理级")
      );
    },
    getUniqueCompanies(students) {
      if (!students || !students.length) return [];
      const companies = new Set();
      students.forEach((student) => {
        if (student.companyName) companies.add(student.companyName);
      });
      return Array.from(companies);
    },
    getStudentsByCompany(students, companyName) {
      if (!students || !students.length) return [];
      return students.filter((student) => student.companyName === companyName);
    },
    isFirstPositionLevelForCompany(companyName, currentIndex) {
      for (let i = 0; i < currentIndex; i++) {
        const item = this.tableData[i];
        if (this.isPositionLevel(item.companyName)) {
          const companies = this.getUniqueCompanies(item.list);
          if (companies.includes(companyName)) {
            return false;
          }
        } else if (item.companyName === companyName) {
          return false;
        }
      }
      return true;
    },
    getCompanyTotalRows(companyName) {
      let totalRows = 0;

      this.tableData.forEach((item) => {
        if (this.isPositionLevel(item.companyName)) {
          const students = this.getStudentsByCompany(item.list, companyName);
          if (students.length > 0) {
            totalRows += 1 + students.length;
          }
        } else if (item.companyName === companyName) {
          totalRows += item.list ? item.list.length : 0;
        }
      });

      return totalRows;
    },
    adjustTableWidth() {
      setTimeout(() => {
        const headerWrapper = document.querySelector(".table-header-wrapper");
        const bodyWrapper = document.querySelector(".table-body-wrapper");
        const headerTable = document.querySelector(".scroll-table-header");
        const bodyTable = document.querySelector(".table-body-wrapper > table");

        if (!headerWrapper || !bodyWrapper || !headerTable || !bodyTable)
          return;

        // 获取滚动条是否可见
        const scrollbarVisible =
          bodyWrapper.scrollHeight > bodyWrapper.clientHeight;

        // 测量滚动条宽度
        const scrollbarWidth = scrollbarVisible
          ? bodyWrapper.offsetWidth - bodyWrapper.clientWidth
          : 0;

        // 表头宽度设置
        headerWrapper.style.width = scrollbarWidth
          ? `calc(100% - ${scrollbarWidth}px)`
          : "100%";
      }, 50);
    },
  },
  watch: {
    $route: function () {
      this.initPage();
    },
    tableData: {
      handler() {
        this.$nextTick(() => {
          this.adjustTableWidth();
        });
      },
      deep: true,
    },
  },
  mounted() {
    this.initPage();
    $(window).resize(() => {
      this.body_height = $(window).height();
      this.adjustTableWidth();
    });
    this.$nextTick(() => {
      this.adjustTableWidth();

      setTimeout(() => this.adjustTableWidth(), 100);
      setTimeout(() => this.adjustTableWidth(), 500);
      setTimeout(() => this.adjustTableWidth(), 1000);
    });
    window.addEventListener("resize", this.adjustTableWidth);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.adjustTableWidth);
  },
};
</script>

<style>
/* 表格容器样式 */
.result-detail-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.row {
  flex-shrink: 0;
}

.list-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 滚动条处理 */
.table-body-wrapper {
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.table-body-wrapper::-webkit-scrollbar {
  width: 0;
  display: none;
}

.table-header-wrapper {
  width: 100%;
  overflow: hidden;
}

/* 表格样式 */
.scroll-table-header,
.table-body-wrapper > table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
}

.scroll-table-header th,
.table-body-wrapper td {
  box-sizing: border-box;
  white-space: normal;
  word-wrap: break-word;
  word-break: normal;
}

.scroll-table-header {
  position: relative;
  z-index: 2;
  background-color: #fff;
}
</style>
