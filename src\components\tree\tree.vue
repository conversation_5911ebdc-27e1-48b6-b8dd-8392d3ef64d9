<template>
  <div class="tree">
      <tree-node @node-expand="handleNodeExpand" v-for="node in root.childNodes" 
      :key="getNodeKey(node)" 
      :render-content="renderContent"
      :props="props"
      :node="node"></tree-node>
  </div>
</template>

<script>
import TreeNode from "./tree-node";
import TreeStore from "./store";
import emitter from "@/utils/emitter";
import { getNodeKey } from "./util";
export default {
  mixins: [emitter],
  componentName: "Tree",
  data() {
    return {
      // isTree:false,
      root: null,
      store: null,
      currentNode: null,
      treeItems: null,
      checkboxItems: []
    };
  },
  props: {
    data: {
      type: Array
    },
    nodeKey: String,
    checkStrictly:Boolean,
    defaultExpandAll:Boolean,
    showCheckbox: {
      type: Boolean,
      default: false
    },
    props: {
      default() {
        return {
          children: "children",
          label: "label"
        };
      }
    },
    lazy: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    },
    heighlighttCurrent: Boolean,
    load: Function,
    accordion: Boolean,
    expandOnClickNode: {
      type: Boolean,
      default: true
    },
    checkDescendants: {
      type:<PERSON>olean,
      default:false
    },
    autoExpandParent:{
      type:Boolean,
      default:true
    },
    defaultCheckedKeys:Array,
    defaultExpandedKeys:Array,
    emptyText: {
      type: String,
      default: "暂无数据"
    },
    renderContent:Function,
  },
  components: {
    TreeNode
  },
  computed: {
    children: {
      set(value) {
        this.data = value;
      },
      get() {
        return this.data;
      }
    }
  },
  watch:{
    defaultCheckedKeys(newVal){
      this.store.defaultExpandedKeys = newVal;
      this.store.setDefaultCheckedKey(newVal);
    },
    defaultExpandedKeys(newVal){
      this.store.defaultExpandedKeys = newVal;
      this.store.setDefaultExpandedKeys(newVal);
    },
    data(newVal){
      this.store.setData(newVal);
    }
  },
  methods: {
    getNodeKey(node) {
      return getNodeKey(this.nodeKey, node.data);
    },
    handleNodeExpand(nodeData, node, instance) {
      this.broadcast("TreeNode", "tree-node-expand", node);
      this.tree.$emit("node-expand", nodeData, node, instance);
    },
    assignNodeData(nodeData, parent) {
      nodeData.forEach(node => {
        node = Object.assign(node, {
          isLoading: false,
          isExpaned: true,
          isLeaf: false,
          checked: false,
          parent: parent || null
        });
        if (node.children.length != 0) {
          node.children = this.assignNodeData(node.children, node);
        }
      });
      return nodeData;
    },
    setCurrentNode(node) {
      if (!this.nodeKey)
        throw new Error("[Tree] nodeKey is required in setCurrentNode");
      this.store.setUserCurrentNode(node);
    },
    getCheckedNodes(leafOnly) {
      return this.store.getCheckedNodes(leafOnly);
    },
    getCheckedKeys(leafOnly) {
      return this.store.getCheckedKeys(leafOnly);
    },
    setCheckedNodes(nodes, leafOnly) {
      if (!this.nodeKey)
        throw new Error("[Tree] nodeKey is required in setCheckedNodes");
      this.store.setCheckedNodes(nodes, leafOnly);
      const setChildState = function(node) {
        if (node.childNodes.length === 0) return;
        for (let i = 0, j = node.childNodes.length; i < j; i++) {
          let n = node.childNodes[i];
          if(n.disabled) return;
          n.setChecked(true, false);
          if (n.childNodes.length != 0) {
            setChildState(n);
          }
        }
      };
      this.$nextTick(() => {
        nodes.forEach(node => {
          const checkedNode = this.store.getNode(node);
          if (!checkedNode) return;
          setChildState(checkedNode);
        });
      });
    }
  },
  created() {
    this.isTree = true;
    this.store = new TreeStore({
      key: this.nodeKey,//树唯一key值
      data: this.data,
      lazy: this.lazy,
      props: this.props,
      load: this.load,//lazy为true时加载数据方法,lazy为false时不触发
      checkDescendants: this.checkDescendants, //在lazy模式下勾选节点是否展开整个子树
      checkStrictly:this.checkStrictly,
      currentNodeKey: this.currentNodeKey,
      defaultCheckedKeys:this.defaultCheckedKeys,
      defaultExpandedKeys:this.defaultExpandedKeys,
      defaultExpandAll:this.defaultExpandAll
    });
    this.root = this.store.root;
  }
};
</script>

<style lang="scss" scoped>
.tree {
  margin: 0;
  padding: 0;
}
</style>
