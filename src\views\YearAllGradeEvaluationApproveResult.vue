<template>
  <div class="deputy-director-container">
    <div class="row">
      <div class="col-xs-12 text-left">
        <h4 class="col-xs-6" v-if="getCompanyName() == '全集团'">
          【全集团高级经理职等以上】{{ month == 6? '半':'' }}年度管理人员综合能力【评定结果审批】表
        </h4>
        <h4 class="col-xs-6" v-else>
          【{{ getCompanyName() }}】{{ $route.name }}
        </h4>
        <div class="col-xs-6 text-right" style="line-height: 40px">
          <span style="margin-left: 24px; color: red; font-size: 14px">
            <strong> 提醒：</strong>每个职等的 "本期合议调整"
            栏数据录入完成后，记得点击 "保存录入" 一次，以免数据丢失。</span
          >
        </div>

        <div class="col-xs-12 text-left" style="line-height: 40px">
          <div style="display: inline-block; margin: 0 40px">
            <label
              >审批状态：<font color="blue">{{ approveStatus }}</font></label
            >
          </div>
          <div style="display: inline-block; margin: 0 5px">
            <label>排序办法：</label>
          </div>
          <select
            id="select_QueryType"
            style="width: 120px; height: 24px; line-height: 24px"
          >
            <option :value="''">请选择</option>
            <option value="1">按评定结果</option>
            <option value="2">按合议结果</option>
            <option value="4">按审核结果</option>
            <option value="3">按审批结果</option>
          </select>
          <button
            class="btn btn-primary btn-xs"
            @click="_findCompanyLastResult()"
          >
            查询
          </button>

          <div
            style="
              float: right;
              white-space: nowrap;
              display: flex;
              align-items: center;
            "
          >
            <button class="btn btn-primary btn-xs" @click="showDescrAction">
              操作说明
            </button>
            <button class="btn btn-primary btn-xs" @click="showDescrPoint">
              评定说明
            </button>
            <button class="btn btn-primary btn-xs" @click="showBaseLevelDialog">
              评定级别、职级标准
            </button>

            <button
              type="button"
              class="btn btn-primary btn-xs"
              @click="sheetIt"
            >
              导出Excel
            </button>
            <el-upload
              :show-file-list="false"
              action=""
              :headers="headers"
              :before-upload="BeforeUpload"
              :http-request="Upload"
              style="display: inline-block; margin-right: 10px"
            >
              <template #trigger>
                <button type="button" class="btn btn-primary btn-xs">
                  导入模板
                </button>
              </template>
            </el-upload>

            <button
              class="btn btn-primary btn-xs"
              :disabled="disabledButton"
              @click="_updateAdjustLevel"
              v-if="
                $store.state.doubleCol.arrAuths.point_ablity_updateAdjustLevel
              "
            >
              保存录入
            </button>
            <button
              class="btn btn-primary btn-xs"
              :disabled="disabledButton"
              @click="_updateAdjustLevelSendOA"
              v-if="
                $store.state.doubleCol.arrAuths
                  .point_ablity_updateAdjustLevelSendOA
              "
            >
              提交OA
            </button>
            <button
              class="btn btn-primary btn-xs"
              @click="$router.push(`/${$route.params.year}/${month}/menu/1`)"
            >
              返回目录
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-xs-12" style="width: auto; padding: 0">
        <div id="table" class="list-table">
          <div
            class="scroll-table"
            style="width: 100%; height: 100%; overflow-x: hidden"
          >
            <table class="scroll-table-header table-bordered">
              <colgroup>
                <col width="40" />
                <col width="80" />
                <col width="60" />
                <col width="40" />
                <col width="40" />
                <col width="40" />
                <col width="100" />
                <col width="80" />
                <col width="80" />
                <col width="40" />

                <!-- <col width="70" />
                <col width="70" />
                <col width="70" />
                <col width="70" />
                <col width="70" v-if="managerType=='0'"/>
                <col width="70" v-if="managerType=='0'"/> -->
                <col width="55" />
                <col width="50" />
                <col width="40" />
                <col width="40" />
                <col width="55" />
                <col width="55" />

                <col width="40" />
                <col width="40" />
                <col width="40" />
                <col width="40" />
                <col width="40" />
                <col width="40" />
                <col width="40" />
                <col width="100" />
              </colgroup>
              <thead>
                <tr>
                  <th colspan="3">系统维护人员：</th>
                  <th
                    :colspan="managerType == '0' ? '21' : '21'"
                    style="text-align: left; padding-left: 10px"
                  >
                    陈文学、卢业辉
                  </th>
                </tr>
                <tr>
                  <th rowspan="2">序号</th>
                  <th rowspan="2">
                    {{ managerType == "0" ? "公司" : "厂/部门" }}
                  </th>
                  <th rowspan="2">姓名</th>
                  <th rowspan="2">性别</th>
                  <th rowspan="2">年龄</th>
                  <th rowspan="2">学历</th>
                  <th colspan="3">任职信息</th>
                  <th rowspan="2">上期职级</th>

                  <!-- <th :colspan="managerType=='0' ? '6': '4'">级别评定人</th> -->
                  <th colspan="2">评定分值</th>
                  <th colspan="2">评定结果</th>
                  <th colspan="2">合议结果</th>
                  <th colspan="2">审核结果</th>
                  <th colspan="2">审批结果</th>
                  <th rowspan="2">优或标杆</th>
                  <th rowspan="2">评分明细</th>
                  <th rowspan="2">工作亮点</th>
                  <th rowspan="2">备注</th>
                </tr>
                <tr>
                  <th>职务</th>
                  <th>职等</th>
                  <th>时长</th>
                  <!-- <th>直接领导</th>
                  <th>间接领导</th>
                  <th>关联业务领导</th>
                  <th>关联业务领导</th>
                  <th v-if="managerType=='0'">关联业务领导</th>
                  <th v-if="managerType=='0'">关联业务领导</th>                 -->
                  <th>最高职务评分</th>
                  <th>合议分值</th>
                  <th>级别</th>
                  <th>职级</th>
                  <th>级别</th>
                  <th>职级</th>
                  <th>级别</th>
                  <th>职级</th>
                  <th>级别</th>
                  <th>职级</th>
                </tr>
              </thead>
            </table>
            <div
              class="scroll-table-body"
              :style="{
                overflow: 'auto',
                maxHeight: body_height - 240 + 'px',
                'overflow-y': 'scroll',
                'overflow-x': 'hidden',
              }"
            >
              <table
                style="
                  margin: 0px 10px 0 0;
                  position: relative;
                  font-size: 13px;
                  float: left;
                  border: none;
                "
                class="table table-bordered table-hover table-striped"
                v-for="(data, index) in tableData"
                :key="index"
              >
                <colgroup>
                  <col width="40" />
                  <col width="80" />
                  <col width="60" />
                  <col width="40" />
                  <col width="40" />
                  <col width="40" />
                  <col width="100" />
                  <col width="80" />
                  <col width="80" />
                  <col width="40" />

                  <!-- <col width="70" />
                  <col width="70" />
                  <col width="70" />
                  <col width="70" />
                  <col width="70" v-if="managerType=='0'"/>
                  <col width="70" v-if="managerType=='0'"/> -->
                  <col width="55" />
                  <col width="50" />
                  <col width="40" />
                  <col width="40" />
                  <col width="55" />
                  <col width="55" />

                  <col width="40" />
                  <col width="40" />
                  <col width="40" />
                  <col width="40" />
                  <col width="40" />
                  <col width="40" />
                  <col width="40" />
                  <col width="100" />
                </colgroup>
                <thead>
                  <tr>
                    <td
                      style="
                        background: #fff;
                        font-weight: bold;
                        padding: 10px 10px 1px 10px;
                      "
                      :colspan="managerType == '0' ? '29' : '27'"
                    >
                      <div
                        style="
                          text-align: left;
                          width: 50%;
                          height: 100%;
                          float: left;
                          vertical-align: bottom;
                        "
                      >
                        {{ data.gradeName }}
                      </div>

                      <div
                        style="text-align: right; width: 620px; float: right"
                      >
                        <!--{{ data.rateControl }}<br />{{ data.rateControl2 }}-->
                        <button
                          class="btn btn-primary btn-xs"
                          style="padding: 2px 5px 1px 5px"
                          @click="
                            showGradePointDetails(data.gradeId, data.gradeName)
                          "
                        >
                          职等评分明细
                        </button>
                        <button
                          class="btn btn-primary btn-xs"
                          style="padding: 2px 5px 1px 5px"
                          @click="
                            showGradeRateDetails(data.gradeId, data.gradeName)
                          "
                        >
                          级别、职级占比汇总
                        </button>
                      </div>
                    </td>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="(item, i_index) in data.list"
                    :key="i_index"
                    @dblclick="
                      data.confirmLeaderId || item.adjust_status <= 11
                        ? showRemarkDialog(item, index)
                        : null
                    "
                  >
                    <td>{{ i_index + 1 }}</td>
                    <td style="text-align: left">{{ item.dept_name || "" }}</td>
                    <td>{{ item.user_name || "" }}</td>
                    <td>{{ item.gender || "" }}</td>
                    <td>{{ item.age || "" }}</td>
                    <td>{{ item.degree || "" }}</td>
                    <td style="text-align: left">{{ item.job_name || "" }}</td>
                    <td>{{ item.grade_name || "" }}</td>
                    <td>
                      {{
                        Math.floor(item.work_long / 12) <= 0 ||
                        item.work_long == null
                          ? ""
                          : Math.floor(item.work_long / 12) + "年"
                      }}{{
                        item.work_long % 12 === 0
                          ? ""
                          : (item.work_long % 12) + "个月"
                      }}
                    </td>
                    <td>
                      {{
                        (item.studentJobCategory || "") == "新晋升" ||
                        (item.studentJobCategory || "") == "首次评定"
                          ? item.studentJobCategory
                          : item.pre_last_result || ""
                      }}
                    </td>

                    <td>
                      {{
                        ["标杆", "优"].indexOf(item.standard_level) == -1
                          ? item.max_grade_point || ""
                          : parseInt(item.avg_point || 0)
                      }}
                    </td>
                    <td>{{ item.meetingPoint2 || item.avg_point || "" }}</td>
                    <!-- <td>{{(item.resultRemark||'')=="新晋升评定"?"":(item.pre_meeting_level ||'')}}</td>
                                  <td>{{(item.resultRemark||'')=="新晋升评定"?"":(item.pre_last_result||'')}}</td> -->
                    <td>
                      {{ item.status == 30 ? item.meeting_level || "" : "" }}
                    </td>
                    <td>
                      {{ item.status == 30 ? item.last_result || "" : "" }}
                    </td>
                    <td style="padding: 0">
                      <select
                        v-if="item.adjust_status != 30"
                        :disabled="item.adjust_status == 30"
                        :style="{
                          'border-color':
                            item.meeting_level &&
                            item.meeting_level !== item.adjustLevel
                              ? 'red'
                              : '',
                          width: '92%',
                          height: '24px',
                        }"
                        v-model="item.adjustLevel"
                        @change="itemGradeLevel(item)"
                      >
                        <option value=""></option>
                        <template v-for="(grade, index_g) in item.gradeList">
                          <option
                            :key="index_g"
                            v-if="grade.positionGradeName == item.grade_name"
                            :value="grade.level"
                          >
                            {{ grade.level }}
                          </option>
                        </template>
                      </select>
                      <span v-if="item.adjust_status == 30">{{
                        item.adjustLevel
                      }}</span>
                    </td>
                    <td style="padding: 0">
                      <select
                        v-if="item.adjust_status != 30"
                        :disabled="item.adjust_status == 30"
                        :style="{
                          'border-color':
                            item.last_result &&
                            item.last_result !== item.adjustResult
                              ? 'red'
                              : '',
                          width: '92%',
                          height: '24px',
                        }"
                        v-model="item.adjustResult"
                      >
                        <option value=""></option>
                        <option
                          v-for="(gradeLevel, index_g_l) in item.gradeLevelList"
                          :key="index_g_l"
                        >
                          {{ gradeLevel.levelGrade }}
                        </option>
                      </select>
                      <span v-if="item.adjust_status == 30">{{
                        item.adjustResult
                      }}</span>
                    </td>
                    <td>{{ item.upcheckLevel || "" }}</td>
                    <td>{{ item.upcheckResult || "" }}</td>
                    <td>{{ item.bossLevel || "" }}</td>
                    <td>{{ item.bossResult || "" }}</td>
                    <td>{{ item.standard_level || "" }}</td>
                    <td>
                      <span v-if="item.is_standard != true">
                        <a
                          @click="
                            showUserPointDetails(item.user_id, item.user_name)
                          "
                        >
                          <img
                            src="../assets/images/action_dtl.png"
                            alt=""
                          /> </a
                      ></span>
                    </td>
                    <td>
                      <el-button
                        @click="
                          $router.push(
                            'positionGradeId/summary?fdId=' +
                              item.fdId +
                              '&name=' +
                              item.user_name
                          )
                        "
                        type="text"
                        size="small"
                        >查看</el-button
                      >
                    </td>
                    <td style="text-align: left">
                      <a @click="item.remark == '' ? null : showRemark(item)">
                        {{
                          item.remark == ""
                            ? null
                            : item.remark.length < 11
                            ? item.remark
                            : item.remark.substring(0, 10) + "..."
                        }}</a
                      >
                    </td>
                  </tr>
                </tbody>
              </table>
              <span v-if="tableDataLength <= 0">
                {{ tableDataLength == -1 ? "加载中..." : "暂无数据" }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import baseLevelList from "./BaseLevel_list";
import AblityPointDetails from "./AssessmentComprehensiveAbility/AblityPointDetails";
import AdjustApprove from "./ComprehensiveCapacityRankGrade/AdjustApprove";
import GradeRateDetails from "./ComprehensiveCapacityRankGrade/GradeRateDetails";
import GradePointDetails from "./ComprehensiveCapacityRankGrade/GradePointDetails";
import UserPointDetails from "./ComprehensiveCapacityRankGrade/UserPointDetails";
import { getCookie, TokenKey } from "@/utils/Cookie";

import {
  findCompanyLastResult,
  findListByCompanyGroupId,
  findDetailsListByCompanyGroupId,
  updateAdjustLevel,
  updateAdjustLevelSendOA,
  importAdjustResult,
} from "@/api";
export default {
  data() {
    return {
      isLoading: true,
      tableData: [],
      gradeLevelList: [],
      companyGroupId: "",
      year: "",
      month: this.$route.meta.month,
      managerType: "1",
      body_height: 0,
      currentGrade: {},
      headers: {
        [TokenKey]: getCookie(TokenKey),
      },
      newFile: new FormData(),
      tableDataLength: -1,
    };
  },
  computed: {
    approveStatus() {
      if (this.tableData && this.tableData.length != 0) {
        if (
          this.tableData[0].list[0].adjust_status == "" ||
          this.tableData[0].list[0].adjust_status == 10
        ) {
          return "未提交";
        } else if (this.tableData[0].list[0].adjust_status == "11") {
          return "提交OA";
        } else if (this.tableData[0].list[0].adjust_status == "20") {
          return "审批中";
        } else if (this.tableData[0].list[0].adjust_status == "30") {
          return "已审批";
        }
      } else {
        return "未知状态";
      }
    },
    disabledButton() {
      if (this.isLoading) {
        return true;
      } else if (this.tableData && this.tableData.length != 0) {
        return this.tableData[0].list[0].adjust_status != ""
          ? this.tableData[0].list[0].adjust_status != "10"
            ? true
            : false
          : false;
      }
      return true;
    },
  },
  methods: {
    itemGradeLevel(data, isInit) {
      if (!isInit) {
        data.adjustResult = "";
      }
      let result = data.gradeList.find((grade) => {
        return (
          grade.level == data.adjustLevel &&
          grade.positionGradeName == data.grade_name
        );
      });
      result = result || {};
      data.gradeLevelList = this.gradeLevelList.filter((item) => {
        return result.fdId == item.baseLevelId;
      });
    },
    _findListByCompanyGroupId(list) {
      findDetailsListByCompanyGroupId({
        CompanyGroupId: this.companyGroupId,
        Year: this.year,
        Month: this.$route.meta.month,
      }).then((res1) => {
        this.isLoading = false;
        let gradeLevelList = res1.data;
        this.gradeLevelList = gradeLevelList;
        findListByCompanyGroupId({
          CompanyGroupId: this.companyGroupId,
          Year: this.year,
          Month: this.$route.meta.month,
        }).then((res) => {
          let gradeList = res.data || [];
          list.forEach((item) => {
            if (item.list) {
              item.list.forEach((item2) => {
                item2.gradeList = gradeList;
                if (item2.adjustLevel) {
                  this.itemGradeLevel(item2, true);
                } else {
                  item2.gradeLevelList = [];
                }
              });
            }
          });
          this.tableData = list;
          this.tableDataLength = this.tableData.length;
        });
      });
    },
    getCompanyName() {
      let companyName = Utils.getCompanyNameByGroupId(this.companyGroupId);
      if (this.$route.meta.managerType === 0) {
        companyName = "全集团";
      }
      return companyName;
    },
    getCommitData() {
      let arr = [];
      this.tableData.forEach((data) => {
        arr = arr.concat(data.list);
      });
      return arr;
    },
    _updateAdjustLevelSendOA() {
      if (this.disabledButton) {
        return;
      }
      layer.confirm(
        "是否确定提交OA？",
        {
          title: "提示",
          btn: ["确定", "取消"], //按钮
        },
        (index) => {
          this.isLoading = true;
          updateAdjustLevelSendOA(this.getCommitData()).then((res) => {
            let result = res.success;
            result ? this._findCompanyLastResult() : null;
            layer.msg(`提交OA${result ? "成功" : "失败"}`, {
              icon: result ? 1 : 2,
              shade: 0.3,
              shadeClose: true,
            });
          });
        }
      );
    },
    _updateAdjustLevel() {
      if (this.disabledButton) {
        return;
      }
      this.isLoading = true;
      updateAdjustLevel(this.getCommitData()).then((res) => {
        let result = res.success;
        result ? this._findCompanyLastResult() : null;
        layer.msg(`提交${result ? "成功" : "失败"}`, {
          icon: result ? 1 : 2,
          shade: 0.3,
          shadeClose: true,
        });
      });
    },
    _findCompanyLastResult() {
      /*先判断有权限才可以查询(modify by huangdh@2019-05-24)*/
      if (
        this.$store.state.doubleCol.arrAuths.point_ablity_findCompanyLastResult
      ) {
        let queryType = $("#select_QueryType").val();
        console.log("queryType：" + queryType);
        if (queryType == "") {
          queryType = 3;
        }
        findCompanyLastResult({
          companyGroupId:
            this.$route.meta.managerType === 0
              ? this.$route.meta.managerType
              : this.companyGroupId,
          year: this.year,
          month: this.$route.meta.month,
          queryType: queryType,
        }).then((res) => {
          if (!res.success) return;
          this._findListByCompanyGroupId(res.data ? res.data : []);
        });
      } else {
        layer.msg(`对不起，您没有权限查询本界面.`, {
          icon: 2,
          shade: 0.3,
          shadeClose: true,
        });
      }
    },

    showBakRemark(item) {
      /*评定事例依据*/
      if (item.is_standard) {
        var remark = item.bak_remark || "";
        if (remark == "") {
          remark = "无评定.";
        }
        Utils.layerBox.layerDialogOpen({
          title: "评定事例依据",
          area: ["600px", "300px"],
          btn: ["关闭"],
          content:
            `<div style="padding:10px 20px;margin:0 auto;">` +
            remark +
            `</div>`,
        });
      } else {
        this.showAblityPointDetails(item.fdId, item.user_name);
      }
    },
    showAdjustApprove(item) {
      /*审批结果调整（不要这个过程）*/
      let layerDialog = Utils.layerBox.layerDialogOpen({
        title: "审批结果调整",
        btn: [],
        maxmin: false, //开启最大化最小化按钮
        area: ["760px", "300px"],
      });
      this.dialogComponent = Utils.layerBox.layerLoadVueComponent({
        layer: layerDialog,
        component: AdjustApprove,
        methods: {
          doConfirm: (params) => {
            layer.close(layerDialog);
          },
          doClose: () => {
            layer.close(layerDialog); //关闭对话框
          },
        },
        props: {
          ablityId: item.fdId,
        },
      });
    },
    /*XX职等【级别、职级】占比汇总表*/
    showGradeRateDetails(gradeId, gradeName) {
      let value = gradeName.split("：")[0];
      value = value.substring(0, value.length - 1);
      let layerDialog = Utils.layerBox.layerDialogOpen({
        title: value + "职等【级别、职级】占比汇总表",
        btn: [],
        maxmin: false, //开启最大化最小化按钮
        area: ["800px", "420px"],
      });
      this.dialogComponent = Utils.layerBox.layerLoadVueComponent({
        layer: layerDialog,
        component: GradeRateDetails,
        methods: {
          doConfirm: (params) => {
            layer.close(layerDialog);
          },
          doClose: () => {
            layer.close(layerDialog); //关闭对话框
          },
        },
        props: {
          fdYear: this.year,
          fdMonth: this.month,
          companyGroupId: this.companyGroupId,
          positionGradeId: gradeId,
        },
      });
    },
    /*XX职等评分明细表*/
    showGradePointDetails(gradeId, gradeName) {
      let value = gradeName.split("：")[0];
      value = value.substring(0, value.length - 1);
      let layerDialog = Utils.layerBox.layerDialogOpen({
        title: value + "职等【评定人及评分】明细表",
        btn: [],
        maxmin: false, //开启最大化最小化按钮
        area: ["1000px", "600px"],
      });
      this.dialogComponent = Utils.layerBox.layerLoadVueComponent({
        layer: layerDialog,
        component: GradePointDetails,
        methods: {
          doConfirm: (params) => {
            layer.close(layerDialog);
          },
          doClose: () => {
            layer.close(layerDialog); //关闭对话框
          },
        },
        props: {
          fdYear: this.year,
          fdMonth: this.month,
          companyGroupId: this.companyGroupId,
          grade: gradeId,
        },
      });
    },
    /*XX用户个人评分明细表*/
    showUserPointDetails(userId, userName) {
      let layerDialog = Utils.layerBox.layerDialogOpen({
        title: userName + "【评定人及评分】明细表",
        btn: [],
        maxmin: false, //开启最大化最小化按钮
        area: ["700px", "300px"],
      });
      this.dialogComponent = Utils.layerBox.layerLoadVueComponent({
        layer: layerDialog,
        component: UserPointDetails,
        methods: {
          doConfirm: (params) => {
            layer.close(layerDialog);
          },
          doClose: () => {
            layer.close(layerDialog); //关闭对话框
          },
        },
        props: {
          fdYear: this.year,
          fdMonth: this.month,
          userId: userId,
        },
      });
    },

    showRemarkDialog(item, index_a) {
      let that = this;
      Utils.layerBox.layerDialogOpen({
        title: "录入备注",
        area: ["450px", "220px"],
        content:
          `<div style="width:100%;height:100%;text-align:center;line-height:32px;box-sizing: border-box;padding: 12px 0">
          <div class="row" style="margin:0;text-align:center;" id="mergeSelect">
            <span><textarea id="txt_remark" type="text" style="width:95%;height:100px;">` +
          item.remark +
          `</textarea></span>
          </div>
        </div>`,
        success: function (layero) {
          // console.log(layero);
        },
        btn1: function (index, val) {
          that.tableData.forEach((j_item, j_index) => {
            if (j_index === index_a) {
              j_item.list.forEach((k_item, k_index) => {
                if (item.fdId === k_item.fdId) {
                  item.remark = $("#txt_remark").val();
                }
              });
            }
          });
          layer.close(index);
        },
      });
    },
    showRemark(item) {
      var remark = item.remark || "";
      Utils.layerBox.layerDialogOpen({
        title: "查看备注",
        area: ["450px", "220px"],
        btn: ["关闭"],
        content:
          `<div style="padding:10px 20px;margin:0 auto;">` + remark + `</div>`,
      });
    },
    showAblityPointDetails(ablityId, userName) {
      let layerDialog = Utils.layerBox.layerDialogOpen({
        title: "评定事例依据",
        btn: [],
        maxmin: false, //开启最大化最小化按钮
        area: ["760px", "420px"],
        btn1: (index, layero) => {},
        btn2: (index, layero) => {},
      });
      this.dialogComponent = Utils.layerBox.layerLoadVueComponent({
        layer: layerDialog,
        component: AblityPointDetails,
        methods: {
          doConfirm: (params) => {
            layer.close(layerDialog);
          },
          doClose: () => {
            layer.close(layerDialog); //关闭对话框
          },
        },
        props: {
          ablityId: ablityId,
          userName: userName,
        },
      });
    },
    showDescrPoint() {
      Utils.layerBox.layerDialogOpen({
        title: "管理人员综合能力评定流程及办法说明",
        area: ["900px", "550px"],
        btn: [],
        content: `<div style="padding:10px 20px 0 20px;margin:0 auto;">
          <p style="line-height:24px;">
            <p><b>一、综合能力级别和职级定义</b></p>\
            <p>1.级别：体现管理人员在同职等人员中的综合能力水平，每个职等人员均分为A、B、C三级。</p>\
            <p>2.职级：体现管理人员在当年度工作中的工作态度、工作投入程度以及工作效率，在个人综合能力级别的基础上，由直接领导进行评定。<p>\
            <p><b>二、综合能力评定</b></p>\
            <p><b>（一）评定类型</b></p>\
            <p>1.年度评定：每年评定1次，每年11月1日开始评定，12月31日完成评定审批；</p>\
            <p>2.半年度评定：每年评定1次，每年5月1日开始评定，6月30日完成评定审批；</p>\
            <p>3.未能按时完成的，对各公司总经理和人资领导作工作失职处理。</p>\
            <p><b>（二）评定目的</b></p>\
            <p>1.年度评定目的</p>\
            <p>（1）作为下一年度职务晋升、降职及调动的依据之一。</p>\
            <p>（2）作为下一年度薪酬职级及职级工资的执行依据。</p>\
            <p>2.半年度评定目的：只作为人资部对员工职务晋升、降职及调动的依据之一，与员工的薪酬职级和职级工资执行无关。</p>\
            <p><b>三、年度评定</b></p>\
            <p><b>（一）主要内容</b></p>\
            <p>1.级别评定</p>\
            <p>（1）评定单位划分</p>\
            <p>（2）级别评定人确定</p>\
            <p>（3）优级和标杆人员评定</p>\
            <p>（4）级别评定及合议</p>\
            <p>2.职级评定</p>\
            <p>（1）直接领导进行职级评定</p>\
            <p>（2）审核人、审定人审定职级</p>\
            <p>3.评定结果合议及报批</p>\
            <p><b>（二）评定单位</b></p>\
            <p>1.原则：以各职等人员划分评定单位。</p>\
            <p>2.厂长以下、值班长以上：每个职等作为一个评定单位。</p>\
            <p>3.主任以下、科员以上：</p>\
            <p>（1）纸业公司：分为生产一线、生产支持、生产辅助三个板块，按板块划定评定单位，如果板块人数很少，可以考虑合并部分板块进行评定。</p>\
            <p>（2）林业公司：部分职等分为经营区和林场，按板块划定评定单位，如果板块人数很少，可以考虑合并部分板块进行评定。</p>\
            <p><b>（三）级别评定人确定</b></p>\
            <p>1.评定人资格</p>\
            <p>（1）为评定对象的直接领导、间接领导和业务关联领导。</p>\
            <p>（2）业务关联领导一定是对评定对象了解的领导。</p>\
            <p>（3）级别评定人一定是领导职务人员。</p>\
            <p>2.评定人人数：每个评定对象原则上3人，不足的可以2人，极个别只有1人对评定对象了解的，则允许1人。</p>\
            <p>（四）<b>优级和标杆人员评定</b></p>\
            <p><b>1.优级人员评定</b></p>\
            <p>（1）评定目的：提拔任用，而不是为了加薪酬。</p>\
            <p>（2）评优级条件</p>\
            <p>①特殊贡献：创新、创造与突破的业绩，每年必须有3件以上（3件以上是初定标准）。</p>\
            <p>②业务能力：优于上一等级30%以上的人员（初定标准）。</p>\
            <p>③特别要求：自己的责任范围当年度没有出现重大的设备事故、质量事故、安全事故和渎职行为。</p>\
            <p>（3）特殊贡献定义</p>\
            <p>①个人的原始创新、创造与突破的成果和推广。</p>\
            <p>②作为已取得成果或推广创新、创造项目实施的主要负责人。</p>\
            <p>③说明：已经有过、发生过、出现过、解决过的事例原则上不算创新、创造与突破。</p>\
            <p>（4）业务能力</p>\
            <p>①定义：指的是日常企业生产运营与管理的业务熟悉和掌握程度，并且根据公司新业务、新流程、新标准、新的思想要求不断地提高、进步。</p>\
            <p>②业务能力优于上一等级30%以上理由：因为上一等级的人员会存在因原来的评定、晋升原因或个人能力停滞等造成能力低，所以要比该部分人员优秀才具备评优和晋升的条件。</p>\
            <p>（4）评优级人员人数</p>\
            <p>①在同一评定单位原则上不超过15%，特殊情况要特别说明。</p>\
            <p>②说明：有人员符合条件才能评优，没有人员符合可以不评。</p>\
            <p>（5）评优级人员待遇</p>\
            <p>①职务晋升：优级人员原则上要申请晋升考核，但是否发起晋升考核需申请审批后再确定，因为有的优级人员虽然表现好但未必具备晋升的潜力和条件。</p>\
            <p>②优级薪酬待遇：</p>\
            <p>a.在同职等中特别设立了优级薪酬，不具备晋升条件的人员通过享受优级薪酬提高工资来达到激励的目的。</p>\
            <p>b.说明：现有薪酬体系设立了优级薪酬，但保持了同等级的级差额度一致，以后根据评优人员的情况，再考虑有部分不具备晋升条件且以后可能都不晋升的是否需加大优级的级差，特别像厂长、值班长、班长级别。</p>\
            <p><b>2.标杆人员评定</b></p>\
            <p>（1）评定目的：作为评定人进行级别评定时评分的参考标准。</p>\
            <p>（2）标杆人员资格：需是该评定单位中综合能力最好的人员。</p>\
            <p>（3）标杆人数：每个评定单位评出至少1人。</p>\
            <p>（4）评定办法：</p>\
            <p>①评定时间：在评定优级人员时一起组织评定。</p>\
            <p>②评定方式：通过推荐和评议选出标杆，由参与级别评定的人员推荐各自符合条件的人员，并对被推荐人的有关工作表现进行阐述，以便其他评定人进行评议。</p>\
            <p>③评定得分：评出标杆人员后，根据其实际的综合能力确定得分，分值最高为85分，能力达不到可80分、75分等。</p>\
            <p>（五）<b>级别评定及合议</b></p>\
            <p>1.级别评定：由级别评定人在自己的OA工作台对评定对象进行评定打分。</p>\
            <p>2.级别评定合议：</p>\
            <p>（1）按各评定对象的最终得分从高到低进行排名，然后根据各级别人数控制比例，确定各评定对象的级别。</p>\
            <p>（2）对于部分最终得分相同其处于2个级别的，以最高领导职务的评定作为参考依据进行合议确定。</p>\
            <p><b>（六）职级评定</b></p>\
            <p>1.职级评定人：评定对象的直接领导。</p>\
            <p>2.评定依据：根据评定对象在工作中的工作态度、工作投入程度以及工作效率进行评定。</p>\
            <p>3.评定办法：</p>\
            <p>（1）由直接领导在级别评定的基础上，进行职级评定。</p>\
            <p>（2）同一级别的人员应该按照职级均分的原则，剩余不能均分的视其综合能力评定而定，如：B级有4人，则应该B+、B、B-职级各1人，剩余1人根据实际情况评定。</p>\
            <p>4.职级审核、审定：</p>\
            <p>（1）直接领导评定完成后，传审核人审核、审定人审定。</p>\
            <p>（2）在审核和审定过程中，对于直接领导未遵守同级别的人员职级应均分的原则，可进行调整。</p>\
            <p><b>（七）评定结果合议及报批</b></p>\
            <p>1.合议人员：以公司为单位进行汇总合议，由人资部负责人组织公司各体系领导进行合议。</p>\
            <p>2.合议原则：通过对同一级别人员的综合能力进行横向对比，对于能力评定不合理或职级比例控制不合理的部分人员进行合议调整。</p>\
            <p>3.合议结果审批流程：人资部考核专员发起→各体系领导核签→总经理（副总裁）审核→集团分管人资总裁助理复核→董事长审批。</p>\
            <p><b>四、半年度评定</b></p>\
            <p><b>（一）主要内容</b></p>\
            <p>1.职级评定</p>\
            <p>（1）直接领导在年度评定级别的基础上直接进行职级评定。</p>\
            <p>（2）审核人、审定人审定职级。</p>\
            <p>2.评定结果合议及报批</p>\
            <p><b>（二）职级评定：同年度评定。</b></p>\
            <p><b>（三）评定结果合议及报批：同年度评定。</b></p></div>`,
      });
    },
    showDescrAction() {
      Utils.layerBox.layerDialogOpen({
        title: "操作说明",
        area: ["750px", "400px"],
        btn: [],
        content: `<div style="padding:10px 20px 0 20px;margin:0 auto;">
          <p style="line-height:24px;">
            <p>1、审批结果调整：审批结果的级别、职级默认为【审核结果】的级别、职级，若需要修改审批结果，点击【审批结果】"调整"栏进行调整。</p>\
            <p>2、排序办法：默认【按审批结果】排序，若需调整排序办法，请点击左上角的"排序办法选择"，点击后会下拉显示3个选项，分别为"按评定结果"、"按合议结果"、"按审核结果"、"按审批结果"，点击某个下拉选项后，表单将会按照选中的选项进行排序，排序规则为先按照"级别（优、A、B、C）"排序，然后再按照"职级（+、-）"排序；<p>\
            <p>3、级别、职级占比汇总：每个职等的右上角设置了"级别、职级占比汇总"按键，点击后将会弹出该职能的《级别、职级占比汇总表》。</p>\
            <p>4、评定说明：点击"评定说明"按键，将弹出"评定说明"框。</p>\
            <p>5、保存录入：点击"保存录入"，可以对整个表单数据进行保存。</p>\
            </div>`,
      });
    },
    showBaseLevelDialog() {
      let name = this.getCompanyName();
      let layerDialog = Utils.layerBox.layerDialogOpen({
        title:
          "<strong>" +
          name +
          `管理人员评定<font color='red'>级别职级</font>标准表</strong>`,
        btn: [],
        maxmin: false, //开启最大化最小化按钮
        area: ["650px", "550px"],
      });
      this.dialogComponent = Utils.layerBox.layerLoadVueComponent({
        layer: layerDialog,
        component: baseLevelList,
        methods: {
          doConfirm: (params, action) => {},
          doClose: () => {},
        },

        //传递本组件的参数给对话框组件，对话框组件通过props属性params获取值,例如下面这个val属性取值：this.params.val
        props: {
          companyGroupId:
            this.$route.meta.managerType === 0 ? 0 : this.companyGroupId,
          year: this.year,
          month: this.$route.meta.month,
        },
      });
    },
    BeforeUpload(file) {
      if (file) {
        this.newFile = new FormData();
        this.newFile.append("file", file);
        return true;
      } else {
        return false;
      }
    },
    Upload() {
      const loading = this.$loading({
        lock: true,
        text: "导入中...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });

      // 准备FormData对象
      const formData = this.newFile;
      formData.append("companyGroupId", this.companyGroupId);
      formData.append("year", this.year);
      formData.append("month", this.month);

      // 使用API方法
      importAdjustResult(formData)
        .then((res) => {
          loading.close();
          if (res.success) {
            layer.msg("导入成功", { icon: 1, shade: 0.3, shadeClose: true });
            this._findCompanyLastResult(); // 重新加载数据
          } else {
            layer.msg("导入失败：" + (res.msg || ""), {
              icon: 2,
              shade: 0.3,
              shadeClose: true,
            });
          }
        })
        .catch((err) => {
          loading.close();
          layer.msg("导入异常：" + (err.msg || err.message || ""), {
            icon: 2,
            shade: 0.3,
            shadeClose: true,
          });
        });
    },
    sheetIt() {
      // 临时修改DOM以适应导出
      const selects = document.querySelectorAll("select");
      const selectValues = [];

      // 记录所有select元素的位置和当前值
      selects.forEach((select, index) => {
        const selectedOption = select.options[select.selectedIndex];
        const selectedText = selectedOption ? selectedOption.textContent : "";
        const parent = select.parentNode;
        const nextSibling = select.nextSibling;

        // 保存更多信息以便准确恢复
        selectValues.push({
          element: select,
          parent: parent,
          nextSibling: nextSibling,
          selectedText: selectedText,
          selectedIndex: select.selectedIndex,
        });

        // 创建一个span来替换select
        const span = document.createElement("span");
        span.textContent = selectedText;
        span.setAttribute("data-select-index", index); // 添加索引以便恢复时识别
        parent.replaceChild(span, select);
      });

      // 执行导出
      this.exportExcelOne.exportExcel(
        `【${this.getCompanyName()}】年度管理人员综合能力评定结果审批.xlsx`,
        "#table"
      );

      // 导出完成后直接恢复原始元素
      setTimeout(() => {
        document.querySelectorAll("span[data-select-index]").forEach((span) => {
          const index = parseInt(span.getAttribute("data-select-index"));
          const selectData = selectValues[index];
          if (selectData) {
            // 直接恢复原始的select元素
            span.parentNode.replaceChild(selectData.element, span);
            // 确保恢复select的选中值
            selectData.element.selectedIndex = selectData.selectedIndex;
          }
        });

        // 确保所有select都已恢复，以防有遗漏
        if (selectValues.length > 0) {
          this._findCompanyLastResult(); // 如果恢复有问题，重新加载数据以确保页面正确
        }
      }, 1000);
    },
    initPage() {
      this.body_height = $(window).height();
      const { companyGroupId, year, managerType } = this.$route.params;
      this.companyGroupId = companyGroupId;
      this.year = year;
      this.managerType = this.$route.meta.managerType;
      console.log("managerType:" + this.managerType);

      this._findCompanyLastResult();
    },
  },
  watch: {
    $route: function () {
      this.initPage();
    },
  },
  mounted() {
    this.initPage();
    //console.log($store.state.doubleCol.arrAuths.point_ablity_updateAdjustLevel);
    $(window).resize(() => {
      this.body_height = $(window).height();
    });

    // 隐藏上传文件按钮
    setTimeout(() => {
      document.getElementsByClassName("el-upload__input")[0].style =
        "display:none";
    }, 100);
  },
};
</script>

<style scoped>
select[disabled="disabled"] {
  background: #e1e1e1;
}

/* 隐藏上传文件按钮 */
:deep(.el-upload__input) {
  display: none !important;
}

/* 确保按钮样式统一且在一行显示 */
.btn-primary.btn-xs {
  margin-right: 10px;
  white-space: nowrap;
}

/* 上传按钮容器样式 */
:deep(.el-upload) {
  display: inline-block;
}

/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}
.list-year-data {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
.scroll-table-body {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
</style>
