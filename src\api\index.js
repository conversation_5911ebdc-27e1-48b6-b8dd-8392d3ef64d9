import request from "@/utils/fetch";
import request2 from "@/utils/fetch2";

const api_preffix = "/base";

/**
 * 管理人员综合能力级别配置表
 */

//列表
export const findListByCompanyGroupId = (params) => {
  return request.get(`/base/baseLevel/findListByCompanyGroupId`, { params });
};
//新增
export const saveLevelBatch = (params) => {
  return request.post(`/base/baseLevel/saveBatch`, params);
};
//修改
export const updateLevelBatch = (params) => {
  return request.post(`/base/baseLevel/updateBatch`, params);
};
//有效职等下拉选择
export const findPositionGradeByCompanyGroupId = (params) => {
  return request.get(`/base/positionGrade/findPositionGradeByCompanyGroupId`, {
    params,
  });
};
//基础资料：级别配置表
export const findGradeSettingList = (params) => {
  return request.get(`/base/positionGrade/findGradeSettingList`, { params });
};

//通过职等ID获取一个职等明细
export const findListByPositionGradeId = (params) => {
  return request.get(`/base/baseLevel/findListByPositionGradeId`, { params });
};
//删除
export const deleteByPositionGradeId = (params) => {
  return request.get(`/base/baseLevel/deleteByPositionGradeId`, { params });
};
//按公司查询评定级别、职级标准表
export const findListByCompanyId = (params) => {
  return request.get(`/base/baseLevel/findListByCompanyId`, { params });
};

/**
 * 管理人员综合能力职级配置表
 */

//列表查询
export const findDetailsListByCompanyGroupId = (params) => {
  return request.get(`/base/baseLevelDetails/findDetailsListByCompanyGroupId`, {
    params,
  });
};
//新增
export const saveLvelDetailBatch = (params) => {
  return request.post(`/base/baseLevelDetails/saveBatch`, params);
};
//修改
export const updateBatch = (params) => {
  return request.post(`/base/baseLevelDetails/updateBatch`, params);
};
//通过级别id获取职级明细
export const findDetailsByBaseLevelId = (params) => {
  return request.get(`/base/baseLevelDetails/findDetailsByBaseLevelId`, {
    params,
  });
};
//删除
export const deleteByBaseLevelId = (params) => {
  return request.get(`/base/baseLevelDetails/deleteByBaseLevelId`, { params });
};

/**
 * 管理人员综合能力级别、职级及比例标准表
 */

//列表查询
export const findBaseLevelRateListByCompanyGroupId = (params) => {
  return request.get(`/base/baseLevel/findBaseLevelRateListByCompanyGroupId`, {
    params,
  });
};
//级别、职级比例录入调整
export const updateAdjustRate = (params) => {
  return request.post(`/base/baseLevel/updateAdjustRate`, params);
};

/**
 * 各部门人员统计
 */
export const findDeptCount = (params) => {
  return request.get(`/base/dept/findDeptCount`, { params });
};
/**
 * 各部门人员明细表
 */
export const findDeptMemberDetailListByCompanyGroupId = (params) => {
  return request.get(`/point/ablity/findListByCompanyGroupId`, { params });
};

/**
 * 入职满三年本科学历副主任级以下人员能力评定
 */
export const findUpWorkThreeYear = (params) => {
  return request.get(`/point/ablity/findUpWorkThreeYear`, { params });
};
//副主任级以下人员能力评定
export const updateRisePosition = (params) => {
  return request.get(`/point/ablity/updateRisePosition`, { params });
};

/**
 * 评定对象明细表
 */

//综合能力【级别、职级评定结果汇总】【职级评定结果汇总】表
export const findCheckedLastResult = (params) => {
  return request.get(`/point/ablity/findCheckedLastResult`, { params });
};
//合并单位前评定对象明细表
export const findMergeListByCompanyGroupId = (params) => {
  return request.get(`/point/ablity/findMergeListByCompanyGroupId`, { params });
};
//合并目标单位下拉选择框
export const findMergeDropdownList = (params) => {
  return request.get(`/base/positionGrade/findMergeDropdownList`, { params });
};
//更新(合并评价)
export const updateMergePositionGrade = (params) => {
  return request.post(`/point/ablity/updateMergePositionGrade`, params);
};

/**
 * 级别评定人明细表
 */
export const findOne = (params) => {
  return request.get(`point/ablity/findOne`, { params });
};
//暂存评定人（年度）
export const updateNotCommitSave = (params, { positionGradeId }) => {
  return request.post(
    `/point/ablity/updateNotCommitSave?positionGradeId=${positionGradeId}`,
    params
  );
};
//确定评定人（年度）
export const updateConfirmLeader = (params, { positionGradeId }) => {
  return request.post(
    `/point/ablity/updateConfirmLeader?positionGradeId=${positionGradeId}`,
    params
  );
};
//管理人员综合能力评定职级评定流程节点查询表
export const findCheckerPointByConfirmLeaderId = (params) => {
  return request.get(
    `point/ablityDirectLeader/findCheckerPointByConfirmLeaderId`,
    { params }
  );
};
//管理人员综合能力评定职级评定流程节点查询表（个人明细表）
export const findCheckerPointDetailsByAblityDirectLeaderId = (params) => {
  return request.get(
    `point/ablityDirectLeader/findCheckerPointDetailsByAblityDirectLeaderId`,
    { params }
  );
};

//确定评定人（半年度）
export const updateDirectLeaderByHalfYear = (params, { positionGradeId }) => {
  return request.post(
    `/point/ablity/updateDirectLeaderByHalfYear?positionGradeId=${positionGradeId}`,
    params
  );
};
//查询用户是否存在
export const findUserByUserName = (params) => {
  return request.get(`/base/user/findUserByUserName`, { params });
};
//通用姓名查询OA用户获取部门、职务、职等
export const findOAUserByUserName = (params) => {
  return request.get(`/base/user/findOAUserByUserName`, { params });
};
//更新：管培生转管理人员日期
export const updateToManagerDate = (params) => {
  return request.get(`/base/user/updateToManagerDate`, { params });
};
//更新：管培生转管理人员日期
export const updateToManagerDateNew = (params) => {
  return request.get(`/base/user/updateToManagerNew`, { params });
};
/**
 * 优级及标杆人员评定
 */

export const findDetailsListByYearMonth = (params) => {
  return request.get(`point/confirmLeaderDetails/findDetailsListByYearMonth`, {
    params,
  });
};
//确定标杆：保存录入/完成评定
export const updateStandardEmployee = (params, { isFinishConfirm }) => {
  return request.post(
    `/point/ablity/updateStandardEmployee?isFinishConfirm=${isFinishConfirm}`,
    params
  );
};

//通过年、月、职等参数获取【普通人】评定人明细
export const findConfirmLeaderList = (params) => {
  return request.get(`/point/confirmLeaderDetails/findConfirmLeaderList`, {
    params,
  });
};
//通过年、月、职等参数获取【管培生】评定人明细
export const findStudentConfirmLeaderList = (params) => {
  return request.get(
    `/point/confirmLeaderDetails/findStudentConfirmLeaderList`,
    { params }
  );
};

//评定开始发起【普通人】
export const updateStandardEmployeeToOA = ({
  confirmLeaderId,
  requireFinishDate,
  positionGradeId,
}) => {
  return request.post(
    `/point/ablity/updateStandardEmployeeToOA?confirmLeaderId=${confirmLeaderId}&requireFinishDate=${requireFinishDate}&positionGradeId=${positionGradeId}`
  );
};
//评定开始发起【管培生】
export const updateStandardStudentToOA = ({
  confirmLeaderId,
  requireFinishDate,
  companyGroupId,
}) => {
  return request.post(
    `/point/ablity/updateStandardStudentToOA?confirmLeaderId=${confirmLeaderId}&requireFinishDate=${requireFinishDate}&companyGroupId=${companyGroupId}`
  );
};

/**
 * 级别评定汇总
 */
//列表
export const findMeetingLevelDetailsList = (params) => {
  return request.get(`/point/ablity/findMeetingLevelDetailsList`, { params });
};
//合议评定：保存录入/级别确定
export const updateMeetingLevel = (params, { isMeetingConfirm }) => {
  return request.post(
    `/point/ablity/updateMeetingLevel?isMeetingConfirm=${isMeetingConfirm}`,
    params
  );
};

//合议评定：职级评定发起
export const updateMeetingLevelToOA = ({
  confirmLeaderId,
  positionGradeId,
}) => {
  return request.post(
    `/point/ablity/updateMeetingLevelToOA?confirmLeaderId=${confirmLeaderId}&positionGradeId=${positionGradeId}`
  );
};

//XX职级综合能力【职级评定】表
export const findMeetingConfirmBy = (params) => {
  return request.get(`/point/ablity/findMeetingConfirmBy`, { params });
};
//XX职级综合能力【职级评定】明细表
export const findFinishConfirmDetailsBy = (params) => {
  return request.get(`/point/ablity/findFinishConfirmDetailsBy`, { params });
};

/**
 * 级别评定
 */
//通过评定人查找级别评定列表
// export const findMergeListByLeaderName = (params)=>{
//     return request.get(`/point/ablity/findMergeListByLeaderName`,{params})
// }
export const findAblityPointListByConfirmLeaderId = (params) => {
  return request.get(`point/ablityPoint/findAblityPointListByConfirmLeaderId`, {
    params,
  });
};
export const findAblityPointListByAblityId = (params) => {
  return request.get(`point/ablityPoint/findAblityPointListByAblityId`, {
    params,
  });
};

//年度各职等管理人员综合能力【评定结果审批】表
export const findCompanyLastResult = (params) => {
  return request.get(`point/ablity/findCompanyLastResult`, { params });
};
//年度各职等管理人员综合能力【评定结果审批】表：XX职等【级别、职级】占比汇总表（副表）
export const findLevelGradeRate = (params) => {
  return request.get(`point/ablity/findLevelGradeRate`, { params });
};
//半年度各职等管理人员综合能力【评定结果审批】表
export const findCompanyLastResultHalfYear = (params) => {
  return request.get(`point/ablity/findCompanyLastResultHalfYear`, { params });
};

export const findFeedbackList = (params) => {
  return request.get(`point/ablityFeedback/findFeedbackList`, { params });
};

export const resultConfirmListByDept = (params) => {
  return request.get(`point/ablityFeedback/resultConfirmListByDept`, {
    params,
  });
};

export const getLeaderAggregateQuery = (params) => {
  return request.get(`base/basePositionPlan/getLeaderAggregateQuery`, {
    params,
  });
};

export const getAggregateQuery = (params) => {
  return request.get(`base/basePositionPlan/getAggregateQuery`, { params });
};

export const getUserInfo = (params) => {
  return request.get(`point/ablityFeedback/userInfo`, { params });
};

export const isAllowSentOa = (params) => {
  return request.get(`point/ablityFeedback/isAllowSentOa`, { params });
};

export const deleteOne = (params) => {
  return request.get(`point/ablityFeedback/deleteOne`, { params });
};

export const updateFeedback = (params) => {
  return request.post(`point/ablityFeedback/updateFeedback`, params);
};

export const sentFeedbackToOA = (params) => {
  return request.get(`point/ablityFeedback/sentFeedbackToOA`, { params });
};

//年度各职等管理人员综合能力【评定结果审批】暂存
export const updateAdjustLevel = (params) => {
  return request.post(`/point/ablity/updateAdjustLevel`, params);
};

//年度各职等管理人员综合能力【评定结果审批】推送OA
export const updateAdjustLevelSendOA = (params) => {
  return request.post(`/point/ablity/updateAdjustLevelSendOA`, params);
};

//历年评定汇总表
export const findYearReport = (params) => {
  return request.get(`/point/ablity/findYearReport`, { params });
};

export const findAllCompanyAndPositionGrade = (params) => {
  return request.get(`base/dept/findCompanyMenu`, { params });
};

export const findSelectYearMonth = (params) => {
  return request.get(`base/dept/findSelectYearMonth`, { params });
};
export const findYearList = (params) => {
  return request.get(`base/dept/findYearList`, { params });
};

//公司部门排序
export const findDeptOrderBy = (params) => {
  return request.get(`base/dept/findDeptOrderBy`, { params });
};

/*管培生*/

//定职人员明细表
export const findStudentListByCompanyGroupId = (params) => {
  return request.get(`point/ablity/findStudentListByCompanyGroupId`, {
    params,
  });
};
export const findStudentListByCompanyGroupId2 = (params) => {
  return request.get(`point/ablity/findSocialManagerList`, {
    params,
  });
};
export const addSocialManager = (params) => {
  return request.get(`base/user/addSocialManager`, { params });
};
export const findList = (params) => {
  return request.get(`point/ablityWorkSummary/findList`, { params });
};
//定职人员明细表届别调整判断结果查询
export const findAllAdjustGraduateYear = (params) => {
  return request.get(`base/adjustGraduateYear/findAll`, { params });
};
//定职人员明细表届别调整保存
export const adjustGraduateYearSave = (params) => {
  return request.post(`base/adjustGraduateYear/save`, params);
};

//管培生届别、级别
export const findSchoolLevel = (params) => {
  return request.get(`base/dept/findSchoolLevel`, { params });
};
//管培生评定人提交OA
export const updateConfirmLeaderByStudent = (params) => {
  return request.post(`point/ablity/updateConfirmLeaderByStudent`, params);
};
export const sendOa = (params) => {
  return request.get(`point/ablityWorkSummary/sendOa`, { params });
};
export const ablityPptSummarySendOa = (params) => {
  return request.post(`point/ablityPptSummary/sendOa`,  params);
};

export const batchDownloadPpt = (params) => {
  return request2.get(`point/ablityPptSummary/batchDownloadPpt`, { params });
};
export const exportData = (params) => {
  return request.get(`point/ablityWorkSummary/exportData`, { params });
};
//管培生职务规划查询
export const findListByYearAndCategory = (params) => {
  return request.get(`base/basePositionPlan/findListByYearAndCategory`, {
    params,
  });
};

export const importAssessor = (params) => {
  return request.post(`point/ablityWorkSummary/importAssessor`, params);
};

export const importMTraineeMeetingResult = (params) => {
  return request.post(
    `point/ablityWorkSummary/importMTraineeMeetingResult`,
    params
  );
};

export const importManagerMeetingResult = (params) => {
  return request.post(
    `point/ablityWorkSummary/importManagerMeetingResult`,
    params
  );
};

export const userSummaryDetailList = (params) => {
  return request.get(`point/ablityWorkSummary/userSummaryDetailList`, {
    params,
  });
};

export const updateBasePositionPlan = (params) => {
  return request.post(`base/basePositionPlan/updateBasePositionPlan`, params);
};

//定职人员综合能力【评定合议】表
export const findAblityStudentPointListBy = (params) => {
  return request.get(`point/ablity/findAblityStudentPointListBy`, { params });
};

export const findStudentMeetingListBy = (params) => {
  return request.get(`point/ablity/findStudentMeetingListBy`, { params });
};

//合议结果汇总表
export const findAllStudentPointToCheckByCompanyId = (params) => {
  return request.get(`point/ablity/findAllStudentPointToCheckByCompanyId2`, {
    params,
  });
};
//合议暂存提交
export const updateStudentRisePositionSave = (params) => {
  return request.post(`/point/ablity/updateStudentRisePositionSave`, params);
};
//合议提交
export const updateStudentRisePosition = (params) => {
  return request.post(`point/ablity/updateStudentRisePosition`, params);
};
//本期合议：综合评价（积极态度、稳定性、优点、缺点）
export const updateMeetingText = (params) => {
  return request.get(`point/ablity/updateMeetingText`, { params });
};
//本期合议：综合评价（积极态度、稳定性、优点、缺点）
export const updateMeetingText2 = (params) => {
  return request.post(`point/ablity/updateMeetingText2`, params);
};
/**
 * 更新会议备注信息
 * @param {Object} params - 包含fdId和remark的对象
 */
export const updateMeetingRemark = (params) => {
  return request.get(`point/ablity/updateMeetingRemark`, { params });
};

//各公司评定情况汇总
export const findAblityLevel = (params) => {
  return request.get(`/anonymous/findAblityLevel`, { params });
};
//更新工龄、职务、职等
export const updatePositionGradeLong = (params) => {
  return request.get(`/anonymous/updatePositionGradeLong`, { params });
};
//XX职等评分明细表（业务、OA共用-2023-03-01）
export const findGradePointList = (params) => {
  return request.get(`/anonymous/findGradePointList`, { params });
};
//XX用户评分明细表（业务、OA共用-2023-03-01）
export const findUserPointList = (params) => {
  return request.get(`/anonymous/findUserPointList`, { params });
};

//查询审批提示
export const findOneByYearMonth = (params) => {
  return request.get(`/point/studentApprove/findOneByYearMonth`, { params });
};
//审批提示新增或修改保存
export const studentApproveSave = (params) => {
  return request.post(`/point/studentApprove/save`, params);
};

//届别下拉
export const findGraduateYear = (params) => {
  return request.get(`base/dept/findGraduateYear`, { params });
};
//管培生职等下拉
export const findStudentGrade = (params) => {
  return request.get(`/base/dept/findStudentGrade`, { params });
};
//查询用户是否有权限【提交审批】
export const findUserAuth = (params) => {
  return request.get(`/base/dept/findUserAuth`, { params });
};

//定职人员综合能力评定结果查询表
export const findAblityStudentPointListByResult = (params) => {
  return request.get(`point/ablity/findAblityStudentPointListByResult`, {
    params,
  });
};

//管培生：定职人员【历年评定人员明细】查询表
export const findStudentDetailsListBy = (params) => {
  return request.get(`point/ablity/findStudentDetailsListBy`, { params });
};

//定职人员综合得分能力得分明细
export const findStudentPointListByCompanyGroupId = (params) => {
  return request.get(
    `point/studentPoint/findStudentPointListByCompanyGroupId2`,
    { params }
  );
};
//管培生：定职人员综合能力评定【合议结果】审批表
export const findListByFdYear = (params) => {
  return request.get(`point/ablity/findListByFdYear`, { params });
};
//管培生：述职ppt
export const ablityPptSummary = (params) => {
  return request.get(`point/ablityPptSummary/findList`, { params });
};
//管培生：定职人员综合能力评定【合议结果】审批表（提交审批）
export const commitStudentToOA = (params) => {
  return request.post(`point/ablity/commitStudentToOA`, params);
};
//管培生：定职人员综合能力评定【审批结果】查询表
export const findResultListByFdYear = (params) => {
  return request.get(`point/ablity/findResultListByFdYear`, { params });
};

//OA管培生九项评分查询：通过ConfirmLeaderId和leaderName查询
export const findStudentPointListByConfirmLeaderId = (params) => {
  return request.get(
    `point/studentPoint/findStudentPointListByConfirmLeaderId`,
    { params }
  );
};
//查询评定对象的领导评分明细
export const findOneByAblityId = (params) => {
  return request.get(`point/studentPoint/findOneByAblityId`, { params });
};
//查看列表（优点、优点案例、缺点、缺点案例）
export const findListByAblityId = (params) => {
  return request.get(`anonymous/findListByAblityId`, { params });
};
//OA查询所有公司管培生得分明细表
export const findCompanyStudentList = (params) => {
  return request.get(`/anonymous/findCompanyStudentList`, { params });
};

//查询一个公司管培生【评定结果面谈】列表
export const findTalkListBy = (params) => {
  return request.get(`point/ablityTalk/findTalkListBy`, { params });
};
//更新谈话人调整
export const updateAdjustTalker = (params) => {
  return request.get(`point/ablityTalk/updateAdjustTalker`, { params });
};
//触发面谈
export const triggerTalk = (params) => {
  return request.get(`point/ablityTalk/triggerTalk`, { params });
};

export const talkListIsDelete = (params) => {
  return request.get(`point/ablityTalk/talkListIsDelete`, { params });
};
//评定结果面谈记录
export const findAblityTalk = (params) => {
  return request.get(`point/ablityTalk/findAblityTalk`, { params });
};

//系统权限用户组
export const getAuthGroup = (params) => {
  return new Promise((resolve, reject) => {
    const groups = {
      managerGroup: [
        "liuzl",
        "wupf",
        "huangkf",
        "maifc",
        "zhangk",
        "huanggq",
        "wuqj",
        "lusy",
        "linsm",
        "mujl",
        "qinl",
        "huangml",
        "cenl",
      ],
      traineeGroup: [
        "liuzl",
        "wupf",
        "huangkf",
        "maifc",
        "zhangk",
        "huanggq",
        "wuqj",
        "lusy",
        "linsm",
        "wuj1",
        "yangyg",
        "weicl",
        "qinly",
      ],
    };
    if (process.env.NODE_ENV != "production") {
      groups.managerGroup = []
        .concat(groups.managerGroup)
        .concat(["laisf", "huangdh"]);
      groups.traineeGroup = []
        .concat(groups.traineeGroup)
        .concat(["laisf", "huangdh"]);
    }
    resolve(groups);
  });
};

//获取登入用户权限列表（按钮有权限则显示，无权限隐藏；有查询权限则返回列表，无权限返回空集合）
export const findAuths = (params) => {
  return request.get(`/base/user/findAuths`, { params });
};

/**
 * 导入评定结果
 * @param {FormData} data - 包含文件和其他参数的FormData对象
 */
export function importAdjustResult(data) {
  return request({
    url: "point/ablityWorkSummary/importAdjustResult",
    method: "post",
    data: data,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}
