<template>
    <div class="year-box" >
        <div class="select-block">
            <div id="div_manager" class="my-title" @click="selectTitle('1')" >
                管理人员综合能力评定</div>
            <div id="div_trainee" class="my-title" @click="selectTitle('2')" >
                管培生综合能力评定</div>
        </div>
        <ul class="year-list" id="ul_manager" style="display:none;" >
            <span style="margin-left:55px;color:white;font-size:20px;">12月份评定</span> 
            <span style="margin-right:52px;color:white;font-size:20px;">6月份评定</span>
            <template v-for="(year,index) in yearList">
                <router-link v-if="year.fd_month12!=null" tag="li" :to="`/${year.fd_year}/${year.fd_month12}/menu/1`" :key="index+'full'">
                    {{year.fd_year+"年度"}}
                </router-link>

                <li v-else :key="index+'full_empty'" style="background:transparent;cursor: default;"></li>

                <router-link v-if="year.fd_month!=null" tag="li" :to="`/${year.fd_year}/${year.fd_month}/menu/1`" :key="index+'half'">
                    {{year.fd_year+"半年度"}}
                </router-link>

                <li v-else :key="index+'half_empty'" style="background:transparent;cursor: default;"></li>
            </template>
        </ul>
        <ul class="year-list" id="ul_trainee" style="display:none;" >
            <span style="margin-left:55px;color:white;font-size:20px;">6月份评定</span> 
            <span style="margin-right:52px;color:white;font-size:20px;">12月份评定</span>
            <template v-for="(year,index) in yearList">
                <router-link v-if="year.fd_month!=null" tag="li" :to="`/${year.fd_year}/${year.fd_month}/menu/2`" :key="index+'half_trainee'">
                    {{(year.fd_year-1)+"-"+year.fd_year+"年度"}}
                </router-link>
                <li v-else :key="index+'half_trainee_empty'" style="background:transparent;cursor: default;"></li>

                <router-link v-if="year.fd_month12!=null" tag="li" :to="`/${year.fd_year}/${year.fd_month12}/menu/2`" :key="index+'full_trainee'">
                    {{year.fd_year+"-"+(year.fd_year+1)+"半年度"}}
                </router-link>
                <li v-else :key="index+'full_trainee_empty'" style="background:transparent;cursor: default;"></li>
            </template>
        </ul>
        <div class="select-block" id="div_button" style="margin-top:20px;display:none;">
            <button class="btn btn-primary btn-xs" @click="selectBlock()">返回模块选择</button>
        </div>
    </div> 
</template>

<script>
import {findSelectYearMonth} from '@/api'
export default {
    data(){
        return {
            mIndex:0,
            mName:"",
            yearList:[]
        }
    },
    methods: {
        selectTitle(val) {
            this.mIndex=val;
            if (val==1){
                this.mName="管理人员";
                document.getElementById("ul_manager").style.display="";
                document.getElementById("ul_trainee").style.display="none";
                document.getElementById("div_manager").style.display="";
                document.getElementById("div_trainee").style.display="none";
            }
            else if (val==2){
                this.moduleName="管培生";
                document.getElementById("ul_manager").style.display="none";
                document.getElementById("ul_trainee").style.display="";
                document.getElementById("div_manager").style.display="none";
                document.getElementById("div_trainee").style.display="";
            }
            document.getElementById("div_button").style.display="";
            //console.log("test:"+this.moduleName);
        },
        selectBlock(){
            document.getElementById("ul_manager").style.display="none";
            document.getElementById("ul_trainee").style.display="none";
            document.getElementById("div_manager").style.display="";
            document.getElementById("div_trainee").style.display="";
            document.getElementById("div_button").style.display="none";
        }
    },
    mounted(){
        findSelectYearMonth({fdYear:null}).then(res=>{
            this.yearList = res.data||[];
        })
    }
};
</script>




<style lang="scss" >
    
    ul {
        list-style: none;
        padding:0;
        margin:0;
    }
    .year-box {
        width:600px;
        margin:0 auto;
        padding: 5% 0 0 0;        
        .year-list {
            width: 100%;
            margin:0 auto;
            padding:20px 80px 20px;
            background: #999;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            &>li {
                width:47%;
                height:40px;
                line-height: 40px;
                font-size: 24px;
                background: #19a3ff;
                margin:5px 0;
                color:#fff;
                cursor: pointer;
            }
        }
        .select-block {
            width:600px;
            text-align: center;
            padding: 0 18% 0 18%;   
            .my-title {
                width:400px;font-size:26px;background: #19a3ff;
                margin-top:50px;margin-bottom:10px;
                border: solid 1px #d28e66;
                padding:5px;
                cursor: pointer;
            }
        }
        
    }

</style>
