<template>
  <div class="department-sort-container">
      <div class="row" style="line-height:40px;">
        <div class="col-xs-12 text-left">
          <h4 class="col-xs-7" style="padding: 0;">【{{getCompanyName()}}】公司部门排序</h4>
          <div class="col-xs-5 text-right" style="padding: 0;">
            <button class="btn btn-primary btn-xs" @click="$router.push(`/${$route.params.year}/${$route.params.month}/menu/1`)">返回目录</button>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-xs-6" style="width:540px">
          <v-table
            is-vertical-resize
            style="width:100%;height:640px"
            :columns="columns"
            :table-data="tableData"
            row-hover-color="#eee"
          ></v-table>
          <!-- <div class="list-table" style="width:600px;">
            <div class="scroll-table" style="width:100%;height:100%;overflow-x:hidden;">
                <table class="scroll-table-header table-bordered" :style="{width:600 - 1.4*12 +'px'}">
                  <colgroup>
                    <col style="width:8%">
                    <col style="width:23%">
                    <col style="width:23%">
                    <col style="width:23%">
                  </colgroup>
                  <thead>
                    <tr>
                      <th>序号</th>
                      <th>生产一线</th>
                      <th>生产辅助</th>
                      <th>生产支持</th>
                    </tr>
                  </thead>
                </table>
                <div class="scroll-table-body" :style="{overflow:'auto',maxHeight:body_height - 135 + 'px','overflow-y':'scroll','overflow-x':'hidden'}">
                    <table style="margin:0px 10px 0 0;position: relative;width:100%;font-size:13px;float:left;border:none;" class="table table-bordered table-hover table-striped">
                        <colgroup>
                          <col style="width:8%">
                          <col style="width:23%">
                          <col style="width:23%">
                          <col style="width:23%">
                        </colgroup>
                        <tbody>
                          <tr v-for="(data,$index) in tableData" :key="$index">
                            <td>{{data.order_index}}</td>
                            <td>{{data.name1}}</td>
                            <td>{{data.name2}}</td>
                            <td>{{data.name3}}</td>
                          </tr>
                      </tbody>
                    </table>
                </div>
            </div>
          </div> -->
        </div>
      </div>
  </div>
</template>

<script>
import { findDeptOrderBy } from '@/api'
export default {
  data(){
    return {
      body_height: 0,
      tableData:[],
      columns:[
        {field: 'order_index',title:'序号', width:50,titleAlign:'center',columnAlign:'center',isResize:true},
        {field: 'name1',title:'生产一线', width:150,titleAlign:'center',columnAlign:'center',isResize:true},
        {field: 'name2',title:'生产辅助', width:150,titleAlign:'center',columnAlign:'center',isResize:true},
        {field: 'name3',title:'生产支持', width:160,titleAlign:'center',columnAlign:'center',isResize:true}
      ],
      year:"",
      month:"",
      companyGroupId:"2",
    }
  },
  methods:{
    getCompanyName(){
      return Utils.getCompanyNameByGroupId(this.companyGroupId);
    },
    _findDeptOrderBy(){
      findDeptOrderBy().then(res=>{
        if(!res.success) return;
        this.tableData = res.data ? res.data : [];
      })
    },
    initPage(){
      const {year,month} = this.$route.params;
      this.year = year;
      this.month = month;
      this._findDeptOrderBy();
    }
  },
  watch:{
    "$route":function(){
      this.initPage();
    }
  },
  created(){
    this.initPage();
  }
}
</script>

<style>

</style>
