<template>
    <div class="container" style="width:300px;padding-top:20px;">
        <form>
            <div class="form-group row">
                <label class="col-xs-4 text-right">谈话对象：</label>
                <span>{{formData.userName}}</span>
            </div>
            <div class="form-group row">
                <label class="col-xs-4 text-right">谈话人：</label>
                <input type="text" v-model="formData.AdjustUserName" />
            </div>            
            <div class="form-group row">
                <label class="col-xs-4 text-right">职务：</label>
                <span>{{formData.AdjustPositionName}}</span>
            </div>
            <div class="form-group row">
                <label class="col-xs-4 text-right">职等：</label>
                <span>{{formData.AdjustGradeName}}</span>
            </div>
            <div class="form-group text-center">
                <button type="button" class="btn btn-xs btn-primary" @click="checkUserName">验证</button>
                <button type="button" class="btn btn-xs btn-primary" @click="_updateAdjustTalker">确认调整</button>
            </div>
        </form>
    </div>
</template>

<script>
import { findOAUserByUserName,updateAdjustTalker } from "@/api";

export default {
  props: {
    params: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      tableData:{},
      isCheck: "",
      formData: {
        fdId: "",
        userName: "",
        AdjustUserName: "",
        AdjustPositionName: "",
        AdjustGradeName: ""
      }
    };
  },
  methods: {
    _updateAdjustTalker() {
        if (this.isCheck!="1"){
            layer.msg('未【验证】谈话人，不能确认调整.', {icon: 2,shade:0.3,shadeClose:true});
            return;
        }
      layer.confirm(`确认调整保存吗?`, { icon: 3, title: `提示` },
        index => {
          updateAdjustTalker({fdId:this.formData.fdId,adjustUserName:this.formData.AdjustUserName}).then(res => {
              let result = res.success;
              layer.msg(`调整${result ? '成功，请重新查询.' : '失败'}`, {icon: result ? 1 : 2,shade:0.3,shadeClose:true});
              layer.close(index)
              this.params.closeComponent&&this.params.closeComponent();
          });
        }
      );
    },
    checkUserName() {
        let adjustName = this.formData.AdjustUserName;
        findOAUserByUserName({userName: adjustName}).then(res=>{
            this.tableData = res.data || {};
            if (this.tableData.positionName){
                this.isCheck="1";
                this.formData.AdjustPositionName=this.tableData.positionName;
                this.formData.AdjustGradeName=this.tableData.gradeName;
            }else {
                this.isCheck="0";
                layer.msg(adjustName+'不存在.', {icon: 2,shade:0.3,shadeClose:true});
            }
        })
    }
  },
  mounted() {
    this.formData = {
      fdId: this.params.fdId,
      userName: this.params.userName || "",
      AdjustUserName: this.params.talkUserName || "",
      AdjustPositionName: this.params.talkPositionName || "",
      AdjustGradeName: this.params.talkGradeName || "",
    };
  }
};
</script>



<style>
</style>
