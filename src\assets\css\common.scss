.beauty-scroll-bar::-webkit-scrollbar {
    width: 5px;
    height:5px;
}

/*修改滚动条样式*/
/*隐藏轨道*/
.beauty-scroll-bar::-webkit-scrollbar-track-piece{
    background-color:#fff;
    -webkit-border-radius:0;
}
/* 滚动条两端的按钮 */
.beauty-scroll-bar::-webkit-scrollbar-button {
    display: none;
}

/* 外层轨道 */
.beauty-scroll-bar::-webkit-scrollbar-track {
    border-radius: 5px;
    background-color: rgba(255,255,255,0);
}
.beauty-scroll-bar::-webkit-scrollbar{
    height:20px;
}
/* 滚动的滑块 */
.beauty-scroll-bar::-webkit-scrollbar-thumb {
    width:4px;
    height: 4px;
    border-radius: 5px;
    background-color: #a09f9f;
}
/*横向滚动条*/
.beauty-scroll-bar::-webkit-scrollbar:horizontal {

    height:4px;

}
/* 边角 */
.beauty-scroll-bar::-webkit-scrollbar-corner {
    display: none;
}

/* 右下角拖动块 */
.beauty-scroll-bar::-webkit-resizer {
    display: none;
}
a {
    cursor: pointer;
}