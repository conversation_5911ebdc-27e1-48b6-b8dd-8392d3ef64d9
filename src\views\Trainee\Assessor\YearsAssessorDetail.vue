<template>
  <div class="years-assessor-container">
      <div class="row">
        <div class="col-xs-12 text-left">
          <h4 class="col-xs-6">{{$route.name}}</h4>
          <div class="col-xs-6 text-right" style="line-height:40px;">
            <button class="btn btn-primary btn-xs" @click="$router.push(`/${$route.params.year}/${$route.params.month}/menu/1`)">返回目录</button>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-xs-12">
          <div class="list-year-head-table" style="background:#b7d8dc;">
              <table class="list-year-head table table-bordered" :style="`margin-bottom:0;margin:0 auto 0 0;`">
                <colgroup>
                    <col width='50'/>
                    <col width='90'/>
                    <col width='70'/>
                    <col width='100'/>
                    <col width='90'/>
                    <col width='70'/>
                    <col width='100'/>
                    <col width='100'/>
                    <col width='100'/>
                    <col width='100'/>
                    <col width='100'/>
                    <col width='100'/>
                    <col width='80'/>
                </colgroup>
                <thead>
                <tr>
                    <th rowspan="2">序号</th>
                    <th rowspan="2">公司</th>
                    <th colspan="4">管培生</th>
                    <th colspan="6">评定人</th>
                    <th rowspan="2">备注</th>
                </tr>
                <tr>
                    <th>姓名</th>
                    <th>部门</th>
                    <th>岗位</th>
                    <th>职务等级</th>
                    <th>第一年</th>
                    <th>第二年</th>
                    <th>第三年</th>
                    <th>第四年</th>
                    <th>第五年</th>
                    <th>第六年</th>
                </tr>
                </thead>
            </table>
          </div>
          <div class="list-year-data" style="overflow:auto;border-bottom:1px solid #ccc;">
              <table style="margin-bottom:0;" class="table table-bordered" v-for="(data,$index) in tableData" :key="$index">
                <colgroup>
                    <col width='50'/>
                    <col width='90'/>
                    <col width='70'/>
                    <col width='100'/>
                    <col width='90'/>
                    <col width='70'/>
                    <col width='100'/>
                    <col width='100'/>
                    <col width='100'/>
                    <col width='100'/>
                    <col width='100'/>
                    <col width='100'/>
                    <col width='80'/>
                </colgroup>
                <thead>
                    <tr>
                        <th colspan="13" style="background:#d6fbff;text-align:left;text-indent:10px;">{{data.year}}届</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(item,index) in (data.list||[])" :key="index">
                        <td>{{index+1}}</td>
                        <td>{{item.company_name}}</td>
                        <td>{{item.user_name}}</td>
                        <td>{{item.dept_name}}</td>
                        <td>{{item.job_name}}</td>
                        <td>{{item.grade_name}}</td>
                        <td>{{item.year_leader1}}</td>
                        <td>{{item.year_leader2}}</td>
                        <td>{{item.year_leader3}}</td>
                        <td>{{item.year_leader4}}</td>
                        <td>{{item.year_leader5}}</td>
                        <td>{{item.year_leader6}}</td>
                        <td>{{item.remark}}</td>
                    </tr>
                </tbody>
            </table>
            <span v-if="tableDataLength <= 0">
              {{ tableDataLength == -1 ? "加载中..." : "暂无数据" }}
            </span>
          </div>
        </div>
      </div>
  </div>
</template>

<script>
import { findStudentDetailsListBy } from '@/api'
export default {
  data(){
    return {
      tableData:[],
      year:'',
      month:'',
      tableDataLength: -1
    }
  },
  methods:{
    _findStudentDetailsListBy(){
      /*先判断有权限才可以查询(modify by huangdh@2019-05-24)*/
      if (this.$store.state.doubleCol.arrAuths.point_ablity_findStudentDetailsListBy)
      {
        findStudentDetailsListBy({year:this.year,month:this.month}).then(res=>{
            this.tableData = res.data || [];
            this.tableDataLength = this.tableData.length
        })
      }
      else {
          layer.msg(`对不起，您没有权限查询本界面.`, {icon: 2,shade:0.3,shadeClose:true});
      }
    },
    resizeTable(){
      $(".list-year-data").height($(window).height() - 160 +'px');
      $(".list-year-head").width($(".list-year-data>table").width()+'px');
    },
    initPage(){
        this.resizeTable();
      const {year,month} = this.$route.params;
      this.year = year;
      this.month = month;
      this._findStudentDetailsListBy();
    }
  },
  watch:{
    "$route":function(){
      this.initPage();
    }
  },
  mounted(){
    this.initPage();
  }
}
</script>

<style scoped>
/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}
.list-year-data {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
.scroll-table-body {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
</style>