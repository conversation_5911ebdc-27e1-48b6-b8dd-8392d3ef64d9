<template>
  <div
    :style="isCollapsed ? 'width:1280px' : ''"
    class="rank-evaluation-summary-container"
  >
    <div class="row" style="line-height: 40px">
      <div class="col-xs-12 text-left">
        <h4 class="col-xs-6">
          {{ this.$route.params.year }}{{ this.month == `6` ? `半` : `` }}年度{{
            getCompanyName()
          }}（{{ getGradeName() }}）{{ $route.name }}
        </h4>
        <div class="col-xs-6 text-right">
          <div class="buts">
            <button class="btn btn-primary btn-xs" @click="showDescrDialog()">
              使用说明
            </button>
            <button
              type="button"
              class="btn btn-primary btn-xs"
              @click="sheetIt"
            >
              下载模板
            </button>
            <el-upload
              :show-file-list="false"
              action=""
              :headers="headers"
              :before-upload="BeforeUpload"
              :http-request="Upload"
            >
              <template #trigger>
                <button type="button" class="btn btn-primary btn-xs">
                  导入模板
                </button>
              </template>
            </el-upload>
            <button
              class="btn btn-primary btn-xs"
              :disabled="disabledButton"
              @click="_updateMeetingLevel(0)"
              v-if="
                $store.state.doubleCol.arrAuths.point_ablity_updateMeetingLevel
              "
            >
              保存录入
            </button>
            <button
              class="btn btn-primary btn-xs"
              :disabled="disabledButton"
              @click="_updateMeetingLevel(1)"
              v-if="
                $store.state.doubleCol.arrAuths.point_ablity_updateMeetingLevel
              "
            >
              级别确定
            </button>
            <button
              class="btn btn-primary btn-xs"
              @click="$router.push(`/${$route.params.year}/12/menu/1`)"
            >
              返回目录
            </button>
          </div>
        </div>
      </div>
    </div>
    <div class="row" v-show="isPrint">
      <div class="col-xs-12">
        <div class="list-table">
          <div class="scroll-table" style="height: 100%; overflow-x: hidden">
            <table
              class="scroll-table-header table-bordered"
            >
              <colgroup>
                <col style="width: 2%" />
                <col style="width: 5%" />
                <col style="width: 4%" />
                <col style="width: 2.5%" />
                <col style="width: 2.5%" />
                <col style="width: 3%" />
                <col style="width: 7%" />
                <col
                  v-for="(item, index) in leaderData"
                  :key="index"
                  v-show="!isCollapsed"
                  :style="{
                    width:
                      60 /
                        (leaderData.length + (leaderData.length > 8 ? 0 : 7)) +
                      '%',
                  }"
                />
                <col style="width: 3%" />
                <col :style="isCollapsed ? 'width:80px' : 'width: 4%'" />
                <col :style="isCollapsed ? 'width:80px' : 'width: 5.5%'" />
              </colgroup>
              <thead>
                <tr>
                  <th rowspan="3">序号</th>
                  <th rowspan="3">厂部</th>
                  <th rowspan="3">姓名</th>
                  <th rowspan="3">性别</th>
                  <th rowspan="3">年龄</th>
                  <th rowspan="3">学历</th>
                  <th rowspan="3">岗位</th>
                  <th :colspan="isCollapsed ? 1 : leaderData.length + 1">
                    <button
                      @click="toggleCollapse"
                      :style="{ width: isCollapsed ? '116px' : 'auto' }"
                    >
                      {{
                        isCollapsed ? "评定人评定分值 ∨" : "评定人评定分值 ＞"
                      }}
                    </button>
                  </th>
                  <th colspan="2">合议结果</th>
                  <th rowspan="3">评定<br />事例<br />依据</th>
                </tr>
                <tr v-show="!isCollapsed">
                  <th v-for="(item, index) in leaderData" :key="index">
                    {{ item.leader }}
                  </th>
                  <th rowspan="2">平均<br />分值</th>
                  <th rowspan="2">分值</th>
                  <th rowspan="2">级别</th>
                </tr>
                <tr v-show="!isCollapsed">
                  <th
                    v-for="(item, index) in leaderData"
                    :key="index"
                    @click="showDialog(item)"
                  >
                    <a>查看</a>
                  </th>
                </tr>
                <tr v-show="isCollapsed">
                  <th rowspan="2" style="width: 116px">平均分值</th>
                  <th rowspan="2">分值</th>
                  <th rowspan="2">级别</th>
                </tr>
              </thead>
            </table>
            <div
              class="scroll-table-body"
              :style="{
                overflow: 'auto',
                maxHeight: body_height - 190 + 'px',
                'overflow-y': 'scroll',
                'overflow-x': 'hidden',
              }"
            >
              <table
                style="
                  margin: 0px 10px 0 0;
                  position: relative;
                  font-size: 13px;
                  float: left;
                  border: none;
                "
                class="table table-bordered table-hover table-striped"
              >
                <colgroup>
                  <col style="width: 2%" />
                  <col style="width: 5%" />
                  <col style="width: 4%" />
                  <col style="width: 2.5%" />
                  <col style="width: 2.5%" />
                  <col style="width: 3%" />
                  <col style="width: 7%" />
                  <col
                    v-for="(item, index) in leaderData"
                    :key="index"
                    v-show="!isCollapsed"
                    :style="{
                      width:
                        60 /
                          (leaderData.length +
                            (leaderData.length > 8 ? 0 : 7)) +
                        '%',
                    }"
                  />
                  <col :style="isCollapsed ? 'width:130px' : 'width: 3%'" />
                  <col :style="isCollapsed ? 'width:80px' : 'width: 4%'" />
                  <col :style="isCollapsed ? 'width:80px' : 'width: 5.5%'" />
                </colgroup>
                <thead>
                  <tr>
                    <td
                      style="
                        padding: 8px 10px 5px 10px;
                        text-align: left;
                        border-bottom: none;
                      "
                      :colspan="`${11 + leaderData.length}`"
                    >
                      <div id="div_control"></div>
                    </td>
                  </tr>
                  <tr>
                    <td
                      style="
                        padding: 8px 10px 5px 10px;
                        text-align: left;
                        border-bottom: none;
                      "
                      :colspan="`${11 + leaderData.length}`"
                    >
                      <div id="div_fact"></div>
                    </td>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(data, $index) in tableData" :key="$index">
                    <td>{{ $index + 1 }}</td>
                    <td>{{ data.dept_name }}</td>
                    <td>{{ data.user_name }}</td>
                    <td>{{ data.gender }}</td>
                    <td>{{ data.age }}</td>
                    <td>{{ data.degree }}</td>
                    <td>{{ data.job_name }}</td>
                    <td
                      v-for="(item, index) in leaderData"
                      :key="index"
                      v-show="!isCollapsed"
                    >
                      {{ getLeaderScore(item, data) }}
                    </td>
                    <td>{{ data.avg_point || "" }}</td>
                    <td>
                      <input
                        :style="
                          data.meeting_point2 == data.avg_point
                            ? ''
                            : 'color: red'
                        "
                        style="width: 100%"
                        v-model="data.meeting_point2"
                      />
                    </td>
                    <td>
                      <select
                        v-model="data.meeting_level"
                        class="form-control input-sm"
                      >
                        <option value="">请选择</option>
                        <option value="优">优</option>
                        <option value="A">A</option>
                        <option value="B">B</option>
                        <option value="C">C</option>
                      </select>
                    </td>
                    <td>
                      <a @click="showRemark(data)"
                        ><img src="../../assets/images/action_dtl.png" alt=""
                      /></a>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-show="!isPrint" id="table" class="row">
      <table class="table table-bordered table-hover">
        <thead>
          <tr>
            <th rowspan="2">序号</th>
            <th rowspan="2">厂部</th>
            <th rowspan="2">姓名</th>
            <th rowspan="2">性别</th>
            <th rowspan="2">年龄</th>
            <th rowspan="2">学历</th>
            <th rowspan="2">岗位</th>
            <th rowspan="2">合议结果</th>
            <th colspan="2">评定事例依据</th>
            <th rowspan="2">事例</th>
          </tr>
          <tr>
            <th>分值</th>
            <th>级别</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(data, index) in tableData" :key="index">
            <td>{{ index + 1 }}</td>
            <td>{{ data.dept_name }}</td>
            <td>{{ data.user_name }}</td>
            <td>{{ data.gender }}</td>
            <td>{{ data.age }}</td>
            <td>{{ data.degree }}</td>
            <td>{{ data.job_name }}</td>
            <td>{{ data.result }}</td>
            <td>{{ data.score }}</td>
            <td>{{ data.level }}</td>
            <td>{{ data.example }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
import GradeEvaluationDialog from "./GradeEvaluationDialog";
import GradeEvaluationFlowQueryDialog from "./GradeEvaluationFlowQueryDialog";
import {
  findMeetingLevelDetailsList,
  updateMeetingLevel,
  importManagerMeetingResult,
  importAssessor,
} from "@/api";
import AblityPointDetails from "./AblityPointDetails";
import { getCookie, TokenKey } from "@/utils/Cookie";
export default {
  data() {
    return {
      isLoading: true,
      body_height: 0,
      tableData: [],
      scoreData: [],
      leaderData: [],
      year: "",
      month: "12",
      companyGroupId: "",
      positionGradeId: "",
      infoControl: "",
      infoFact: "",
      deptArea: Utils.getQueryParams("deptArea"),
      isCollapsed: false, // 新增属性
      headers: {
        [TokenKey]: getCookie(TokenKey),
      },
      newFile: new FormData(),
      isPrint: true,
    };
  },
  computed: {
    disabledButton() {
      if (this.isLoading || this.tableData.length == 0) {
        return true;
      } else if (this.tableData && this.tableData.length != 0) {
        return !(
          this.getDataPointHasEmpty() ||
          (this.tableData[0].is_meeting_confirm == 0 &&
            this.tableData[0].confirm_leader_status == 30)
        );
      }
      return true;
    },
  },
  methods: {
    BeforeUpload(file) {
      // const fileSuffix = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()
      // const whiteList = ['csv']
      if (file) {
        this.newFile.append("file", file);
      } else {
        return false;
      }
    },
    Upload() {
      const loading = this.$loading({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      this.newFile.append("companyGroupId", this.companyGroupId);
      this.newFile.append("year", this.year);
      this.newFile.append("month", this.month);
      this.newFile.append("userType", "管理人员");
      const newData = this.newFile;
      let params = newData;
      importManagerMeetingResult(params)
        .then((res) => {
          loading.close();
          layer.msg(res.data, { icon: 1, shade: 0.3, shadeClose: true });
          this._findMeetingLevelDetailsList();
        })
        .catch((err) => {
          loading.close();
          layer.msg(`${err.msg}`, { icon: 2, shade: 0.3, shadeClose: true });
        });
    },

    sheetIt() {
      this.isPrint = false;
      setTimeout(() => {
        this.exportExcelOne.exportExcel(
          `【${this.getCompanyName()}】综合能力【级别评定汇总合议】表.xlsx`,
          "#table"
        );
        this.isPrint = true;
      });
    },
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed;
    },
    getDataPointHasEmpty() {
      return this.tableData.find((data) => {
        return data.avg_point == 0;
      });
    },
    getCompanyName() {
      let companyName = Utils.getCompanyNameByGroupId(this.companyGroupId);
      if (
        this.positionGradeId == "0006" ||
        this.positionGradeId == "0007" ||
        this.positionGradeId == "0032" ||
        this.positionGradeId == "0033"
      ) {
        companyName = "全集团";
      }
      return companyName;
    },
    getGradeName() {
      let name = Utils.getGradeLevlByGradeId(this.positionGradeId);
      if (this.deptArea == "A") {
        name = name + "-生产一线";
      } else if (this.deptArea == "B") {
        name = name + "-生产辅助";
      } else if (this.deptArea == "C") {
        name = name + "-生产支持";
      }
      return name;
    },

    _updateMeetingLevel(isMeetingConfirm) {
      this.isLoading = true;
      let list = [];
      this.tableData.forEach((item) => {
        list.push({
          fdId: item.fd_id,
          meetingLevel: item.meeting_level,
          meetingPoint2: item.meeting_point2,
          fdYear: this.year,
        });
      });
      layer.confirm(
        `确认操作：${
          isMeetingConfirm == 0 ? "保存录入" : "级别确定"
        }，请确认？`,
        {
          title: "提示",
          btn: ["确定", "取消"], //按钮
        },
        (index) => {
          this.isLoading = true;
          updateMeetingLevel(list, { isMeetingConfirm: isMeetingConfirm })
            .then((res) => {
              let result = res.success;
              layer.msg(
                `${isMeetingConfirm == 0 ? "保存录入操作" : "级别确定操作"}${
                  result ? "成功" : "失败"
                }`,
                { icon: result ? 1 : 2, shade: 0.3, shadeClose: true }
              );
              this._findMeetingLevelDetailsList();
            })
            .catch((err) => {
              layer.msg(`${err.message}`, {
                icon: 2,
                shade: 0.3,
                shadeClose: true,
              });
            });
        }
      );
    },

    getLeaderScore(leaderData, employeeData) {
      let obj_arr = this.scoreData.filter((item) => {
        return item.leader_name == leaderData.leader;
      });
      var score = obj_arr.find((item) => {
        return item.ablity_id == employeeData.fd_id;
      });
      return score ? score.point || "" : "";
    },
    _findMeetingLevelDetailsList() {
      /*先判断有权限才可以查询(modify by huangdh@2019-05-24)*/
      if (
        this.$store.state.doubleCol.arrAuths
          .point_ablity_findMeetingLevelDetailsList
      ) {
        findMeetingLevelDetailsList({
          CompanyGroupId: this.companyGroupId,
          Year: this.year,
          Month: this.month,
          PositionGradeId: this.positionGradeId,
          DeptArea: this.deptArea,
        })
          .then((res) => {
            this.isLoading = false;
            if (!res.success) return;
            let data = res.data
              ? res.data[0]
                ? res.data[0].AblityList
                : []
              : [];
            data.map((item) => {
              if (!item.meeting_point2 || item.meeting_point2 == "") {
                item.meeting_point2 = item.avg_point;
              }
            });
            this.tableData = (data || []).map((item) => {
              if (item.standard_level == "优") {
                //item.meeting_level = item.standard_level  /*标杆的级别，也要读取【级别评定汇总合议】表的录入结果(2021-12-27)*/
              }
              return item;
            });
            this.confirmLeaderId = data[0] ? data[0].confirm_leader_id : "";
            this.scoreData = res.data
              ? res.data[0]
                ? res.data[0].AblityPointDetailsList
                : []
              : [];
            this.leaderData = res.data
              ? res.data[0]
                ? res.data[0].RelationLeaderList
                : []
              : [];
            let c = res.data[0].rateControl;
            let f = res.data[0].rateFact;
            document.getElementById("div_control").innerHTML =
              "【控制比例】A:" +
              c.rateA +
              "%（" +
              this.matchNum(c.countA, f.countA, "c") +
              "人）、B：" +
              c.rateB +
              "%（" +
              this.matchNum(c.countB, f.countB, "c") +
              "人）、C：" +
              c.rateC +
              "%（" +
              this.matchNum(c.countC, f.countC, "c") +
              "人）；总人数：" +
              c.totalCount +
              "人。";
            document.getElementById("div_fact").innerHTML =
              "【实际比例】A:" +
              f.rateA +
              "%（" +
              this.matchNum(c.countA, f.countA, "f") +
              "人）、B：" +
              f.rateB +
              "%（" +
              this.matchNum(c.countB, f.countB, "f") +
              "人）、C：" +
              f.rateC +
              "%（" +
              this.matchNum(c.countC, f.countC, "f") +
              "人）；总人数：" +
              f.totalCount +
              "人。";
          })
          .catch((err) => {
            layer.msg(`${err.message}`, {
              icon: 2,
              shade: 0.3,
              shadeClose: true,
            });
          });
      } else {
        layer.msg(`对不起，您没有权限查询本界面.`, {
          icon: 2,
          shade: 0.3,
          shadeClose: true,
        });
      }
    },
    matchNum(dataC, dataF, type) {
      if (dataC == dataF && (dataC == 0 || dataC == "")) {
        return "0";
      } else {
        if (dataC == dataF) {
          return type == "f" ? dataF : dataC;
        } else {
          return (
            "<font style='color:red'>" +
            (type == "f" ? dataF : dataC) +
            "</font>"
          );
        }
      }
    },

    showRemark(item) {
      if (item.is_standard) {
        var remark = item.bak_remark || "";
        if (remark == "") {
          remark = "无评定.";
        }
        Utils.layerBox.layerDialogOpen({
          title: "评定事例依据",
          area: ["600px", "300px"],
          btn: ["关闭"],
          content:
            `<div style="padding:10px 20px;margin:0 auto;">` +
            remark +
            `</div>`,
        });
      } else {
        this.showAblityPointDetails(item.fd_id, item.user_name);
      }
    },
    showDialog(leader) {
      let layerDialog = Utils.layerBox.layerDialogOpen({
        title: "级别评定表",
        btn: [],
        maxmin: false, //开启最大化最小化按钮
        area: ["860px", "450px"],
        btn1: (index, layero) => {},
        btn2: (index, layero) => {},
      });
      this.dialogComponent = Utils.layerBox.layerLoadVueComponent({
        layer: layerDialog,
        component: GradeEvaluationDialog,
        methods: {
          doConfirm: (params) => {
            layer.close(layerDialog);
          },
          doClose: () => {
            layer.close(layerDialog); //关闭对话框
          },
        },
        props: {
          confirmLeaderId: leader.confirm_leader_id,
          LeaderName: leader.leader,
        },
      });
    },
    showAblityPointDetails(ablityId, userName) {
      let layerDialog = Utils.layerBox.layerDialogOpen({
        title: "评定事例依据",
        btn: [],
        maxmin: false, //开启最大化最小化按钮
        area: ["760px", "420px"],
        btn1: (index, layero) => {},
        btn2: (index, layero) => {},
      });
      this.dialogComponent = Utils.layerBox.layerLoadVueComponent({
        layer: layerDialog,
        component: AblityPointDetails,
        methods: {
          doConfirm: (params) => {
            layer.close(layerDialog);
          },
          doClose: () => {
            layer.close(layerDialog); //关闭对话框
          },
        },
        props: {
          ablityId: ablityId,
          userName: userName,
        },
      });
    },
    showDescrDialog() {
      Utils.layerBox.layerDialogOpen({
        title: "使用说明",
        area: ["630px", "415px"],
        btn: ["关闭"],
        content: `<div style="padding:10px 20px;margin:0 auto;">
          <p style="line-height:24px;">
            一、本表作用：<br/>
1、用于查询各级别评定人是否评定完成；点击各评定人下方的“查看”键，可查询当前该评定人的OA评定表是否已经完成评定。<br/>
2、用于汇总各评定人的评分并在本表根据得分和级别比例控制合议确定各评定对象级别。<br/>
3、在本表触发OA的职级评定表给评定人评分。<br/>
二、合议结果<br/>
1、分值：<br/>
（1）系统自动读取平均分值的分数；<br/>
（2）分值可以修改，合议评定时可对评定分值进行调整；<br/>
（3）需确保分值与级别的合理性，不能出现甲员工的评定级别比乙员工低，但甲的评定分值大于或等于乙的情况。如：张三、李四的评定分值均为80分，若合议时，张三的级别为B、李四的级别为A，需将张三的合议分值调整至80分以下。<br/>
2、级别：<br/>
（1）系统自动根据级别人数计算的取整人数自动定级，从高级定到低级，若出现两个级别交叉点有分数相同的，先按评定分值的排序顺序确定级别，待合议时再确定其级别。<br/>
（2）级别可以修改，合议评定时可对系统确定的级别进行调整。<br/>
（3）点击“级别确定”键后，保存数据不能修改。
          </p></div>`,
      });
    },
    initPage() {
      this.pageResize();
      const { companyGroupId, year, positionGradeId } = this.$route.params;
      this.companyGroupId = companyGroupId;
      this.year = year;
      this.positionGradeId = positionGradeId;
      //console.log("test="+$store.state.doubleCol.arrAuths.point_ablity_updateMeetingLevel);
      // this.deptArea = "";
      this._findMeetingLevelDetailsList();
    },
    pageResize() {
      this.body_height = $(window).height();
    },
  },
  watch: {
    $route: function () {
      this.initPage();
    },
  },
  mounted() {
    this.initPage();
    $(window).resize(this.pageResize);
    document.getElementsByClassName("el-upload__input")[0].style =
      "display:none";
  },
};
</script>
<style scoped>
.rank-setting-container .input-sm {
  height: 24px !important;
}

.butFlex {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: flex-end;
}

.butFlex > button {
  margin-left: 10px;
}
.buts {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-top: 7px;
}
.buts > div {
  line-height: 14px;
}
.btn {
  margin-right: 10px;
}
/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}
.list-year-data {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
.scroll-table-body {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
</style>
