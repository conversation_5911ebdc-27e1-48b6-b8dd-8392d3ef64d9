<template>
  <div class="result-detail-container">
      <div class="row">
        <div class="col-xs-12 text-left">
          <h4 class="col-xs-6">{{$route.name}}</h4>
          <div class="col-xs-6 text-right" style="line-height:40px;">
            <button class="btn btn-primary btn-xs" @click="$router.push(`/${$route.params.year}/${$route.params.month}/menu/1`)">返回目录</button>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-xs-12">
            <div class="list-table">
                <div class="scroll-table" style="width:100%;height:100%;overflow-x:hidden;">
                    <table class="scroll-table-header table-bordered" :style="{'width':'calc(100% - '+12*1.4+'px)'}">
                        <colgroup>
                            <col width="50"/>
                            <col width="90"/>
                            <col width="60"/>
                            <col width="60"/>
                            <col width="60"/>
                            <col width="75"/>
                            <col width="95"/>
                            <col width="120"/>
                            <col width="90"/>
                            <col width="60"/>
                            <col width="60"/>
                            <col width="70"/>
                            <col width="60"/>
                            <col width="80"/>
                            <col width="60"/>
                            <col />
                        </colgroup>
                        <thead>
                            <tr>
                                <th colspan="3" style="text-align:left;">年度：{{$route.params.year}}年</th>
                                <th colspan="3">
                                    <div class="text-left">
                                        <label>公司：</label>
                                        <select v-model="companyGroupId" class="input-sm" style="height: 24px;padding: 0px 10px 0px 5px;">
                                            <option v-for="(company,index) in companyList" :value="company.code+''" :key="index">{{company.name}}</option>
                                        </select>
                                    </div>
                                </th>
                                <th colspan="3">
                                    <div class="text-left">
                                        <label>届别：</label>
                                        <select v-model="graduate_year" class="input-sm" style="height: 24px;padding: 0px 10px 0px 5px;">
                                            <option value="">请选择</option>
                                            <option v-for="(item,index) in geadeList" :key="index" :value="item.graduate_year">{{item.graduate_year}}</option>
                                        </select>
                                    </div>
                                </th>
                                <th colspan="7">
                                    <div class="text-left">
                                        <label>岗位级别：</label>
                                        <select v-model="schoolLevel" class="input-sm" style="height: 24px;padding: 0px 10px 0px 5px;">
                                            <option value="">请选择</option>
                                            <option v-for="level in 3" :key="level" :value="level+''">{{levelJob(level)}}级岗位</option>
                                        </select>
                                    </div>
                                    </th>
                            </tr>
                            <tr>
                                <th>序号</th>
                                <th>公司</th>
                                <th>届别</th>
                                <th>岗位级别</th>
                                <th>姓名</th>
                                <th>学历</th>
                                <th>部门</th>
                                <th>岗位</th>
                                <th>职务等级</th>
                                <th>年份</th>
                                <th>平均得分</th>
                                <th>排名</th>
                                <th>能力等级</th>
                                <th>本次评定类型</th>
                                <th>是否晋升</th>
                                <th>晋升职务等级</th>
                            </tr>
                        </thead>
                    </table>
                    <div class="scroll-table-body" :style="{overflow:'auto',maxHeight:body_height - 170 + 'px','overflow-y':'scroll','overflow-x':'hidden'}">
                        <table style="margin:0px 10px 0 0;position: relative;width:100%;font-size:13px;float:left;border:none;" class="table table-bordered table-hover table-striped">
                            <colgroup>
                                <col width="50"/>
                                <col width="90"/>
                                <col width="60"/>
                                <col width="60"/>
                                <col width="60"/>
                                <col width="75"/>
                                <col width="95"/>
                                <col width="120"/>
                                <col width="90"/>
                                <col width="60"/>
                                <col width="60"/>
                                <col width="70"/>
                                <col width="60"/>
                                <col width="80"/>
                                <col width="60"/>
                                <col />
                            </colgroup>
                            <tbody>
                                <tr v-for="(data,$index) in tableData" :key="$index">
                                    <td>{{$index+1}}</td>
                                    <td>{{getCompanyName()||''}}</td>
                                    <td>{{data.graduate_year||''}}</td>
                                    <td>{{data.school_level||''}}</td>
                                    <td>{{data.user_name||''}}</td>
                                    <td>{{data.degree||''}}</td>
                                    <td>{{data.dept_name||''}}</td>
                                    <td>{{data.job_name||''}}</td>
                                    <td>{{data.grade_name||''}}</td>
                                    <td>{{data.year_index||''}}</td>
                                    <td>{{data.avg_point||''}}</td>
                                    <td>{{$index+1}}</td>
                                    <td>{{data.meeting_level||''}}</td>
                                    <td>{{data.student_rise_type||''}}</td>
                                    <td>{{data.student_is_rise||''}}</td>
                                    <td>{{data.student_grade_name||''}}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
      </div>
  </div>
</template>

<script>
import { findAblityStudentPointListByResult,findGraduateYear } from '@/api'
export default {
  data(){
    return {
      tableData:[],
      companyGroupId:'1',
      schoolLevel:'',
      graduate_year:'',
      year:'',
      body_height:0,
      geadeList:[],//届别
      companyList:[
        {
        name:'集团总部',
        code:'1'
        },
        {
        name:'销售公司',
        code:'5'
        },
        {
        name:'赣州纸业纸品',
        code:'2'
        },
        {
        name:'广西竹林',
        code:'3'
        },
        {
        name:'崇左纸业',
        code:'6'
        },
      ]
    }
  },
  methods:{
    getCompanyName(){
        return Utils.getCompanyNameByGroupId(this.companyGroupId);
    },
    levelJob(level){
        return Utils.number2ChNum(level);
    },
    getCompanyName(){
      return Utils.getCompanyNameByGroupId(this.companyGroupId);
    },
    _findAblityStudentPointListByResult(){
        /*先判断有权限才可以查询(modify by huangdh@2019-05-24)*/
        if (this.$store.state.doubleCol.arrAuths.point_ablity_findAblityStudentPointListByResult)
        {
            findAblityStudentPointListByResult({companyGroupId:this.companyGroupId,year:this.year,graduateYear:this.graduate_year,schoolLevel:this.schoolLevel}).then(res=>{
                this.tableData = res.data || [];
            })
        }
        else {
            layer.msg(`对不起，您没有权限查询本界面.`, {icon: 2,shade:0.3,shadeClose:true});
        }
    },
    _findGraduateYear(){
        findGraduateYear().then(res=>{
            this.geadeList = res.data || [];
        })
    },
    initPage(){
      this.body_height = $(window).height();
      const {year} = this.$route.params;
      this.year = year;
      this._findAblityStudentPointListByResult();
      this._findGraduateYear();
    }
  },
  watch:{
    "$route":function(){
      this.initPage();
    },
    companyGroupId(val){
        this._findAblityStudentPointListByResult()
    },
    schoolLevel(val){
        this._findAblityStudentPointListByResult()
    },
    graduate_year(value){
        this._findAblityStudentPointListByResult();
    }
  },
  mounted(){
    this.initPage();
    $(window).resize(()=>{
      this.body_height = $(window).height();
    })
  }
}
</script>

<style>

</style>
