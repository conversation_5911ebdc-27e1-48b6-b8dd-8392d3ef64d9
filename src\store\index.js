import Vue from 'vue'
import Vuex from 'vuex'
import createPersistedState from 'vuex-persistedstate'
import app from './modules/app'
import permission from './modules/permission'
import doubleCol from './modules/doubleCol'
import getters from './getters'

Vue.use(Vuex)


const store = new Vuex.Store({
  modules: {
    app,
    permission,
    doubleCol,
  },
  getters,
  plugins: [
    createPersistedState({ storage: window.sessionStorage }),
  ]
})

export default store
