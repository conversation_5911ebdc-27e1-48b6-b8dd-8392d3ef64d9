<template>
  <div class="planning-detail-container">
      <div class="row">
        <div class="col-xs-12 text-left">
          <h4 class="col-xs-6">【{{pageTitle}}】{{jobCategory}}管培生职务规划表</h4>
          <div class="col-xs-6 text-right" style="line-height:40px;">
            <button class="btn btn-primary btn-xs" @click="$router.push(`/${$route.params.year}/${$route.params.month}/trainee/plansetting/${$route.params.graduateYear}/${$route.params.schoolLevel}/edit?type=${type}`)"
                v-if="$store.state.doubleCol.arrAuths.base_basePositionPlan_updateBasePositionPlan">编辑</button>
            <button class="btn btn-primary btn-xs" @click="$router.push(`/${$route.params.year}/${$route.params.month}/menu/2`)">返回目录</button>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-xs-6">
          <table class="table table-bordered">
            <thead>
              <tr>
                <th rowspan="2">序号</th>
                <th rowspan="2">职务等级</th>
                <th rowspan="2">年份</th>
                <th colspan="2">时间</th>
              </tr>
              <tr>
                <th>起</th>
                <th>止</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(data,$index) in tableData" :key="$index">
                <td>{{data.yearIndex}}</td>
                <td>{{data.positionGradeName}}</td>
                <td>{{data.yearName}}</td>
                <td>{{data.startYear?`${data.startYear}年${data.startMonth}月`:''}}</td>
                <td>{{data.endYear?`${data.endYear}年${data.endMonth}月`:''}}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
  </div>
</template>

<script>
import { findListByYearAndCategory } from '@/api'
export default {
  data(){
    return {
      tableData:[],
      graduateYear:'',
      schoolLevel:'',
      type:''
    }
  },
  computed:{
    jobCategory(){
      let result = "";
      switch(this.type){
        case 'scxs':
          result = '市场销售类';break;
        case 'xzrz':
          result = '行政人资类';break;
        default:
          result = '';
      }
      return result;
    },
    pageTitle(){
      let result = `${this.graduateYear}届${Utils.number2ChNum(this.schoolLevel)}级岗位`;
      return result;
    }
  },
  methods:{
    getTitle(){
      return Utils.getCompanyNameByGroupId(this.companyGroupId);
    },
    _findListByYearAndCategory(){
      /*先判断有权限才可以查询(modify by huangdh@2019-05-24)*/
      if (this.$store.state.doubleCol.arrAuths.base_basePositionPlan_findListByYearAndCategory)
      {
        findListByYearAndCategory({graduateYear:this.graduateYear,schoolLevel:this.schoolLevel,jobCategory:this.jobCategory}).then(res=>{
            this.tableData = res.data || [];
        })
      }
      else {
          layer.msg(`对不起，您没有权限查询本界面.`, {icon: 2,shade:0.3,shadeClose:true});
      }
    },
    initPage(){
      const {graduateYear,schoolLevel} = this.$route.params;
      this.graduateYear = graduateYear;
      this.type = Utils.getQueryParams('type')||'';
      this.schoolLevel = schoolLevel;
      this._findListByYearAndCategory();
    }
  },
  watch:{
    "$route":function(){
      this.initPage();
    }
  },
  created(){
    this.initPage();
  }
}
</script>

<style>

</style>
