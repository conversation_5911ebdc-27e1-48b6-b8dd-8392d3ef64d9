# 项目说明 #
>查看本文档请下载markdown插件,`vs code`插件名称为`Markdown Preview Enhanced`.
``` bash
  本项目作为公司管理系统/平台项目模板，根据公司系统界面风格所做的一套统一模板。项目使用Vue + vue-router + axios + jquery(由于使用ztree和layer) + layer(弹窗提示库) + ztree(在地址本用到)，以及自己编写的组件：地址本、表单验证、树形组件、日历组件、分页组件。
```
----------
## 项目结构 ##
```bash
│  .babelrc
│  .editorconfig
│  .gitignore
│  .postcssrc.js
│  COMPONENTS-README.md
│  favicon.ico
│  index.html
│  package.json
│  README.md
│  yarn.lock
│
├─build     //存放webpack配置文件
│      build.js
│      check-versions.js
│      logo.png
│      utils.js
│      vue-loader.conf.js
│      webpack.base.conf.js
│      webpack.dev.conf.js
│      webpack.prod.conf.js
│
├─config    //环境编译配置文件
│      dev.env.js
│      index.js
│      prod.env.js
│
├─src   //项目源码根目录
│  │  App.vue
│  │  main.js
│  │
│  ├─api    //接口访问源
│  ├─assets     //静态资源文件(会被webpack编译，引用时使用相对路径)
│  │  │  .DS_Store
│  │  │  logo.png
│  │  │
│  │  ├─css     //样式文件
│  │  │      bootstrap-theme.css
│  │  │      bootstrap-theme.css.map
│  │  │      bootstrap-theme.min.css
│  │  │      bootstrap-theme.min.css.map
│  │  │      bootstrap.css
│  │  │      bootstrap.css.map
│  │  │      bootstrap.min.css
│  │  │      bootstrap.min.css.map
│  │  │      common.scss
│  │  │
│  │  ├─fonts       //字体文件
│  │  │      glyphicons-halflings-regular.eot
│  │  │      glyphicons-halflings-regular.svg
│  │  │      glyphicons-halflings-regular.ttf
│  │  │      glyphicons-halflings-regular.woff
│  │  │      glyphicons-halflings-regular.woff2
│  │  │
│  │  └─images      //图片文件
│  │          logo.png
│  │
│  ├─components     //公共组件
│  │  │  AddressBook.vue
│  │  │  DropDownFilter.vue
│  │  │  HelloWorld.vue
│  │  │  LeftMenu.vue
│  │  │  loading.vue
│  │  │  Pagination.vue
│  │  │
│  │  ├─tree    //树形组件
│  │  │      node.js
│  │  │      store.js
│  │  │      table-tree-node.vue
│  │  │      table-tree.vue
│  │  │      tree-node.vue
│  │  │      tree.vue
│  │  │      util.js
│  │  │
│  │  └─vue2-slot-calendar  //日历组件
│  │          Calendar.vue
│  │
│  ├─permission     //权限/拦截器
│  │      index.js
│  │
│  ├─router     //路由配置
│  │      index.js
│  │
│  ├─utils      //工具类
│  │      Cookie.js
│  │      emitter.js
│  │      fetch.js
│  │      index.js
│  │      validator-directive.js
│  │      validators.js
│  │
│  └─views      //vue页面
│          Container.vue
│          Demo.vue
│          List.vue
│
└─static    //静态资源文件(不会被webpack编译，引用时必须使用绝对路径。建议只放第三方插件)
    │  .gitkeep
    │  layer.js     //layer弹窗插件
    │
    ├─theme //layer弹窗静态资源文件
    │  │  .DS_Store
    │  │
    │  └─default
    │          icon-ext.png
    │          icon.png
    │          layer.css
    │          loading-0.gif
    │          loading-1.gif
    │          loading-2.gif
    │
    └─zTree_v3      //ztree插件
        ├─css
        │  └─metroStyle
        │      │  metroStyle.css
        │      │
        │      └─img
        │              line_conn.png
        │              loading.gif
        │              metro.gif
        │              metro.png
        │
        └─js
                jquery-1.4.4.min.js
                jquery.ztree.all.js
                jquery.ztree.all.min.js
                jquery.ztree.core.js
                jquery.ztree.core.min.js
                jquery.ztree.excheck.js
                jquery.ztree.excheck.min.js
                jquery.ztree.exedit.js
                jquery.ztree.exedit.min.js
                jquery.ztree.exhide.js
                jquery.ztree.exhide.min.js
```
> ### Tip
>
```bash
1.assets和static区别

在 *.vue 组件中，所有模板和CSS都会被 vue-html-loader 及 css-loader 解析，并查找资源URL。例如，在 <img src="./logo.png">
和 background: url(./logo.png) 中，"./logo.png" 是相对的资源路径，将由Webpack解析为模块依赖。  
因为 logo.png 不是 JavaScript，当被视为模块依赖时，需要使用 url-loader 和 file-loader
处理它。vue-cli 的 webpack 脚手架已经配置了这些 loader，因此可以使用相对/模块路径。  
由于这些资源可能在构建过程中被内联/复制/重命名，所以它们基本上是源代码的一部分。这就是为什么建议将
Webpack 处理的静态资源放在 /src 目录中和其它源文件放一起的原因。事实上，甚至不必把它们全部放在 /src/assets：可以用模块/组件的组织方式来使用它们。例如，可以在每个放置组件的目录中存放静态资源。  
"Real" Static Assets
相比之下，static/ 目录下的文件并不会被 Webpack 处理：它们会直接被复制到最终目录（默认是dist/static）下。必须使用绝对路径引用这些文件，这是通过在 config.js 文件中的 build.assetsPublicPath 和 build.assetsSubDirectory 连接来确定的。  
任何放在 static/ 中文件需要以绝对路径的形式引用：/static/[filename]。如果更改 assetSubDirectory 的值为 assets，那么路径需改为 /assets/[filename]。  
详请请查看webpack对此的说明：https://athena0304.gitbooks.io/vue-template-webpack-cn/content/static.html
```
>

----------
## 日历控件 ##

![](/src/components/vue2-slot-calendar/demo.gif)

#### 使用说明 ####

```html
    <calendar
    :value="value"
    :disabled-days-of-week="disabled"
    :format="format"
    :clear-button="clear"
    :placeholder="placeholder"
    :pane="2"
    :has-input="false"
    :on-day-click="onDayClick2"
    :special-days="_dateMap"
  ></calendar>

```
#### 异步数据使用说明 ####

```html
<calendar class="event-calendar" :value="value" :disabled-days-of-week="disabled" :format="format" :clear-button="clear" :placeholder="placeholder" :pane="2" :has-input="false" :on-day-click="onDayClick3" :change-pane="changePane">
    <div v-for="evt in events" :slot="evt.date">
        ${{evt.content}} <i :class="{low : evt.low}" v-if="evt.low">↓</i>
    </div>
  </calendar>
```
>具体说明：https://github.com/icai/vue2-calendar

> 例子：http://blog.w3cub.com/vue2-calendar/
----------
## layer弹框 ##
> 使用说明详见：http://layer.layui.com/api.html
----------
## 人员选择框 ##
> 人员选择框使用layer弹框结合ztree组成,依赖jQuery
> 使用说明查看Helloworld.vue文件demo的注释。
```javascript
showAddressBookDialog(){
    let layerDialog = Utils.layerBox.layerDialogOpen({
        title: '人员选择器',
        btn:['确定','取消'],
        maxmin: false, //开启最大化最小化按钮
        area: ['860px', '590px'],
        btn1:(index,layero)=>{
        this.dialogComponent&&this.dialogComponent.$children[0].doSubmit&&this.dialogComponent.$children[0].doSubmit();
        },
        btn2:(index,layero)=>{
        this.dialogComponent&&this.dialogComponent.$children[0].doCancel&&this.dialogComponent.$children[0].doCancel();
        }
    });
    this.dialogComponent = Utils.layerBox.layerLoadVueComponent({layer:layerDialog,component:AddressBook,methods:{
        //必要参数
        doConfirm:(result)=>{
            //doConfirm可获取组件页面传过来的参数，作用：例如执行回调方法或者传递结果到父组件
            this.selectList = result
            layer.close(layerDialog);//关闭对话框
        },
        //必要参数
        doClose:()=>{
            layer.close(layerDialog);//关闭对话框
        }
        },
        //传递本组件的参数给对话框组件，对话框组件通过props属性params获取值,例如下面这个val属性取值：this.params.val
        props:{
        type:'2',//1:部门,2://人员,3:部门和人员
        }});
},
```
------------
## 表单验证
> 在src/main.js入口文件引入
```javascript
import Validator from '@/utils/validator-directive'

new Validator(Vue,{timeout:2000}).install(Vue);//加载安装表单验证指令,timeout:2000表示错误信息提示2秒之后消失，不传此参数则提示信息不会自动消失。
```
> 验证器由`validator-directive.js`和`validators.js`组成。  
`validator-directive.js`主要为指令安装方法和验证触发事件绑定；`validators.js`则是表单常用验证规则，包括类型为`number,required,email,date,length,validator,url`。其中validator类型为自定义验证方式。   
下面是使用方式：
```html
<template>
  <div class="app-management-edit-wrap">
    <form class="form-horizontal" style="padding:20px;width:600px;">
        <div class="form-group">
            <label class="col-xs-3 text-right">应用名称：</label>
            <div class="col-xs-7">
                <input v-validate="rules.fdName" v-model="form.fdName" class="form-control" placeholder="" />
            </div>
        </div>
        <div class="form-group">
            <label class="col-xs-3 text-right">应用域名：</label>
            <div class="col-xs-7">
                <input class="form-control" v-validate="rules.fdDomain" v-model="form.fdDomain" placeholder="" />
            </div>
        </div>
        <div class="form-group">
            <label class="col-xs-3 text-right">端口号：</label>
            <div class="col-xs-7">
                <input class="form-control" v-validate="rules.fdPort" v-model="form.fdPort" placeholder="" />
            </div>
        </div>
        <div class="form-group">
            <label class="col-xs-3 text-right">应用路径：</label>
            <div class="col-xs-7">
                <input class="form-control"  v-validate="rules.fdPath" v-model="form.fdPath" placeholder="" />
            </div>
        </div>
        <div class="form-group">
            <div class="col-xs-3"></div>
            <div class="col-xs-7 text-left">
                <button class="btn btn-primary" type="button" @click="doSubmit">提交</button>
                <button class="btn btn-default" type="button" @click="goBack">返回</button>
            </div>
        </div>
    </form>
  </div>
</template>

<script>
export default {
  data() {
    const formName = "appForm";
    return {
      dialogComponent: null,
      form: {
        fdName: "",
        fdDomain: "",
        fdPort: "",
        fdPath: "",
      },
      rules: {
        fdName: {
          rule: [
            {
              trigger: "blur",
              type: "validator",
              validate: value => {
                if (!value) return { success: false, msg: "应用名称不能为空" };
                return new Promise(resolve => {
                  checkName({
                    fdName: value,
                    fdId: this.$route.params.id || ""
                  })
                    .then(res => {
                      resolve(true);
                    })
                    .catch(err => {
                      resolve({ success: err.success, msg: err.msg });
                    });
                });
              }
            }
          ],
          form: formName
        },
        fdDomain: {
          rule: [
              { trigger: "blur", type: "required", message: "请输入应用域名" },
              { trigger: "change", type: "url", message: "请输入正确应用域名" }
            ],
          form: formName
        },
        fdPort: {
          rule: [
            {
              trigger: "change",
              type: "number",
              message: "请输入正确的端口"
            },
            {
              trigger: "blur",
              type: "validator",
              validate: function(value) {
                return {
                  success:
                    String(value).length < 2 ? false : true,
                  msg: "请输入最少两个字符"
                };
              }
            }
          ],
          form: formName
        },
        fdPath: {
          rule: [
            {
              trigger: "blur",
              type: "validator",
              validate: value => {
                if (!value) return { success: false, msg: "应用路径不能为空" };
                return new Promise(resolve => {
                  checkPath({
                    fdPath: value,
                    fdId: this.$route.params.id || ""
                  })
                    .then(res => {
                      resolve(true);
                    })
                    .catch(err => {
                      resolve({ success: err.success, msg: err.msg });
                    });
                });
              }
            }
          ],
          form: formName
        }
      }
    };
  },
  methods: {
    doSubmit() {
      this.$validator.submit("appForm").then(valid => {
        if (valid) {
            //do submit action
          } else {
            //do something
          }
        }
      });
    }
  }
};
</script>
```
> 验证指令为 v-validate。使用方式：`<input v-validate="rules.fdName" v-model="form.fdName" class="form-control" placeholder="" />`，`v-validate="rules.fdName"`的`rules.fdName`是验证规则，在data方法里面设置:
```javascript
rules: {
    fdName: {
        rule: [
        {
            trigger: "blur",
            type: "validator",
            validate: value => {
            if (!value) return { success: false, msg: "应用名称不能为空" };
            return new Promise(resolve => {
                checkName({
                fdName: value,
                fdId: this.$route.params.id || ""
                })
                .then(res => {
                    resolve(true);
                })
                .catch(err => {
                    resolve({ success: err.success, msg: err.msg });
                });
            });
            }
        }
        ],
        form: formName
    },
}
```
>fdName里面有rule,form两个属性。
>>rule表示验证规则。rule对象里面的属性有
* `trigger`:触发方式，同js事件类型，如`click`,`change`,`blur`等。
* `type`:验证类型，现有常有类型`number,required,email,date,length,validator,url`,其中只有`validator`类型需与属性`validate`结合使用,支持异步验证，异步验证例子请看上方fdName验证规则，非异步验证例子请看fdPort验证规则,
* `validate`:自定义验证规则，错误信息返回`Object`或者`Boolean`对象，如`{ success: err.success, msg: err.msg }`(`success`为`Boolean`类型，`msg`为错误提示字符串)或 `false`，验证通过则只需返回`true`,注意异步验证需结合`Promise`使用，返回需为`resolve({ success: err.success, msg: err.msg })`或者`resolve(false|true)`。  
>>form表示所要验证的表单名称，验证时将验证表单名称相同的表单控件。

### 提交验证
 ```javascript
doSubmit() {
      //appForm为rules验证规则属性form的值，表示验证哪个表单，valid为返回结果true或者false
      this.$validator.submit("appForm").then(valid => {
        if (valid) {
            //do submit action
          } else {
            //do something
          }
        }
      });
    }
```
-------
## 树形组件
<style>
  .demo-tree {
    .leaf {
      width: 20px;
      background: #ddd;
    }

    .folder {
      width: 20px;
      background: #888;
    }

    .buttons {
      margin-top: 20px;
    }

    .filter-tree {
      margin-top: 20px;
    }
    
    .custom-tree-container {
      display: flex;
      margin: -24px;
    }
    
    .block {
      flex: 1;
      padding: 8px 24px 24px;
      
      &:first-child {
        border-right: solid 1px #eff2f6;
      }
      
      > p {
        text-align: center;
        margin: 0;
        line-height: 4;
      }
    }
    
    .custom-tree-node {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 14px;
      padding-right: 8px;
    }
  }
</style>

<script>
  const data = [{
    label: '一级 1',
    children: [{
      label: '二级 1-1',
      children: [{
        label: '三级 1-1-1'
      }]
    }]
  }, {
    label: '一级 2',
    children: [{
      label: '二级 2-1',
      children: [{
        label: '三级 2-1-1'
      }]
    }, {
      label: '二级 2-2',
      children: [{
        label: '三级 2-2-1'
      }]
    }]
  }, {
    label: '一级 3',
    children: [{
      label: '二级 3-1',
      children: [{
        label: '三级 3-1-1'
      }]
    }, {
      label: '二级 3-2',
      children: [{
        label: '三级 3-2-1'
      }]
    }]
  }];

  const data2 = [{
    id: 1,
    label: '一级 1',
    children: [{
      id: 4,
      label: '二级 1-1',
      children: [{
        id: 9,
        label: '三级 1-1-1'
      }, {
        id: 10,
        label: '三级 1-1-2'
      }]
    }]
  }, {
    id: 2,
    label: '一级 2',
    children: [{
      id: 5,
      label: '二级 2-1'
    }, {
      id: 6,
      label: '二级 2-2'
    }]
  }, {
    id: 3,
    label: '一级 3',
    children: [{
      id: 7,
      label: '二级 3-1'
    }, {
      id: 8,
      label: '二级 3-2'
    }]
  }];

  const data3 = [{
    id: 1,
    label: '一级 2',
    children: [{
      id: 3,
      label: '二级 2-1',
      children: [{
        id: 4,
        label: '三级 3-1-1'
      }, {
        id: 5,
        label: '三级 3-1-2',
        disabled: true
      }]
    }, {
      id: 2,
      label: '二级 2-2',
      disabled: true,
      children: [{
        id: 6,
        label: '三级 3-2-1'
      }, {
        id: 7,
        label: '三级 3-2-2',
        disabled: true
      }]
    }]
  }];

  let id = 1000;

  const regions = [{
    'name': 'region1'
  }, {
    'name': 'region2'
  }];

  let count = 1;

  const props = {
    label: 'name',
    children: 'zones'
  };

  const props1 = {
    label: 'name',
    children: 'zones',
    isLeaf: 'leaf'
  };

  const defaultProps = {
    children: 'children',
    label: 'label'
  };

  export default {
    watch: {
      filterText(val) {
        this.$refs.tree2.filter(val);
      }
    },

    methods: {
      handleCheckChange(data, checked, indeterminate) {
        console.log(data, checked, indeterminate);
      },
      handleNodeClick(data) {
        console.log(data);
      },
      loadNode(node, resolve) {
        if (node.level === 0) {
          return resolve([{ name: 'region1' }, { name: 'region2' }]);
        }
        if (node.level > 3) return resolve([]);
        var hasChild;
        if (node.data.name === 'region1') {
          hasChild = true;
        } else if (node.data.name === 'region2') {
          hasChild = false;          
        } else {
          hasChild = Math.random() > 0.5;
        }

        setTimeout(function() {
          var data;
          if (hasChild) {
            data = [{
              name: 'zone' + count++
            }, {
              name: 'zone' + count++
            }];
          } else {
            data = [];
          }

          resolve(data);
        }, 500);
      },
      loadNode1(node, resolve) {
        if (node.level === 0) {
          return resolve([{ name: 'region' }]);
        }
        if (node.level > 1) return resolve([]);

        setTimeout(() => {
          const data = [{
            name: 'leaf',
            leaf: true
          }, {
            name: 'zone'
          }];

          resolve(data);
        }, 500);
      },
      getCheckedNodes() {
        console.log(this.$refs.tree.getCheckedNodes());
      },
      getCheckedKeys() {
        console.log(this.$refs.tree.getCheckedKeys());
      },
      setCheckedNodes() {
        this.$refs.tree.setCheckedNodes([
          {
            id: 5,
            label: '二级 2-1'
          },
          {
            id: 9,
            label: '三级 1-1-1'
          }
        ]);
      },
      setCheckedKeys() {
        this.$refs.tree.setCheckedKeys([3]);
      },
      resetChecked() {
        this.$refs.tree.setCheckedKeys([]);
      },
      append(data) {
        const newChild = { id: id++, label: 'testtest', children: [] };
        if (!data.children) {
          this.$set(data, 'children', []);
        }
        data.children.push(newChild);
      },

      remove(node, data) {
        const parent = node.parent;
        const children = parent.data.children || parent.data;
        const index = children.findIndex(d => d.id === data.id);
        children.splice(index, 1);
      },

      renderContent(h, { node, data }) {
        return (
          <span class="custom-tree-node">
            <span>{node.label}</span>
            <span>
              <button type="button" on-click={ () => this.append(data) }>Append</button>
              <button type="button" on-click={ () => this.remove(node, data) }>Delete</button>
            </span>
          </span>);
      },

      filterNode(value, data) {
        if (!value) return true;
        return data.label.indexOf(value) !== -1;
      }
    },

    data() {
      return {
        data,
        data2,
        data3,
        data4: JSON.parse(JSON.stringify(data2)),
        data5: JSON.parse(JSON.stringify(data2)),
        regions,
        defaultProps,
        props,
        props1,
        defaultCheckedKeys: [5],
        defaultExpandedKeys: [2, 3],
        filterText: ''
      };
    }
  };
</script>

## Tree 树形控件

用清晰的层级结构展示信息，可展开或折叠。

### 基础用法

基础的树形结构展示。

:::demo
```html
<tree :data="data" :props="defaultProps" @node-click="handleNodeClick"></tree>

<script>
  export default {
    data() {
      return {
        data: [{
          label: '一级 1',
          children: [{
            label: '二级 1-1',
            children: [{
              label: '三级 1-1-1'
            }]
          }]
        }, {
          label: '一级 2',
          children: [{
            label: '二级 2-1',
            children: [{
              label: '三级 2-1-1'
            }]
          }, {
            label: '二级 2-2',
            children: [{
              label: '三级 2-2-1'
            }]
          }]
        }, {
          label: '一级 3',
          children: [{
            label: '二级 3-1',
            children: [{
              label: '三级 3-1-1'
            }]
          }, {
            label: '二级 3-2',
            children: [{
              label: '三级 3-2-1'
            }]
          }]
        }],
        defaultProps: {
          children: 'children',
          label: 'label'
        }
      };
    },
    methods: {
      handleNodeClick(data) {
        console.log(data);
      }
    }
  };
</script>
```
:::

### 可选择

适用于需要选择层级时使用。

:::demo 本例还展示了动态加载节点数据的方法。
```html
<tree
  :props="props"
  :load="loadNode"
  lazy
  show-checkbox
  @check-change="handleCheckChange">
</tree>

<script>
  export default {
    data() {
      return {
        props: {
          label: 'name',
          children: 'zones'
        },
        count: 1
      };
    },
    methods: {
      handleCheckChange(data, checked, indeterminate) {
        console.log(data, checked, indeterminate);
      },
      handleNodeClick(data) {
        console.log(data);
      },
      loadNode(node, resolve) {
        if (node.level === 0) {
          return resolve([{ name: 'region1' }, { name: 'region2' }]);
        }
        if (node.level > 3) return resolve([]);

        var hasChild;
        if (node.data.name === 'region1') {
          hasChild = true;
        } else if (node.data.name === 'region2') {
          hasChild = false;
        } else {
          hasChild = Math.random() > 0.5;
        }

        setTimeout(() => {
          var data;
          if (hasChild) {
            data = [{
              name: 'zone' + this.count++
            }, {
              name: 'zone' + this.count++
            }];
          } else {
            data = [];
          }

          resolve(data);
        }, 500);
      }
    }
  };
</script>
```
:::

### 懒加载自定义叶子节点

:::demo 由于在点击节点时才进行该层数据的获取，默认情况下 Tree 无法预知某个节点是否为叶子节点，所以会为每个节点添加一个下拉按钮，如果节点没有下层数据，则点击后下拉按钮会消失。同时，你也可以提前告知 Tree 某个节点是否为叶子节点，从而避免在叶子节点前渲染下拉按钮。
```html
<tree
  :props="props1"
  :load="loadNode1"
  lazy
  show-checkbox>
</tree>

<script>
  export default {
    data() {
      return {
        props1: {
          label: 'name',
          children: 'zones',
          isLeaf: 'leaf'
        },
      };
    },
    methods: {
      loadNode1(node, resolve) {
        if (node.level === 0) {
          return resolve([{ name: 'region' }]);
        }
        if (node.level > 1) return resolve([]);

        setTimeout(() => {
          const data = [{
            name: 'leaf',
            leaf: true
          }, {
            name: 'zone'
          }];

          resolve(data);
        }, 500);
      }
    }
  };
</script>
```
:::

### 默认展开和默认选中
可将 Tree 的某些节点设置为默认展开或默认选中

:::demo 分别通过`default-expanded-keys`和`default-checked-keys`设置默认展开和默认选中的节点。需要注意的是，此时必须设置`node-key`，其值为节点数据中的一个字段名，该字段在整棵树中是唯一的。
```html
<tree
  :data="data2"
  show-checkbox
  node-key="id"
  :default-expanded-keys="[2, 3]"
  :default-checked-keys="[5]"
  :props="defaultProps">
</tree>

<script>
  export default {
    data() {
      return {
        data2: [{
          id: 1,
          label: '一级 1',
          children: [{
            id: 4,
            label: '二级 1-1',
            children: [{
              id: 9,
              label: '三级 1-1-1'
            }, {
              id: 10,
              label: '三级 1-1-2'
            }]
          }]
        }, {
          id: 2,
          label: '一级 2',
          children: [{
            id: 5,
            label: '二级 2-1'
          }, {
            id: 6,
            label: '二级 2-2'
          }]
        }, {
          id: 3,
          label: '一级 3',
          children: [{
            id: 7,
            label: '二级 3-1'
          }, {
            id: 8,
            label: '二级 3-2'
          }]
        }],
        defaultProps: {
          children: 'children',
          label: 'label'
        }
      };
    }
  };
</script>
```
:::

### 禁用状态
可将 Tree 的某些节点设置为禁用状态

:::demo 通过`disabled`设置禁用状态。
```html
<tree
  :data="data3"
  show-checkbox
  node-key="id"
  :default-expanded-keys="[2, 3]"
  :default-checked-keys="[5]">
</tree>

<script>
  export default {
    data() {
      return {
        data3: [{
          id: 1,
          label: '一级 2',
          children: [{
            id: 3,
            label: '二级 2-1',
            children: [{
              id: 4,
              label: '三级 3-1-1'
            }, {
              id: 5,
              label: '三级 3-1-2',
              disabled: true
            }]
          }, {
            id: 2,
            label: '二级 2-2',
            disabled: true,
            children: [{
              id: 6,
              label: '三级 3-2-1'
            }, {
              id: 7,
              label: '三级 3-2-2',
              disabled: true
            }]
          }]
        }],
        defaultProps: {
          children: 'children',
          label: 'label'
        }
      };
    }
  };
</script>
```
:::

### 树节点的选择

:::demo 本例展示如何获取和设置选中节点。获取和设置各有两种方式：通过 node 或通过 key。如果需要通过 key 来获取或设置，则必须设置`node-key`。
```html
<tree
  :data="data2"
  show-checkbox
  default-expand-all
  node-key="id"
  ref="tree"
  highlight-current
  :props="defaultProps">
</tree>

<div class="buttons">
  <button @click="getCheckedNodes">通过 node 获取</button>
  <button @click="getCheckedKeys">通过 key 获取</button>
  <button @click="setCheckedNodes">通过 node 设置</button>
  <button @click="setCheckedKeys">通过 key 设置</button>
  <button @click="resetChecked">清空</button>
</div>

<script>
  export default {
    methods: {
      getCheckedNodes() {
        console.log(this.$refs.tree.getCheckedNodes());
      },
      getCheckedKeys() {
        console.log(this.$refs.tree.getCheckedKeys());
      },
      setCheckedNodes() {
        this.$refs.tree.setCheckedNodes([{
          id: 5,
          label: '二级 2-1'
        }, {
          id: 9,
          label: '三级 1-1-1'
        }]);
      },
      setCheckedKeys() {
        this.$refs.tree.setCheckedKeys([3]);
      },
      resetChecked() {
        this.$refs.tree.setCheckedKeys([]);
      }
    },

    data() {
      return {
        data2: [{
          id: 1,
          label: '一级 1',
          children: [{
            id: 4,
            label: '二级 1-1',
            children: [{
              id: 9,
              label: '三级 1-1-1'
            }, {
              id: 10,
              label: '三级 1-1-2'
            }]
          }]
        }, {
          id: 2,
          label: '一级 2',
          children: [{
            id: 5,
            label: '二级 2-1'
          }, {
            id: 6,
            label: '二级 2-2'
          }]
        }, {
          id: 3,
          label: '一级 3',
          children: [{
            id: 7,
            label: '二级 3-1'
          }, {
            id: 8,
            label: '二级 3-2'
          }]
        }],
        defaultProps: {
          children: 'children',
          label: 'label'
        }
      };
    }
  };
</script>
```
:::

### 自定义节点内容
节点的内容支持自定义，可以在节点区添加按钮或图标等内容

:::demo 可以通过两种方法进行树节点内容的自定义：`render-content`和 scoped slot。使用`render-content`指定渲染函数，该函数返回需要的节点区内容即可。渲染函数的用法请参考 Vue 文档。使用 scoped slot 会传入两个参数`node`和`data`，分别表示当前节点的 Node 对象和当前节点的数据。注意：由于 jsfiddle 不支持 JSX 语法，所以`render-content`示例在 jsfiddle 中无法运行。但是在实际的项目中，只要正确地配置了相关依赖，就可以正常运行。
```html
<div class="custom-tree-container">
  <div class="block">
    <p>使用 render-content</p>
    <tree
      :data="data4"
      show-checkbox
      node-key="id"
      default-expand-all
      :expand-on-click-node="false"
      :render-content="renderContent">
    </tree>
  </div>
  <div class="block">
    <p>使用 scoped slot</p>
    <tree
      :data="data5"
      show-checkbox
      node-key="id"
      default-expand-all
      :expand-on-click-node="false">
      <span class="custom-tree-node" slot-scope="{ node, data }">
        <span>{{ node.label }}</span>
        <span>
          <button
            type="text"
           
            @click="() => append(data)">
            Append
          </button>
          <button
            type="text"
           
            @click="() => remove(node, data)">
            Delete
          </button>
        </span>
      </span>
    </tree>
  </div>
</div>

<script>
  let id = 1000;

  export default {
    data() {
      const data = [{
        id: 1,
        label: '一级 1',
        children: [{
          id: 4,
          label: '二级 1-1',
          children: [{
            id: 9,
            label: '三级 1-1-1'
          }, {
            id: 10,
            label: '三级 1-1-2'
          }]
        }]
      }, {
        id: 2,
        label: '一级 2',
        children: [{
          id: 5,
          label: '二级 2-1'
        }, {
          id: 6,
          label: '二级 2-2'
        }]
      }, {
        id: 3,
        label: '一级 3',
        children: [{
          id: 7,
          label: '二级 3-1'
        }, {
          id: 8,
          label: '二级 3-2'
        }]
      }];
      return {
        data4: JSON.parse(JSON.stringify(data)),
        data5: JSON.parse(JSON.stringify(data))
      }
    },

    methods: {
      append(data) {
        const newChild = { id: id++, label: 'testtest', children: [] };
        if (!data.children) {
          this.$set(data, 'children', []);
        }
        data.children.push(newChild);
      },

      remove(node, data) {
        const parent = node.parent;
        const children = parent.data.children || parent.data;
        const index = children.findIndex(d => d.id === data.id);
        children.splice(index, 1);
      },

      renderContent(h, { node, data, store }) {
        return (
          <span class="custom-tree-node">
            <span>{node.label}</span>
            <span>
              <button type="button" on-click={ () => this.append(data) }>Append</button>
              <button type="button" on-click={ () => this.remove(node, data) }>Delete</button>
            </span>
          </span>);
      }
    }
  };
</script>

<style>
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
  }
</style>
```
:::

### 手风琴模式

对于同一级的节点，每次只能展开一个

:::demo
```html
<tree
  :data="data"
  :props="defaultProps"
  accordion
  @node-click="handleNodeClick">
</tree>

<script>
  export default {
    data() {
      return {
        data: [{
          label: '一级 1',
          children: [{
            label: '二级 1-1',
            children: [{
              label: '三级 1-1-1'
            }]
          }]
        }, {
          label: '一级 2',
          children: [{
            label: '二级 2-1',
            children: [{
              label: '三级 2-1-1'
            }]
          }, {
            label: '二级 2-2',
            children: [{
              label: '三级 2-2-1'
            }]
          }]
        }, {
          label: '一级 3',
          children: [{
            label: '二级 3-1',
            children: [{
              label: '三级 3-1-1'
            }]
          }, {
            label: '二级 3-2',
            children: [{
              label: '三级 3-2-1'
            }]
          }]
        }],
        defaultProps: {
          children: 'children',
          label: 'label'
        }
      };
    },
    methods: {
      handleNodeClick(data) {
        console.log(data);
      }
    }
  };
</script>
```
:::

### Attributes
| 参数                  | 说明                                               | 类型                        | 可选值  | 默认值   |
| --------------------- | ---------------------------------------- | --------------------------- | ---- | ----- |
| data                  | 展示数据                                           | array                       | —    | —     |
| empty-text            | 内容为空的时候展示的文本                           | String                      | —    | —     |
| node-key              | 每个树节点用来作为唯一标识的属性，整棵树应该是唯一的               | String                      | —    | —     |
| props                 | 配置选项，具体看下表                               | object                      | —    | —     |
| render-after-expand   | 是否在第一次展开某个树节点后才渲染其子节点         | boolean                      | —    | true |
| load                  | 加载子树数据的方法，仅当 lazy 属性为true 时生效    | function(node, resolve)     | —    | —     |
| render-content        | 树节点的内容区的渲染 Function                      | Function(h, { node, data, store }        | —    | —     |
| highlight-current     | 是否高亮当前选中节点，默认值是 false。             | boolean                     | —    | false |
| default-expand-all    | 是否默认展开所有节点                               | boolean                     | —    | false |
| expand-on-click-node  | 是否在点击节点的时候展开或者收缩节点， 默认值为 true，如果为 false，则只有点箭头图标的时候才会展开或者收缩节点。 | boolean                     | —    | true  |
| auto-expand-parent    | 展开子节点的时候是否自动展开父节点                 | boolean                     | —    | true  |
| default-expanded-keys | 默认展开的节点的 key 的数组                        | array                       | —    | —     |
| show-checkbox         | 节点是否可被选择                                   | boolean                     | —    | false |
| check-strictly        | 在显示复选框的情况下，是否严格的遵循父子不互相关联的做法，默认为 false   | boolean                     | —    | false |
| default-checked-keys  | 默认勾选的节点的 key 的数组                        | array                       | —    | —     |
| filter-node-method    | 对树节点进行筛选时执行的方法，返回 true 表示这个节点可以显示，返回 false 则表示这个节点会被隐藏 | Function(value, data, node) | —    | —     |
| accordion             | 是否每次只打开一个同级树节点展开                   | boolean                     | —    | false |
| indent                | 相邻级节点间的水平缩进，单位为像素                 | number                     | —    | 16 |
| lazy                  | 是否懒加载子节点，需与 load 方法结合使用           | boolean                     | —    | false |

### props
| 参数       | 说明                | 类型     | 可选值  | 默认值  |
| -------- | ----------------- | ------ | ---- | ---- |
| label    | 指定节点标签为节点对象的某个属性值 | string, function(data, node) | —    | —    |
| children | 指定子树为节点对象的某个属性值 | string | —    | —    |
| disabled | 指定节点选择框是否禁用为节点对象的某个属性值 | boolean, function(data, node) | —    | —    |
| isLeaf | 指定节点是否为叶子节点，仅在指定了 lazy 属性的情况下生效 | boolean, function(data, node) | —    | —    |

### 方法
`Tree` 内部使用了 Node 类型的对象来包装用户传入的数据，用来保存目前节点的状态。
`Tree` 拥有如下方法：

| 方法名             | 说明                                       | 参数                                       |
| --------------- | ---------------------------------------- | ---------------------------------------- |
| filter          | 对树节点进行筛选操作                               | 接收一个任意类型的参数，该参数会在 filter-node-method 中作为第一个参数 |
| updateKeyChildren | 通过 keys 设置节点子元素，使用此方法必须设置 node-key 属性 | (key, data) 接收两个参数，1. 节点 key 2. 节点数据的数组 |
| getCheckedNodes | 若节点可被选择（即 `show-checkbox` 为 `true`），则返回目前被选中的节点所组成的数组 | (leafOnly) 接收一个 boolean 类型的参数，若为 `true` 则仅返回被选中的叶子节点，默认值为 `false` |
| setCheckedNodes | 设置目前勾选的节点，使用此方法必须设置 node-key 属性          | (nodes) 接收勾选节点数据的数组                      |
| getCheckedKeys  | 若节点可被选择（即 `show-checkbox` 为 `true`），则返回目前被选中的节点的 key 所组成的数组 | (leafOnly) 接收一个 boolean 类型的参数，若为 `true` 则仅返回被选中的叶子节点的 keys，默认值为 `false` |
| setCheckedKeys  | 通过 keys 设置目前勾选的节点，使用此方法必须设置 node-key 属性  | (keys, leafOnly) 接收两个参数，1. 勾选节点的 key 的数组 2. boolean 类型的参数，若为 `true` 则仅设置叶子节点的选中状态，默认值为 `false` |
| setChecked      | 通过 key / data 设置某个节点的勾选状态，使用此方法必须设置 node-key 属性 | (key/data, checked, deep) 接收三个参数，1. 勾选节点的 key 或者 data 2. boolean 类型，节点是否选中  3. boolean 类型，是否设置子节点 ，默认为 false |
| getHalfCheckedNodes | 若节点可被选择（即 `show-checkbox` 为 `true`），则返回目前半选中的节点所组成的数组  | - |
| getHalfCheckedKeys | 若节点可被选择（即 `show-checkbox` 为 `true`），则返回目前半选中的节点的 key 所组成的数组 | - |
| getCurrentKey   | 获取当前被选中节点的 key，使用此方法必须设置 node-key 属性，若没有节点被选中则返回 null | — |
| getCurrentNode  | 获取当前被选中节点的 node，若没有节点被选中则返回 null | — |
| setCurrentKey   | 通过 key 设置某个节点的当前选中状态，使用此方法必须设置 node-key 属性 | (key) 待被选节点的 key |
| setCurrentNode  | 通过 node 设置某个节点的当前选中状态，使用此方法必须设置 node-key 属性 | (node) 待被选节点的 node |
| getNode         | 根据 data 或者 key 拿到 Tree 组件中的 node | (data) 要获得 node 的 key 或者 data |
| remove          | 删除 Tree 中的一个节点 | (data) 要删除的节点的 data、key 或者 node |
| append          | 为 Tree 中的一个节点追加一个子节点 | (data, parentNode) 接收两个参数，1. 要追加的子节点的 data 2. 子节点的 parent 的 data、key 或者 node |
| insertBefore    | 为 Tree 的一个节点的前面增加一个节点  | (data, refNode) 接收两个参数，1. 要增加的节点的 data 2. 要增加的节点的后一个节点的 data、key 或者 node |
| insertAfter     | 为 Tree 的一个节点的后面增加一个节点  | (data, refNode) 接收两个参数，1. 要增加的节点的 data 2. 要增加的节点的前一个节点的 data、key 或者 node |

### Events
| 事件名称           | 说明             | 回调参数                                     |
| -------------- | -------------- | ---------------------------------------- |
| node-click     | 节点被点击时的回调      | 共三个参数，依次为：传递给 `data` 属性的数组中该节点所对应的对象、节点对应的 Node、节点组件本身。 |
| node-contextmenu | 当某一节点被鼠标右键点击时会触发该事件 | 共四个参数，依次为：event、传递给 `data` 属性的数组中该节点所对应的对象、节点对应的 Node、节点组件本身。 |
| check-change   | 节点选中状态发生变化时的回调 | 共三个参数，依次为：传递给 `data` 属性的数组中该节点所对应的对象、节点本身是否被选中、节点的子树中是否有被选中的节点 |
| check          | 当复选框被点击的时候触发 | 共两个参数，依次为：传递给 `data` 属性的数组中该节点所对应的对象、树目前的选中状态对象，包含 checkedNodes、checkedKeys、halfCheckedNodes、halfCheckedKeys 四个属性 |
| current-change | 当前选中节点变化时触发的事件 | 共两个参数，依次为：当前节点的数据，当前节点的 Node 对象          |
| node-expand    | 节点被展开时触发的事件    | 共三个参数，依次为：传递给 `data` 属性的数组中该节点所对应的对象、节点对应的 Node、节点组件本身。 |
| node-collapse  | 节点被关闭时触发的事件    | 共三个参数，依次为：传递给 `data` 属性的数组中该节点所对应的对象、节点对应的 Node、节点组件本身。 |

### Scoped slot
| name | 说明 |
|------|--------|
| — | 自定义树节点的内容，参数为 { node, data } |
-------
For a detailed explanation on how things work, check out the [guide](http://vuejs-templates.github.io/webpack/) and [docs for vue-loader](http://vuejs.github.io/vue-loader).


> ### Tip

树形组件是参照elementui中的树形组件实现，使用方式方法相同，属性大部分相同,可参照 `/src/components/HelloWorld.vue`文件例子，上述关于`tree`组件中存在本组件没有的功能，文档仅供参考。`tree`组件功能有：
* 基础用法
* 可选择
* 懒加载自定义叶子节点
* 默认展开和默认选中
* 禁用状态
* 树节点的选择
* 自定义节点内容
* 手风琴模式


----------

## Build Setup

``` bash
# install dependencies
npm install

# serve with hot reload at localhost:8080
npm run dev

# build for production with minification
npm run build

# build for production and view the bundle analyzer report
npm run build --report
```


## 错误集锦

> 若出现以下错误

```bash
> webpack-dev-server --inline --progress --config build/webpack.dev.conf.js

module.js:491
    throw err;
    ^

Error: Cannot find module 'webpack'
    at Function.Module._resolveFilename (module.js:489:15)
    at Function.Module._load (module.js:439:25)
    at Module.require (module.js:517:17)
    at require (internal/module.js:11:18)
    at Object.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\webpack-dev-server\lib\Server.js:22:17)
    at Module._compile (module.js:573:30)
    at Object.Module._extensions..js (module.js:584:10)
    at Module.load (module.js:507:32)
    at tryModuleLoad (module.js:470:12)
    at Function.Module._load (module.js:462:3)
npm ERR! code ELIFECYCLE
npm ERR! errno 1
npm ERR! hwagain-admin@1.0.0 dev: `webpack-dev-server --inline --progress --config build/webpack.dev.conf.js`
npm ERR! Exit status 1
npm ERR!
npm ERR! Failed at the hwagain-admin@1.0.0 dev script.
npm ERR! This is probably not a problem with npm. There is likely additional logging output above.
npm WARN Local package.json exists, but node_modules missing, did you mean to install?

npm ERR! A complete log of this run can be found in:
npm ERR!     C:\Users\<USER>\AppData\Roaming\npm-cache\_logs\2018-03-12T11_19_20_751Z-debug.log
error Command failed with exit code 1.
info Visit https://yarnpkg.com/en/docs/cli/run for documentation about this command.
```
> 需执行 `npm install`命令

>若出现以下错误

```bash
> webpack-dev-server --inline --progress --config build/webpack.dev.conf.js

 10% building modules 1/1 modules 0 activeevents.js:182
      throw er; // Unhandled 'error' event
      ^

Error: getaddrinfo ENOTFOUND laisf.hwagain.com
    at errnoException (dns.js:53:10)
    at GetAddrInfoReqWrap.onlookup [as oncomplete] (dns.js:95:26)
npm ERR! code ELIFECYCLE
npm ERR! errno 1
npm ERR! hwagain-admin@1.0.0 dev: `webpack-dev-server --inline --progress --config build/webpack.dev.conf.js`
npm ERR! Exit status 1
npm ERR!
npm ERR! Failed at the hwagain-admin@1.0.0 dev script.
npm ERR! This is probably not a problem with npm. There is likely additional logging output above.

npm ERR! A complete log of this run can be found in:
npm ERR!     C:\Users\<USER>\AppData\Roaming\npm-cache\_logs\2018-03-12T11_27_38_418Z-debug.log
error Command failed with exit code 1.
info Visit https://yarnpkg.com/en/docs/cli/run for documentation about this command.
```
>看关键错误`Error: getaddrinfo ENOTFOUND laisf.hwagain.com`,打开`src/config/index.js`，查找`host: 'laisf.hwagain.com'`，把这个host改成自己的host，例如`zhangsan.hwagain.com`，须是`*.hwagain.com`域名，因为做了登录拦截，只有此类型域名才能获取到登录信息。注：host在`C:\Windows\System32\drivers\etc\host`文件添加或修改


