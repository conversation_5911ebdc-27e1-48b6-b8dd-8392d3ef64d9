<template>
  <div class="app-wrap">
    <nav class="navbar navbar-inverse navbar-fixed-top nav-bg" >
      <img :src="require('@/assets/images/logo.png')" class="logo"/>
      <div class="system-name"><h3>{{this.$route.params.year?`【${getHalfOrFullYearName()}年度】`:''}}综合能力评定系统【{{siteVersion()}}版】</h3></div>
      <ul class="nav-right-menu">
        <li @click="leftMenuVisible(true)"><i class="glyphicon glyphicon-home"></i>返回菜单</li>
        <li><i class="glyphicon glyphicon-user"></i>{{loginUser.name}}</li>
        <li @click="logout()"><i class="glyphicon glyphicon-log-out"></i>注销</li>
      </ul>
    </nav>
    <left-menu></left-menu>
    <div class="app-main" >
      <div class="masker" v-if="maskerShow" @click="leftMenuVisible(false)"></div>
      <router-view/>
    </div>
  </div>
</template>

<script>
import LeftMenu from '@/components/LeftMenu'
import { getCookie,UserKey } from '@/utils/Cookie'
import { findAuths } from '@/api'
export default {
  name: 'app',
  data(){
    return {
      maskerShow:false,
      arrAuths:{},
      loginUser:{},
      titleType:''
    }
  },
  components:{
    LeftMenu
  },
  methods:{
    siteVersion(){
      return process.env.NODE_ENV === 'production' ? '正式' : '测试';
    },
    getHalfOrFullYearName(){
      let url=window.location.href.toLowerCase();
      let url1=url.replace("trainee","");
      let url2=url.replace("menu/2","");

      if (url.length>url1.length || url.length>url2.length){
        let year=this.$route.params.year;
        let month=this.$route.params.month;
        let strYear=month==6?((parseInt(year)-1)+"-"+year):(year+"-"+(parseInt(year)+1));
        if(this.$route.params.month){
          return strYear+(month == '12' ? '半' : '');
        }else{
          return strYear+(month == '12' ? '半' : '');
        }
      }
      else {
        if(this.$route.params.month){
          return this.$route.params.year+(this.$route.params.month == '6' ? '半' : '');
        }else{
          return this.$route.params.year+(this.$route.meta.month == '6' ? '半' : '');
        }
      }      
    },
    logout(){
      window.location.href = process.env.LOGIN_PATH+window.location.href;
    },
    resizeAppMain(){
      var cHeight = Math.max(document.body.clientHeight, document.documentElement.clientHeight)
      var sHeight = Math.max(document.body.scrollHeight, document.documentElement.scrollHeight)
      var height  = Math.max(cHeight, sHeight)
      $(".app-wrap>.app-main").css({"height":height - 60 +'px'});
    },
    leftMenuVisible(flag){
      //如果返回到导航菜单系统
      this.$router.push("/yearmenu");
      return ;
      // window.location.href = process.env.MENU_PATH;//MENU_PATH在config/xx.env.js配置
      // this.maskerShow = flag;
      // if(flag){
      //   $(".app-wrap>.app-left-menu-wrap").animate({left:'0'},'fast')
      // }else{
      //   $(".app-wrap>.app-left-menu-wrap").animate({left:'-200px'},'fast')
      // }
    },
    _auths(){
      findAuths().then(res=>{
        this.arrAuths = res.data || {};
        this.$store.commit('authsValue',this.arrAuths);
      }).catch(err=>{
        layer.msg(`${err.msg}`, {icon: 2,shade:0.3,shadeClose:true});
      })
    },
  },
  mounted(){
    this.resizeAppMain();    
    this.loginUser = JSON.parse(getCookie(UserKey) || "{}");
    $(window).resize(()=> {
        this.resizeAppMain();
    });
    this._auths();
  }
}
</script>



<style lang="scss">
  .nav-bg {
    background: #19a3ff;
    .system-name {
      display: inline-block;
      position: absolute;
      left: 0;
      bottom: 0;
      right: 0;
      top:-10px;
      z-index: -1;
      h3 {
        font-size: 16px;
        color: #fff;
      }
    }
  }
</style>
