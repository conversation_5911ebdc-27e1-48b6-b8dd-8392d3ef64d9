<template>
    <div style="width: 1100px;" class="years-result-summary-container">
        <div class="row">
            <div class="col-xs-12 text-left">
                <h4 class="col-xs-7">
                    {{ $route.query.deptName + $route.name }}
                </h4>
                <div class="col-xs-5 text-right" style="padding: 0 20px 0 0;line-height: 38px;">
                    <!-- push(`/${$route.params.year}/${$route.params.month}/menu/1`) -->
                    <button class="btn btn-primary btn-xs" @click="$router.go(-1)">返回上级</button>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12">
                <div class="list-table">
                    <div class="scroll-table" style="height:100%;overflow-x:hidden;background:none">
                        <table class="scroll-table-header table-bordered"
                        :style="{ width: `calc(100% - ${1.4 * 12 + 'px'})` }">
                            <colgroup>
                                <col style="width:40px" />
                                <col style="width:70px" />
                                <col style="width:50px" />
                                <col style="width:40px" />
                                <col style="width:40px" />
                                <col style="width:55px" />
                                <col style="width:70px" />
                                <col style="width:55px" />
                                <col style="width:55px" />
                                <col style="width:55px" />
                                <col style="width:55px" />

                            </colgroup>
                            <thead>
                                <tr>
                                    <th rowspan="2">序号</th>
                                    <th rowspan="2">职等</th>
                                    <th rowspan="2">姓名</th>
                                    <th rowspan="2">性别</th>
                                    <th rowspan="2">年龄</th>
                                    <th rowspan="2">学历</th>
                                    <th rowspan="2">岗位</th>
                                    <th colspan="2">评定结果</th>
                                    <th rowspan="2">确认情况</th>
                                    <th rowspan="2">备注</th>
                                </tr>
                                <tr>
                                    <th>级别</th>
                                    <th>职级</th>
                                </tr>

                            </thead>
                        </table>
                        <div class="scroll-table-body"
                            :style="{ overflow: 'auto', maxHeight: body_height - 160 + 'px', 'overflow-y': 'scroll', 'overflow-x': 'hidden' }">
                            <table
                                style="margin:0px 10px 0 0;position: relative;width:100%;font-size:13px;float:left;border:none;"
                                class="table table-bordered table-hover table-striped">
                                <colgroup>
                                    <col style="width:40px" />
                                    <col style="width:70px" />
                                    <col style="width:50px" />
                                    <col style="width:40px" />
                                    <col style="width:40px" />
                                    <col style="width:55px" />
                                    <col style="width:70px" />
                                    <col style="width:55px" />
                                    <col style="width:55px" />
                                    <col style="width:55px" />
                                    <col style="width:55px" />
                                </colgroup>
                                <tbody>
                                    <tr v-for="(data, $index) in tableData" :key="$index">
                                        <td>{{ $index + 1 }}</td>
                                        <td>{{ data.gradeName }}</td>
                                        <td>{{ data.userName }}</td>
                                        <td>{{ data.sex }}</td>
                                        <td>{{ data.age }}</td>
                                        <td>{{ data.degree }}</td>
                                        <td>{{ data.positionName }}</td>
                                        <td>{{ data.level }}</td>
                                        <td>{{ data.result }}</td>
                                        <td>{{ data.confirmStatusText }}</td>
                                        <td>{{ data.remark }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div v-if="isNotData" style="text-align: center;padding: 15px 0;">暂无数据......</div>
    </div>
</template>

<script>
import { resultConfirmListByDept } from '@/api'

export default {
    data() {
        return {
            tableData: [],
            body_height: 0,
            year: "",
            month: "",
            companyGroupId: "",
            positionGradeId: "",
            isNotData: false
        }
    },
    methods: {
        _resultConfirmListByDept() {
            const loading = this.$loading({
                lock: true,
                text: 'Loading',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            });
            /*先判断有权限才可以查询(modify by huangdh@2019-05-24)*/
            // if (this.$store.state.doubleCol.arrAuths.point_ablity_findYearReport) {
            resultConfirmListByDept({ fdId: this.$route.query.id }).then(res => {
                if (!res.success) return;
                this.tableData = res.data || []
                loading.close();
                if (res.data.length == 0) {
                    this.isNotData = true
                }
            }).catch(err => {
                loading.close();
                this.isNotData = true
                layer.msg(`${err.msg}`, { icon: 2, shade: 0.3, shadeClose: true });
            })
            // }
            // else {
            //     layer.msg(`对不起，您没有权限查询本界面.`, { icon: 2, shade: 0.3, shadeClose: true });
            // }
        },



        initPage() {
            this.pageResize();
            const { companyGroupId, year, month, positionGradeId } = this.$route.params;
            this.companyGroupId = companyGroupId;
            this.year = year;
            this.month = month;
            this.positionGradeId = positionGradeId;
            // this.deptArea = "";
            this._resultConfirmListByDept();
        },
        pageResize() {
            this.body_height = $(window).height();
        },

    },
    // watch: {
    //     "$route": function () {
    //         this.initPage();
    //     }
    // },
    mounted() {
        this.initPage();
        $(window).resize(this.pageResize);
    }
}
</script>
