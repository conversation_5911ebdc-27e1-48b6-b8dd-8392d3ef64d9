<template>
  <div id="app">
    <router-view></router-view>
  </div>
</template>

<script>
export default {
  name: 'app',
}
</script>

<style lang="scss">
#app {
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  min-width:1263px !important;
  .glyphicon {
    cursor: pointer;
  }
  .row {
    margin:0;
    padding-left:15px;
    padding-right:15px;
    box-sizing: border-box;
  }
  .input-sm {
    height:28px;
    padding: 5px 10px 5px 5px;
  }
  .m-pagination {
    display: inline-block;
    padding: 0;
    margin: 0;
    font-size: 0;
    list-style: none;
    user-select: none;
    &> .paging-item {
        display: inline;
        font-size: 12px;
        position: relative;
        padding: 2px 5px;
        line-height: 1.42857143;
        text-decoration: none;
        border: 1px solid #ccc;
        background-color: #fff;
        cursor: pointer;
        color: #0275d8;
        margin-left: 5px;
        &:first-child {
            margin-left: 0;
        }
        &:hover {
            background-color: #f0f0f0;
            color: #0275d8;
        }
        &.paging-item--disabled,
        &.paging-item--more{
            background-color: #fff;
            color: #505050;
        }
        //禁用
        &.paging-item--disabled {
            cursor: not-allowed;
            opacity: .75;
        }
        &.paging-item--more,
        &.paging-item--current {
            cursor: default;
        }
        //选中
        &.paging-item--current {
            background-color: #0275d8;
            color:#fff;
            position: relative;
            z-index: 1;
            border-color: #0275d8;
        }
    }
  }
  .v-table-class{
    font-size: 13px;
  }
  .v-table-body {
    overflow:auto !important;
  }
  .v-table-header {
    background:#b7d8dc !important;
    .v-table-title-cell {
      height:32px !important;
      line-height: 32px !important;
    }
  }
  .v-table-body-cell {
    height:32px !important;
    line-height: 32px !important;
  }

  div.scroll-table {
    width:100%;
    height:320px;
    display:inline-block;
    float:left;
    background:#f4fafb;
    position: relative;
    border-bottom: 1px solid #ccc;
    table.scroll-table-header {
      width:100%;
      font-size:13px;
      // display:block;
      // padding-right:17px;
    }
    table.scroll-table-body {
      &>table{
        border-bottom: none;
      }
    }
  }
  table thead {
     tr {
       th {
          text-align: center;
          border-bottom: none;
          padding:2px 5px;
          vertical-align: middle;
          background: #b7d8dc;
          height: 32px;
          font-size: 12px;
          color:#333;
          &>* {
            background:#b7d8dc !important;
            outline: none;
          }
        }
     }
     &.hide-thead-th>tr>th {
      height: 0;
      padding: 0;
      background: transparent;
      border: none;
    }
  }
  .table tbody tr {
    td {
      padding:2px 5px;
      vertical-align: middle;
      height: 27px;
      font-size: 12px;
      word-break: break-all;
      .btn {
        padding-top:0;
        padding-bottom:0;
      }
    }
    &:nth-child(odd){
      background: #fff;
    }
    &:nth-child(even) {
      background: #f9f9f9;
    }
  }
  .app-main {
    margin-top:36px;
    padding: 0;
    box-sizing: border-box;
  }
  .navbar {
    height: 36px;
    min-height: 36PX;
    img.logo {
      height: 30px;
      position: absolute;
      left: 20px;
      top: 4px;
    }
    .nav-right-menu {
      float: right;
      padding: 0 15px;
      margin:0;
      line-height: 36px;
      color:#fff;
      li {
        display: inline-block;
        padding:0 10px 0 20px;
        cursor: pointer;
        &:hover {
          // background: #92d5f5;
          color:#fff;
        }
        i {
          margin-right: 5px;
        }
      }
    }
  }
  .masker {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: .5;
    background: #000;
    z-index:1000;
  }

}
</style>
