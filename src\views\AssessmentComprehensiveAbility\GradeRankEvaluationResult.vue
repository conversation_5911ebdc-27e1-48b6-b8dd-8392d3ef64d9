<template>
  <div class="grade-rank-evaluation-result-container">
      <div class="row">
        <div class="col-xs-12 text-left">
          <h4 class="col-xs-7">{{this.$route.params.year}}{{this.month==`6`?`半`:``}}年度{{getCompanyName()}}（{{getGradeName()}}）{{$route.name}}</h4>
          <div class="col-xs-5 text-right" style="padding: 0;line-height: 38px;">
            <button class="btn btn-primary btn-xs" @click="$router.push(`/${$route.params.year}/${$route.meta.month}/menu/1`)">返回目录</button>
          </div>
        </div>
        <div class="col-xs-6"></div>
      </div>
      <div class="row">
        <div class="col-xs-12">
          <div class="list-table">
            <div class="scroll-table" style="height:100%;overflow-x:hidden;">
                <table class="scroll-table-header table-bordered">
                  <colgroup>
                    <col style="width:40px"/>
                    <col style="width:100px"/>
                    <col style="width:70px"/>
                    <col style="width:40px"/>
                    <col style="width:40px"/>
                    <col style="width:50px"/>
                    <col style="width:130px"/>
                    <col style="width:100px"/>
                    <col style="width:90px"/>
                    <col style="width:90px"/>
                    <col style="width:100px"/>
                  </colgroup>
                  <thead>
                    <tr>
                      <th>序号</th>
                      <th>厂部</th>
                      <th>姓名</th>
                      <th>性别</th>
                      <th>年龄</th>
                      <th>学历</th>
                      <th>岗位</th>
                      <th>职等</th>
                      <th>合议分值</th>
                      <th>评定级别</th>
                      <th>评定职级</th>
                    </tr>
                  </thead>
                </table>
                <div class="scroll-table-body" :style="{overflow:'auto',maxHeight:body_height - 130 + 'px','overflow-y':'scroll','overflow-x':'hidden'}">
                    <table style="margin:0px 10px 0 0;position: relative;font-size:13px;float:left;border:none;" class="table table-bordered table-hover table-striped">
                        <colgroup>
                          <col style="width:40px"/>
                          <col style="width:100px"/>
                          <col style="width:70px"/>
                          <col style="width:40px"/>
                          <col style="width:40px"/>
                          <col style="width:50px"/>
                          <col style="width:130px"/>
                          <col style="width:100px"/>
                          <col style="width:90px"/>
                          <col style="width:90px"/>
                          <col style="width:100px"/>
                        </colgroup>
                        <tbody>
                          <tr v-for="(data,$index) in tableData" :key="$index">
                            <td>{{$index+1}}</td>
                            <td>{{data.deptName||''}}</td>
                            <td>{{data.userName||''}}</td>
                            <td>{{data.gender||''}}</td>
                            <td>{{data.age||''}}</td>
                            <td>{{data.degree||''}}</td>
                            <td>{{data.jobName||''}}</td>
                            <td>{{data.gradeName||''}}</td>
                            <td>{{data.status == 30 ? (data.meetingPoint2 || data.avgPoint) : ''}}</td>
                            <td>{{data.status == 30 ? (data.meetingLevel||'') : ''}}</td>
                            <td>{{data.status == 30 ? (data.approveResult||data.checkerResult||data.leaderResult):''}}</td>
                          </tr>
                      </tbody>
                    </table>
                </div>
            </div>
          </div>
        </div>
      </div>
  </div>
</template>

<script>
import { findCheckedLastResult } from '@/api'
export default {
  data(){
    return {
      tableData:[],
      body_height:0,
      year:"",
      month:this.$route.meta.month,
      companyGroupId:"",
      positionGradeId:"",
      deptArea:Utils.getQueryParams('deptArea')
    }
  },
  methods:{
    getCompanyName(){
      let companyName=Utils.getCompanyNameByGroupId(this.companyGroupId);
      if (this.positionGradeId=="0006" || this.positionGradeId=="0007" || this.positionGradeId=="0032" || this.positionGradeId=="0033"){
        companyName="全集团";
      }
      return companyName;
    },
    getGradeName(){
      let name=Utils.getGradeLevlByGradeId(this.positionGradeId);
      if (this.deptArea=="A"){
        name=name+"-生产一线"
      }
      else if (this.deptArea=="B"){
        name=name+"-生产辅助"
      }
      else if (this.deptArea=="C"){
        name=name+"-生产支持"
      }
      return name;
    },
    _findCheckedLastResult(){
      /*先判断有权限才可以查询(modify by huangdh@2019-05-24)*/
      if (this.$store.state.doubleCol.arrAuths.point_ablity_findCheckedLastResult)
      {
          findCheckedLastResult({CompanyGroupId:this.companyGroupId,Year:this.year,Month:this.month,PositionGradeId:this.positionGradeId,DeptArea:this.deptArea}).then(res=>{
            if(!res.success) return;
            this.tableData = res.data ? res.data : [];
          }).catch(err=>{
            layer.msg(`${err.msg}`, {icon: 2,shade:0.3,shadeClose:true});
          })
      }
      else {
          layer.msg(`对不起，您没有权限查询本界面.`, {icon: 2,shade:0.3,shadeClose:true});
      }
    },
    initPage(){
      this.pageResize();
      const {companyGroupId,year,positionGradeId} = this.$route.params;
      this.companyGroupId = companyGroupId;
      this.year = year;
      this.positionGradeId = positionGradeId;
      // this.deptArea = "";
      this._findCheckedLastResult();
    },
    pageResize(){
      this.body_height = $(window).height();
    }
  },
  watch:{
    "$route":function(){
      this.initPage();
    }
  },
  mounted(){
    this.initPage();
    $(window).resize(this.pageResize);
  }
}
</script>

<style scoped>
/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}
.list-year-data {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
.scroll-table-body {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
</style>
