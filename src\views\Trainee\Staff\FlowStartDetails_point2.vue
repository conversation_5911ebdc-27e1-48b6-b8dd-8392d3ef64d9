<template>
  <div class="grade-evaluation-dialog-container">
      <div class="row">
        <div style="font-size:20px;font-weight:bold;">
          {{this.month==6?(this.year-1):this.year}}-{{this.month==6?this.year:(this.year+1)}}{{this.month==12?"半":""}}年度{{this.companyGroupName}}管培生综合能力评定操作表</div>
        <div class="col-xs-12">
          <div style="float:left;margin-right:20px;margin-top:10px;text-align:left;">
              <span style="margin-left:30px;font-weight:bold;">评定对象：</span>{{tableData.userName}}
              <span style="margin-left:30px;font-weight:bold;">部门：</span>{{tableData.deptName}}
              <span style="margin-left:30px;font-weight:bold;">职务：</span>{{tableData.jobName}}
              <span style="margin-left:30px;font-weight:bold;">职等：</span>{{tableData.gradeName}}       
              <div style="color:red;">
                  <span style="margin-left:30px;font-weight:bold;">评定提醒：</span>若是选择优、A、D级，必须填写案例说明，否则不能保存。  
              </div>       
           </div>
           <div style="float:right;margin-right:30px;margin-top:10px;margin-bottom:10px;">
              <button type="button" class="btn btn-primary btn-xs" :disabled="true" >保存</button>
              <button type="button" class="btn btn-primary btn-xs" @click="backPage()">返回上级</button>
           </div>
          <table class="table table-bordered">
            <thead>
              <tr>
                <th rowspan="2" style="width:40px;">序号</th>
                <th rowspan="2" style="width:150px;">评定项目</th>
                <th rowspan="2" style="width:300px;">评分标准（B级标准）</th>
                <th colspan="5" >综合分评定</th>
                <th rowspan="2" style="width:60px;">评定得分</th>
                <th rowspan="2" style="width:60px;">案例说明</th>
                <th rowspan="2" style="width:60px;">能力等级</th>
                <th rowspan="2" style="width:60px;">考核结论</th>
              </tr>
              <tr>
                <th style="width:80px;">优<br>（85分以上）</th>
                <th style="width:70px;">A<br>（80-84分）</th>
                <th style="width:70px;">B<br>（75-79分）</th>
                <th style="width:70px;">C<br>（70-74分）</th>
                <th style="width:80px;">D<br>（70分以下）</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td colspan="12" style="text-align:left;font-weight:bold;">综合分评定</td>
              </tr>
              <tr>         
                <td>1</td>
                <td>综合分评定</td>
                <td style="text-align:left;">
                  综合分评定是根据评定对象的整体能力表现进行一个综合评定，评定人不熟悉成功要素，采用综合分评定。
                </td>
                <td>                  
                  <input name="item1" v-if="ablityPoint.totalPoint>=85 && ablityPoint.totalPoint<=100" id='Radio4' type='radio' checked='checked'  @click="showPoint('select_1',85)"/>
                  <input name="item1" v-else id='Radio4' type='radio' @click="showPoint('select_1',85)"/>
                </td>
                <td>
                  <input name="item1" v-if="ablityPoint.totalPoint>=80 && ablityPoint.totalPoint<85" id='Radio3' type='radio' checked='checked'  @click="showPoint('select_1',80)"/>
                  <input name="item1" v-else id='Radio3' type='radio' @click="showPoint('select_1',80)"/>
                </td>
                <td>
                  <input name="item1" v-if="ablityPoint.totalPoint>=75 && ablityPoint.totalPoint<80" id='Radio2' type='radio' checked='checked'  @click="showPoint('select_1',75)"/>
                  <input name="item1" v-else id='Radio2' type='radio' @click="showPoint('select_1',75)"/>
                </td>
                <td>
                  <input name="item1" v-if="ablityPoint.totalPoint>=70 && ablityPoint.totalPoint<75" id='Radio1' type='radio' checked='checked'  @click="showPoint('select_1',70)"/>
                  <input name="item1" v-else id='Radio1' type='radio' @click="showPoint('select_1',70)"/>
                </td>
                <td>
                  <input name="item1" v-if="ablityPoint.totalPoint<70" id='Radio0' type='radio' checked='checked' />
                  <input name="item1" v-else id='Radio0' type='radio' @click="showPoint('select_1',60)"/>
                </td>
                <td>
                  <select ref="select_1" style="width:95%;height:24px;">
                  </select>
                </td>
                <td>
                  <a href="#" @click="showInstance(ablityPoint.textTotal)">修改</a>
                </td>
                <td>
                  <div ref="div_ablityLevel" ></div>
                </td>
                <td>
                  <div ref="div_stayStatus" ></div>
                </td>
              </tr>
              <tr>
                <td colspan="12" style="text-align:left;font-weight:bold;">积极态度及稳定性</td>
              </tr>
              <tr>
                <td>2</td>
                <td >
                  积极向上、锐意进取的态度
                </td>
                <td style="text-align:left;">
                  1、品质分主要是以内在品质为评分依据，表象为辅。过于自信、盲目好强、固执等不属于积极向上、锐意进取的品质。<br>
                  <div ref="div_20" style="display:none;" >
                    2、名词解析：<br>
                    （1）积极向上：正面的、努力进取的。<br>
                    （2）锐意进取：意志坚决地追求上进，下决心强化自身，力图有所作为。<br>
                  </div>                 
                  <div ><div ref="div_220" class="divItem">..................</div> 
                       <span ref="span_20" class="spanItem" @click="showText('div_20','div_220','span_20')">点击查看全部</span>
                  </div>
                </td>
                <td colspan="2" style="text-align:left;">
                  <input type="text" ref="txt_energyPoint" style="width:98%;height:30px;" title="请录入分数（70分以上视为积极，满分100分）" maxlength="3" @change="editEnergyPoint(1)" />
                </td>
                <td colspan="5" style="text-align:left;">
                  <textarea ref="txt_energyReason" style="width:98%;height:50px;" title="请录入评价理由"></textarea>
                </td>
                <td colspan="2">
                  <div ref="div_energy"></div>
                </td>
              </tr>
              <tr>
                <td>3</td>
                <td >
                  稳定性
                </td>
                <td style="text-align:left;">
                  1、婚恋情况：对象从事职业、行业以及工作地点与我司条件相符或者相近的；同时能接受异地恋，即使结婚后也能接受两地分居等，稳定性较好。<br>
                  <div ref="div_21" style="display:none;" >
                    2、工作安排认可度：工作热情较高， 能够积极完成领导安排的工作，不推脱，不抱怨等，稳定性较好。<br>
                    3、企业文化认可度：能够适应我司的日常管理制度，认可企业发展理念等，稳定性较好。<br>
                  </div>                 
                  <div ><div ref="div_221" class="divItem">..................</div> 
                       <span ref="span_21" class="spanItem" @click="showText('div_21','div_221','span_21')">点击查看全部</span>
                  </div>
                </td>
                <td colspan="2" style="text-align:left;">
                  <input type="text" ref="txt_stablityPoint" style="width:98%;height:30px;" title="请录入分数（70分以上视为稳定，满分100分）" maxlength="3"  @change="editEnergyPoint(2)" />
                </td>
                <td colspan="5" style="text-align:left;">
                  <textarea ref="txt_stablityReason" style="width:98%;height:50px;" title="请录入稳定性评价理由."></textarea>
                </td>
                <td colspan="2">
                  <div ref="div_stablity"></div>
                </td>
              </tr>
            </tbody>
          </table>
          <div style="text-align:left;">
            <strong>评定说明：</strong><br/>
            1、能力等级说明：能力等级为优、A、B、C级视为合格，D级视为不合格<br/>
              （1）优级：提资时间按照A级（6个月提资一次）执行，同时可考虑提前晋升；<br/>
              （2）A级：6个月提资一次；<br/>
              （3）B级：9个月提资一次；<br/>
              （4）C级：12个月提资一次；<br/>
              （5）D级：原则上劝退；<br/>
              最终能力等级需合议确定。<br/>
              2、积极态度：若该项目合格结果为不积极，则一票否决，原则上劝退。<br/>
              3、稳定性：若该项目合格结果不稳定，则一票否决，原则上劝退。
          </div>         

        </div>
      </div>
  </div>
</template>

<script>
import { findOne,findOneByAblityId } from "@/api";
export default {
  data() {
    return {
        body_height: 0,
        year: "",
        month: "", 
        companyGroupId: "",
        companyGroupName: "",
        tableData: "",
        ablityPoint: ""
    };
  },  

  methods: {
      findObject() {
        findOne({
          fdId:this.$route.query.ablityId
        }).then(res => {
          if (!res.success) return;
          this.tableData = res.data ? res.data : [];
        }).catch(err=>{
          layer.msg(`${err.msg}`, {icon: 2,shade:0.3,shadeClose:true});
        });
      },
      showPoint(key,point){
        let html="",value=0,maxId=4;
        if (point>=85 && point<=100){
          value=85;
          maxId=15;         
          this.$refs["div_ablityLevel"].innerHTML ="优";
        }
        else if (point>=80 && point<=84){
          value=80;
          this.$refs["div_ablityLevel"].innerHTML ="A";
        }
        else if (point>=75 && point<=79){
          value=75;
          this.$refs["div_ablityLevel"].innerHTML ="B";
        }
        else if (point>=70 && point<=75){
          value=70;
          this.$refs["div_ablityLevel"].innerHTML ="C";
        }
        else if (point<=69){
          value=0;
          maxId=69;    
          this.$refs["div_ablityLevel"].innerHTML ="D";
        }
        while (maxId>=0){
            if ((value+maxId)==point){
              html=html+"<option value='"+(value+maxId)+"' selected>"+(value+maxId)+"</option>";
            }
            else {
              html=html+"<option value='"+(value+maxId)+"'>"+(value+maxId)+"</option>";       
            }                               
            maxId=maxId-1;
        }
        if (point>=70){
          this.$refs["div_stayStatus"].innerHTML ="合格";
        }
        else {
          this.$refs["div_stayStatus"].innerHTML ="不合格";
        }
        this.$refs[key].innerHTML =html;
      },
      editEnergyPoint(type){
        if (type==1){
          let point=this.$refs["txt_energyPoint"].value;
          if (point==""){
            this.$refs["div_energy"].innerHTML="";
          }
          else if (point>=70 && point<=100){
            this.$refs["div_energy"].innerHTML="积极";
          }
          else {
            this.$refs["div_energy"].innerHTML="不积极";
          }   
        }
        else if (type==2){
          let point=this.$refs["txt_stablityPoint"].value;
          if (point==""){
            this.$refs["div_stablity"].innerHTML="";
          }
          else if (point>=70 && point<=100){
            this.$refs["div_stablity"].innerHTML="稳定";
          }
          else {
            this.$refs["div_stablity"].innerHTML="不稳定";
          }   
        }            
      },
      _findOneByAblityId(){
        findOneByAblityId({
            ablityId:this.$route.query.ablityId,
            leaderName:this.$route.query.leaderName
          }).then(res => {
            if (!res.success) return;
              this.ablityPoint = res.data ? res.data : [];
              this.year = this.ablityPoint.fdYear;
              this.month = this.ablityPoint.fdMonth;
              this.companyGroupName=this.ablityPoint.companyGroupName;

              if (this.ablityPoint.totalPoint!=null){
                this.showPoint('select_1',this.ablityPoint.totalPoint);
                this.$refs["txt_energyPoint"].value =this.ablityPoint.energyPoint;
                this.$refs["txt_energyReason"].value =this.ablityPoint.energyReason;
                this.editEnergyPoint(1);
                this.$refs["txt_stablityPoint"].value =this.ablityPoint.stablityPoint;
                this.$refs["txt_stablityReason"].value =this.ablityPoint.stablityReason;
                this.editEnergyPoint(2);
              }

          }).catch(err=>{
            layer.msg(`${err.msg}`, {icon: 2,shade:0.3,shadeClose:true});
          });   
      },
      backPage(){
         const {
              companyGroupId,
              year,
              month,
              positionGradeId
          } = this.$route.params;
          this.companyGroupId = companyGroupId;
          this.year = year;
          this.month = month;
        this.$router.push({
          path: '/'+this.year+'/'+this.month+'/Trainee/Staff/'+this.companyGroupId+'/FlowStartDetails_list',
          query: {
            confirmLeaderId:this.$route.query.confirmLeaderId,
            leaderName:this.$route.query.leaderName,
            sendDate:this.$route.query.sendDate,              
            requireFinishDate:this.$route.query.requireFinishDate,        
          }
        });
      },
      showText(key1,key2,span){
        if (this.$refs[key1].style.display == "none"){
          this.$refs[key1].style.display = "block";
          this.$refs[key2].innerHTML = "";
          this.$refs[span].innerHTML = "点击收起全部";
        }
        else {
          this.$refs[key1].style.display = "none";
          this.$refs[key2].innerHTML = "..................";
          this.$refs[span].innerHTML = "点击查看全部";
        }
      },
      showInstance(textValue){
        Utils.layerBox.layerDialogOpen({
          title:'案例说明',
          area:['600px','300px'],
          btn:['保存','取消'],
          content: `<div style="padding:10px 20px;margin:0 auto;">
          <textarea type="text" style="width:100%;height:170px;">
              `+(textValue||"")+`
          </textarea></div>`});
      },
      initPage() {
        this.pageResize();
        const {
            companyGroupId,
            year,
            month,
            positionGradeId
        } = this.$route.query;
        this.companyGroupId = companyGroupId;
        this.year = year;
        this.month = month;
        this.positionGradeId = positionGradeId;
      },
      pageResize() {
          this.body_height = $(window).height();
      },
    },
    
    created() {
        this.findObject();
        this._findOneByAblityId();
    }
};
</script>

<style>
    div.divItem{width:280px;float:left;border:1px solid white;}
    div.fss{color:blue;font-size:14px;}
    span.spanItem{color:blue;cursor:pointer;}
</style>