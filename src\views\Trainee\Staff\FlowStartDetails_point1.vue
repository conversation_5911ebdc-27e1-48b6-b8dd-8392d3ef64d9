<template>
  <div class="grade-evaluation-dialog-container">
      <div class="row">
        <div style="font-size:20px;font-weight:bold;">{{this.month==6?(this.year-1):this.year}}-{{this.month==6?this.year:(this.year+1)}}{{this.month==12?"半":""}}年度{{this.companyGroupName}}管培生综合能力评定操作表</div>
        <div class="col-xs-12">
           <div style="float:left;margin-right:30px;margin-top:10px;text-align:left;">
              <span style="margin-left:30px;font-weight:bold;">评定对象：</span>{{tableData.userName}}
              <span style="margin-left:30px;font-weight:bold;">部门：</span>{{tableData.deptName}}
              <span style="margin-left:30px;font-weight:bold;">职务：</span>{{tableData.jobName}}
              <span style="margin-left:30px;font-weight:bold;">职等：</span>{{tableData.gradeName}}<br>
              <div style="color:red;">
                  <span style="margin-left:30px;font-weight:bold;">评定提醒：</span>若是选择优、A、D级，必须填写案例说明，否则不能保存。  
              </div>
           </div>

           <div style="float:right;margin-right:30px;margin-top:10px;margin-bottom:10px;">
             <button type="button" class="btn btn-primary btn-xs"  @click="showDescrDialog()">评定说明</button>
             <button type="button" class="btn btn-primary btn-xs" :disabled="true" >保存</button>
             <button type="button" class="btn btn-primary btn-xs" @click="backPage()">返回上级</button>
           </div>
          <table class="table table-bordered">
            <thead>
              <tr>
                <th rowspan="2" style="width:40px;">序号</th>
                <th rowspan="2" colspan="2" >评定项目</th>
                <th rowspan="2" >评分标准（B级标准）</th>
                <th colspan="5">各项要素评定</th>
                <th rowspan="2" style="width:60px;">评定<br>得分</th>
                <th rowspan="2" style="width:80px;">案例说明</th>
              </tr>
              <tr>
                <th style="width:90px;">优<br>（85分以上）</th>
                <th style="width:80px;">A<br>（80-84分）</th>
                <th style="width:80px;">B<br>（75-79分）</th>
                <th style="width:80px;">C<br>（70-74分）</th>
                <th style="width:90px;">D<br>（70分以下）</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td colspan="11" style="text-align:left;font-weight:bold;">九大要素评琮</td>
              </tr>
              <tr>         
                <td>1</td>
                <td rowspan="2" style="width:70px;">注重本质 </td>
                <td style="width:100px;">思维与行动</td>
                <td style="text-align:left;">
                  1、能从多元的、纷繁杂乱的源头理清信息。<br>
                  <div ref="div_01" style="display:none;" >
                    2、能从不同的信息中找到重要的关联性。<br>
                    3、能从梳理的信息中发现关键的问题且提出解决方案。<br>
                    4、能发现并着手解决正在产生的问题。<br>
                    5、基于经验与专业知识，权衡好信息、个人判断及直觉之间的关系，并作出相应的决定。
                  </div>                 
                  <div ><div ref="div_11" class="divItem">..................</div> 
                       <span ref="span_1" class="spanItem" @click="showText('div_01','div_11','span_1')">点击查看全部</span>
                  </div>                 
                </td>                
                <td>                  
                  <input name="item1" v-if="ablityPoint.mindAction>=85 && ablityPoint.mindAction<=100" id='Radio4' type='radio' checked='checked'  @click="showPoint('select_1',85)"/>
                  <input name="item1" v-else id='Radio4' type='radio' @click="showPoint('select_1',85)"/>
                </td>
                <td>
                  <input name="item1" v-if="ablityPoint.mindAction>=80 && ablityPoint.mindAction<85" id='Radio3' type='radio' checked='checked'  @click="showPoint('select_1',80)"/>
                  <input name="item1" v-else id='Radio3' type='radio' @click="showPoint('select_1',80)"/>
                </td>
                <td>
                  <input name="item1" v-if="ablityPoint.mindAction>=75 && ablityPoint.mindAction<80" id='Radio2' type='radio' checked='checked'  @click="showPoint('select_1',75)"/>
                  <input name="item1" v-else id='Radio2' type='radio' @click="showPoint('select_1',75)"/>
                </td>
                <td>
                  <input name="item1" v-if="ablityPoint.mindAction>=70 && ablityPoint.mindAction<75" id='Radio1' type='radio' checked='checked'  @click="showPoint('select_1',70)"/>
                  <input name="item1" v-else id='Radio1' type='radio' @click="showPoint('select_1',70)"/>
                </td>
                <td>
                  <input name="item1" v-if="ablityPoint.mindAction<70" id='Radio0' type='radio' checked='checked' />
                  <input name="item1" v-else id='Radio0' type='radio' @click="showPoint('select_1',60)"/>
                </td>
                <td>
                  <select ref="select_1" style="width:95%;height:24px;">
                  </select>
                </td>
                <td>
                  <a href="#" @click="showInstance(ablityPoint.textAction)">修改</a>
                </td>
              </tr>
              <tr>         
                <td>2</td>
                <td>洞察力</td>
                <td style="text-align:left;">
                  1、善于学习并有效运用。<br>
                  <div ref="div_02" style="display:none;" >
                    2、寻找并引入外部的成果和见解。<br>
                    3、及时了解外部的信息、问题并紧跟趋势。<br>
                    4、主动了解同事和伙伴所关注的问题和观点。<br>
                    5、能认识到自己的优势与不足。<br>
                  </div>                 
                  <div ><div ref="div_12" class="divItem">..................</div> 
                       <span ref="span_2" class="spanItem" @click="showText('div_02','div_12','span_2')">点击查看全部</span>
                  </div>                   
                </td>                
                <td>                  
                  <input name="item2" v-if="ablityPoint.sharpEye>=85 && ablityPoint.sharpEye<=100" id='Radio4' type='radio' checked='checked'  @click="showPoint('select_2',85)"/>
                  <input name="item2" v-else id='Radio4' type='radio' @click="showPoint('select_2',85)"/>
                </td>
                <td>
                  <input name="item2" v-if="ablityPoint.sharpEye>=80 && ablityPoint.sharpEye<85" id='Radio3' type='radio' checked='checked'  @click="showPoint('select_2',80)"/>
                  <input name="item2" v-else id='Radio3' type='radio' @click="showPoint('select_2',80)"/>
                </td>
                <td>
                  <input name="item2" v-if="ablityPoint.sharpEye>=75 && ablityPoint.sharpEye<80" id='Radio2' type='radio' checked='checked'  @click="showPoint('select_2',75)"/>
                  <input name="item2" v-else id='Radio2' type='radio' @click="showPoint('select_2',75)"/>
                </td>
                <td>
                  <input name="item2" v-if="ablityPoint.sharpEye>=70 && ablityPoint.sharpEye<75" id='Radio1' type='radio' checked='checked'  @click="showPoint('select_2',70)"/>
                  <input name="item2" v-else id='Radio1' type='radio' @click="showPoint('select_2',70)"/>
                </td>
                <td>
                  <input name="item2" v-if="ablityPoint.sharpEye<70" id='Radio0' type='radio' checked='checked' />
                  <input name="item2" v-else id='Radio0' type='radio' @click="showPoint('select_2',60)"/>
                </td>
                <td>                  
                  <select ref="select_2" style="width:95%;height:24px;">
                  </select>
                </td>
                <td>
                  <a href="#" @click="showInstance(ablityPoint.textEye)">修改</a>
                </td>
              </tr>
              <tr>         
                <td>3</td>
                <td rowspan="3">锐意进取</td>
                <td>专长与应用</td>
                <td style="text-align:left;">
                  1、通过利用自己或他人的特点和专长，在团队中解决问题。<br>
                  <div ref="div_03" style="display:none;" >
                    2、有特殊的技能、知识和实际经验，并能运用其解决实际问题。<br>
                    3、目前拥有某些领域的专业特长，并有不断更新与改进的意识。<br>
                  </div>                 
                  <div ><div ref="div_13" class="divItem">..................</div> 
                       <span ref="span_3" class="spanItem" @click="showText('div_03','div_13','span_3')">点击查看全部</span>
                  </div>                 
                </td>                
                <td>                  
                  <input name="item3" v-if="ablityPoint.mindSkill>=85 && ablityPoint.mindSkill<=100" id='Radio4' type='radio' checked='checked'  @click="showPoint('select_3',85)"/>
                  <input name="item3" v-else id='Radio4' type='radio' @click="showPoint('select_3',85)"/>
                </td>
                <td>
                  <input name="item3" v-if="ablityPoint.mindSkill>=80 && ablityPoint.mindSkill<85" id='Radio3' type='radio' checked='checked'  @click="showPoint('select_3',80)"/>
                  <input name="item3" v-else id='Radio3' type='radio' @click="showPoint('select_3',80)"/>
                </td>
                <td>
                  <input name="item3" v-if="ablityPoint.mindSkill>=75 && ablityPoint.mindSkill<80" id='Radio2' type='radio' checked='checked'  @click="showPoint('select_3',75)"/>
                  <input name="item3" v-else id='Radio2' type='radio' @click="showPoint('select_3',75)"/>
                </td>
                <td>
                  <input name="item3" v-if="ablityPoint.mindSkill>=70 && ablityPoint.mindSkill<75" id='Radio1' type='radio' checked='checked'  @click="showPoint('select_3',70)"/>
                  <input name="item3" v-else id='Radio1' type='radio' @click="showPoint('select_3',70)"/>
                </td>
                <td>
                  <input name="item3" v-if="ablityPoint.mindSkill<70" id='Radio0' type='radio' checked='checked' />
                  <input name="item3" v-else id='Radio0' type='radio' @click="showPoint('select_3',60)"/>
                </td>
                <td>
                  <select ref="select_3" style="width:95%;height:24px;">
                  </select>
                </td>
                <td>
                  <a href="#" @click="showInstance(ablityPoint.textSkill)">修改</a>
                </td>
              </tr>
              <tr>         
                <td>4</td>
                <td>提高能力</td>
                <td style="text-align:left;">
                  1、能从成功和失败中吸取经验和教训。<br>
                  <div ref="div_04" style="display:none;" >
                    2、寻找学习和自我提升的机会。<br>
                    3、能在短期内提升工作所需的技能和增加知识储备。<br>
                    4、能不断强化自己或团队现有的能力，保持现有的成绩。<br>
                    5、能与他人分享知识、技能和经验；但对此做法的重要性认识不足。<br>
                    6、为满足当前及未来需求，不断完善和优化原来的管理制度和管理流程。<br>
                  </div>                 
                  <div ><div ref="div_14" class="divItem">..................</div> 
                       <span ref="span_4" class="spanItem" @click="showText('div_04','div_14','span_4')">点击查看全部</span>
                  </div>                
                </td>                
                <td>                  
                  <input name="item4" v-if="ablityPoint.groupImprove>=85 && ablityPoint.groupImprove<=100" id='Radio4' type='radio' checked='checked' @click="showPoint('select_4',85)" />
                  <input name="item4" v-else id='Radio4' type='radio' @click="showPoint('select_4',85)"/>
                </td>
                <td>
                  <input name="item4" v-if="ablityPoint.groupImprove>=80 && ablityPoint.groupImprove<85" id='Radio3' type='radio' checked='checked' @click="showPoint('select_4',80)" />
                  <input name="item4" v-else id='Radio3' type='radio' @click="showPoint('select_4',80)"/>
                </td>
                <td>
                  <input name="item4" v-if="ablityPoint.groupImprove>=75 && ablityPoint.groupImprove<80" id='Radio2' type='radio' checked='checked' @click="showPoint('select_4',75)" />
                  <input name="item4" v-else id='Radio2' type='radio' @click="showPoint('select_4',75)"/>
                </td>
                <td>
                  <input name="item4" v-if="ablityPoint.groupImprove>=70 && ablityPoint.groupImprove<75" id='Radio1' type='radio' checked='checked' @click="showPoint('select_4',70)" />
                  <input name="item4" v-else id='Radio1' type='radio' @click="showPoint('select_4',70)"/>
                </td>
                <td>
                  <input name="item4" v-if="ablityPoint.groupImprove<70" id='Radio0' type='radio' checked='checked' @click="showPoint('select_4',60)" />
                  <input name="item4" v-else id='Radio0' type='radio' @click="showPoint('select_4',60)"/>
                </td>
                <td>
                  <select ref="select_4" style="width:95%;height:24px;">
                  </select>
                </td>
                <td>
                  <a href="#" @click="showInstance(ablityPoint.textImprove)">修改</a>
                </td>
              </tr>
              <tr>         
                <td>5</td>
                <td>纪律性</td>
                <td style="text-align:left;">
                  1、以结果为导向，保持行动与目的的一致性。<br>
                  <div ref="div_05" style="display:none;" >
                    2、对工作认真负责。<br>
                    3、能够在条件有限的情况下开展工作。<br>
                    4、能够按计划完成工作任务，保证既定结果。<br>
                    5、经常跟踪工作进度，确保达到目标。<br>
                  </div>                 
                  <div ><div ref="div_15" class="divItem">..................</div> 
                       <span ref="span_5" class="spanItem" @click="showText('div_05','div_15','span_5')">点击查看全部</span>
                  </div>
                </td>                
                <td>                  
                  <input name="item5" v-if="ablityPoint.sharpDiscipline>=85 && ablityPoint.sharpDiscipline<=100" id='Radio4' type='radio' checked='checked' @click="showPoint('select_5',85)" />
                  <input name="item5" v-else id='Radio4' type='radio' @click="showPoint('select_5',85)"/>
                </td>
                <td>
                  <input name="item5" v-if="ablityPoint.sharpDiscipline>=80 && ablityPoint.sharpDiscipline<85" id='Radio3' type='radio' checked='checked' @click="showPoint('select_5',80)" />
                  <input name="item5" v-else id='Radio3' type='radio' @click="showPoint('select_5',80)"/>
                </td>
                <td>
                  <input name="item5" v-if="ablityPoint.sharpDiscipline>=75 && ablityPoint.sharpDiscipline<80" id='Radio2' type='radio' checked='checked' @click="showPoint('select_5',75)" />
                  <input name="item5" v-else id='Radio2' type='radio' @click="showPoint('select_5',75)"/>
                </td>
                <td>
                  <input name="item5" v-if="ablityPoint.sharpDiscipline>=70 && ablityPoint.sharpDiscipline<75" id='Radio1' type='radio' checked='checked' @click="showPoint('select_5',70)" />
                  <input name="item5" v-else id='Radio1' type='radio' @click="showPoint('select_5',70)"/>
                </td>
                <td>
                  <input name="item5" v-if="ablityPoint.sharpDiscipline<70" id='Radio0' type='radio' checked='checked' />
                  <input name="item5" v-else id='Radio0' type='radio' @click="showPoint('select_5',60)"/>
                </td>
                <td>
                  <select ref="select_5" style="width:95%;height:24px;">
                  </select>
                </td>
                <td>
                  <a href="#" @click="showInstance(ablityPoint.textDiscipline)">修改</a>
                </td>
              </tr>
              <tr>         
                <td>6</td>
                <td rowspan="4">开拓创新</td>
                <td>领导力</td>
                <td style="text-align:left;">
                  1、善于发现机会、抓住机会并形成可实现的目标。<br>
                  <div ref="div_06" style="display:none;" >
                    2、能够确立方向和目标，并且取得大家的认可。<br>
                    3、善于营造团队成员积极向上，锐意进取的氛围。<br>
                    4、在团队中有话语权。<br>
                  </div>                 
                  <div ><div ref="div_16" class="divItem">..................</div> 
                       <span ref="span_6" class="spanItem" @click="showText('div_06','div_16','span_6')">点击查看全部</span>
                  </div>                
                </td>                
                <td>                  
                  <input name="item6" v-if="ablityPoint.groupLeader>=85 && ablityPoint.groupLeader<=100" id='Radio4' type='radio' checked='checked' @click="showPoint('select_6',85)"/>
                  <input name="item6" v-else id='Radio4' type='radio' @click="showPoint('select_6',85)"/>
                </td>
                <td>
                  <input name="item6" v-if="ablityPoint.groupLeader>=80 && ablityPoint.groupLeader<85" id='Radio3' type='radio' checked='checked' @click="showPoint('select_6',80)" />
                  <input name="item6" v-else id='Radio3' type='radio' @click="showPoint('select_6',80)"/>
                </td>
                <td>
                  <input name="item6" v-if="ablityPoint.groupLeader>=75 && ablityPoint.groupLeader<80" id='Radio2' type='radio' checked='checked' @click="showPoint('select_6',75)" />
                  <input name="item6" v-else id='Radio2' type='radio' @click="showPoint('select_6',75)"/>
                </td>
                <td>
                  <input name="item6" v-if="ablityPoint.groupLeader>=70 && ablityPoint.groupLeader<75" id='Radio1' type='radio' checked='checked' @click="showPoint('select_6',70)" />
                  <input name="item6" v-else id='Radio1' type='radio' @click="showPoint('select_6',70)"/>
                </td>
                <td>
                  <input name="item6" v-if="ablityPoint.groupLeader<70" id='Radio0' type='radio' checked='checked' />
                  <input name="item6" v-else id='Radio0' type='radio' @click="showPoint('select_6',60)"/>
                </td>
                <td>
                  <select ref="select_6" style="width:95%;height:24px;">
                  </select>
                </td>
                <td>
                  <a href="#" @click="showInstance(ablityPoint.textLeader)">修改</a>
                </td>
              </tr>
              <tr>         
                <td>7</td>
                <td>创新与借签</td>
                <td style="text-align:left;">
                  1、通过分析不同渠道的信息，总结出新的想法并转化为可行的解决方案。<br>
                  <div ref="div_07" style="display:none;" >
                    2、敢于突破既有思路，采用新的更好的方法解决问题。<br>
                    3、营造激发他人表达自己想法的氛围。<br>
                    4、善于借鉴成功经验解决问题。<br>
                    5、想象力丰富并且善于对想象的进行分析与证实。<br>
                  </div>                 
                  <div ><div ref="div_17" class="divItem">..................</div> 
                       <span ref="span_7" class="spanItem" @click="showText('div_07','div_17','span_7')">点击查看全部</span>
                  </div>
                </td>                
                <td>                  
                  <input name="item7" v-if="ablityPoint.mindNew>=85 && ablityPoint.mindNew<=100" id='Radio4' type='radio' checked='checked'  @click="showPoint('select_7',85)" />
                  <input name="item7" v-else id='Radio4' type='radio' @click="showPoint('select_7',85)" />
                </td>
                <td>
                  <input name="item7" v-if="ablityPoint.mindNew>=80 && ablityPoint.mindNew<85" id='Radio3' type='radio' checked='checked'  @click="showPoint('select_7',80)" />
                  <input name="item7" v-else id='Radio3' type='radio' @click="showPoint('select_7',80)" />
                </td>
                <td>
                  <input name="item7" v-if="ablityPoint.mindNew>=75 && ablityPoint.mindNew<80" id='Radio2' type='radio' checked='checked'  @click="showPoint('select_7',75)" />
                  <input name="item7" v-else id='Radio2' type='radio' @click="showPoint('select_7',75)" />
                </td>
                <td>
                  <input name="item7" v-if="ablityPoint.mindNew>=70 && ablityPoint.mindNew<75" id='Radio1' type='radio' checked='checked'  @click="showPoint('select_7',70)" />
                  <input name="item7" v-else id='Radio1' type='radio' @click="showPoint('select_7',70)" />
                </td>
                <td>
                  <input name="item7" v-if="ablityPoint.mindNew<70" id='Radio0' type='radio' checked='checked' @click="showPoint('select_7',60)" />
                  <input name="item7" v-else id='Radio0' type='radio' @click="showPoint('select_7',60)"/>
                </td>
                <td>
                  <select ref="select_7" style="width:95%;height:24px;">
                  </select>
                </td>
                <td>
                  <a href="#" @click="showInstance(ablityPoint.textNew)">修改</a>
                </td>
              </tr>
              <tr>         
                <td>8</td>
                <td>拥抱变化</td>
                <td style="text-align:left;">
                  1、寻求并接受新思想、新办法、新机会。<br>
                  <div ref="div_08" style="display:none;" >
                    2、能够认识到现实状况不足的地方。<br>
                    3、提出并主导合理的变化。<br>
                    4、有调整、适应及积极响应的能力。<br>
                    5、随机应变，灵活运用不同的经验，能迅速调整自己适应环境的变化。<br>
                  </div>                 
                  <div ><div ref="div_18" class="divItem">..................</div> 
                       <span ref="span_8" class="spanItem" @click="showText('div_08','div_18','span_8')">点击查看全部</span>
                  </div>
                </td>                
                <td>                  
                  <input name="item8" v-if="ablityPoint.sharpEmbrace>=85 && ablityPoint.sharpEmbrace<=100" id='Radio4' type='radio' checked='checked' @click="showPoint('select_8',85)" />
                  <input name="item8" v-else id='Radio4' type='radio' @click="showPoint('select_8',85)"/>
                </td>
                <td>
                  <input name="item8" v-if="ablityPoint.sharpEmbrace>=80 && ablityPoint.sharpEmbrace<85" id='Radio3' type='radio' checked='checked' @click="showPoint('select_8',80)" />
                  <input name="item8" v-else id='Radio3' type='radio' @click="showPoint('select_8',80)"/>
                </td>
                <td>
                  <input name="item8" v-if="ablityPoint.sharpEmbrace>=75 && ablityPoint.sharpEmbrace<80" id='Radio2' type='radio' checked='checked' @click="showPoint('select_8',75)" />
                  <input name="item8" v-else id='Radio2' type='radio' @click="showPoint('select_8',75)"/>
                </td>
                <td>
                  <input name="item8" v-if="ablityPoint.sharpEmbrace>=70 && ablityPoint.sharpEmbrace<75" id='Radio1' type='radio' checked='checked' @click="showPoint('select_8',70)" />
                  <input name="item8" v-else id='Radio1' type='radio' @click="showPoint('select_8',70)"/>
                </td>
                <td>
                  <input name="item8" v-if="ablityPoint.sharpEmbrace<70" id='Radio0' type='radio' checked='checked' @click="showPoint('select_8',60)" />
                  <input name="item8" v-else id='Radio0' type='radio' @click="showPoint('select_8',60)"/>
                </td>
                <td>
                  <select ref="select_8" style="width:95%;height:24px;">
                  </select>
                </td>
                <td>
                  <a href="#" @click="showInstance(ablityPoint.textEmbrace)">修改</a>
                </td>
              </tr>
              <tr>         
                <td>9</td>
                <td>营造多样化合作</td>
                <td style="text-align:left;">
                  1、能够在原来的基础上维持和拓展多元化合作关系。<br>
                  <div ref="div_09" style="display:none;" >
                    2、认同并重视不同文化、不同背景的人提出的想法或观点。<br>
                    3、能够在组织和组织关系范围内有效开展合作。<br>
                    4、认可合作共赢的做法，有寻求对外合作的意识或经验。<br>
                    5、善于察觉他人的需求、感受并尊重他人。<br>
                  </div>                 
                  <div ><div ref="div_19" class="divItem">..................</div> 
                       <span ref="span_9" class="spanItem" @click="showText('div_09','div_19','span_9')">点击查看全部</span>
                  </div>
                </td>                
                <td>                  
                  <input name="item9" v-if="ablityPoint.groupCooperate>=85 && ablityPoint.groupCooperate<=100" id='Radio4' type='radio' checked='checked' @click="showPoint('select_9',85)"/>
                  <input name="item9" v-else id='Radio4' type='radio' @click="showPoint('select_9',85)"/>
                </td>
                <td>
                  <input name="item9" v-if="ablityPoint.groupCooperate>=80 && ablityPoint.groupCooperate<85" id='Radio3' type='radio' checked='checked' @click="showPoint('select_9',80)"/>
                  <input name="item9" v-else id='Radio3' type='radio' @click="showPoint('select_9',80)"/>
                </td>
                <td>
                  <input name="item9" v-if="ablityPoint.groupCooperate>=75 && ablityPoint.groupCooperate<80" id='Radio2' type='radio' checked='checked' @click="showPoint('select_9',75)" />
                  <input name="item9" v-else id='Radio2' type='radio' @click="showPoint('select_9',75)"/>
                </td>
                <td>
                  <input name="item9" v-if="ablityPoint.groupCooperate>=70 && ablityPoint.groupCooperate<75" id='Radio1' type='radio' checked='checked' @click="showPoint('select_9',70)" />
                  <input name="item9" v-else id='Radio1' type='radio' @click="showPoint('select_9',70)"/>
                </td>
                <td>
                  <input name="item9" v-if="ablityPoint.groupCooperate<70" id='Radio0' type='radio' checked='checked' />
                  <input name="item9" v-else id='Radio0' type='radio'  @click="showPoint('select_9',60)"/>
                </td>
                <td>
                  <select ref="select_9" style="width:95%;height:24px;">
                  </select>
                </td>
                <td>                  
                  <a href="#" @click="showInstance(ablityPoint.textCooperate)">修改</a>
                </td>
              </tr>
              <tr>
                <td colspan="4" rowspan="3">综合得分</td>
                <td colspan="5">
                  综合得分
                </td>
                <td colspan="5">
                  {{ablityPoint.avgPoint}}
                </td>
              </tr>
              <tr>
                <td colspan="5">
                  能力等级
                </td>
                <td colspan="5">
                  {{ablityPoint.ablityLevel}}
                </td>
              </tr>
              <tr>
                <td colspan="5">
                  考核结论
                </td>
                <td colspan="5">
                  {{ablityPoint.stayStatus}}
                </td>
              </tr>
              <tr>
                <td colspan="11" style="text-align:left;font-weight:bold;">积极态度及稳定性</td>
              </tr>
              <tr>
                <td>1</td>
                <td colspan="2">
                  积极向上、锐意进取的态度
                </td>
                <td style="text-align:left;">
                  1、品质分主要是以内在品质为评分依据，表象为辅。过于自信、盲目好强、固执等不属于积极向上、锐意进取的品质。<br>
                  <div ref="div_20" style="display:none;" >
                    2、名词解析：<br>
                    （1）积极向上：正面的、努力进取的。<br>
                    （2）锐意进取：意志坚决地追求上进，下决心强化自身，力图有所作为。<br>
                  </div>                 
                  <div ><div ref="div_220" class="divItem">..................</div> 
                       <span ref="span_20" class="spanItem" @click="showText('div_20','div_220','span_20')">点击查看全部</span>
                  </div>
                </td>
                <td colspan="2" style="text-align:left;">
                  <input type="text" ref="txt_energyPoint" style="width:98%;height:30px;" title="请录入分数（70分以上视为积极，满分100分）" maxlength="3" @change="editEnergyPoint(1)" />
                </td>
                <td colspan="3" style="text-align:left;">
                  <textarea ref="txt_energyReason" style="width:98%;height:50px;" title="请录入评价理由"></textarea>
                </td>
                <td colspan="2">
                  <div ref="div_energy"></div>
                </td>
              </tr>
              <tr>
                <td>2</td>
                <td colspan="2">
                  稳定性
                </td>
                <td style="text-align:left;">
                  1、婚恋情况：对象从事职业、行业以及工作地点与我司条件相符或者相近的；同时能接受异地恋，即使结婚后也能接受两地分居等，稳定性较好。<br>
                  <div ref="div_21" style="display:none;" >
                    2、工作安排认可度：工作热情较高， 能够积极完成领导安排的工作，不推脱，不抱怨等，稳定性较好。<br>
                    3、企业文化认可度：能够适应我司的日常管理制度，认可企业发展理念等，稳定性较好。<br>
                  </div>                 
                  <div ><div ref="div_221" class="divItem">..................</div> 
                       <span ref="span_21" class="spanItem" @click="showText('div_21','div_221','span_21')">点击查看全部</span>
                  </div>
                </td>
                <td colspan="2" style="text-align:left;">
                  <input type="text" ref="txt_stablityPoint" style="width:98%;height:30px;" title="请录入分数（70分以上视为稳定，满分100分）" maxlength="3" @change="editEnergyPoint(2)" />
                </td>
                <td colspan="3" style="text-align:left;">
                  <textarea ref="txt_stablityReason" style="width:98%;height:50px;" title="请录入评价理由"></textarea>
                </td>
                <td colspan="2">
                  <div ref="div_stablity"></div>
                </td>
              </tr>
            </tbody>
          </table>
          
        </div>
      </div>
  </div>
</template>


<script>
import {findOne,findOneByAblityId } from "@/api";
export default {
data() {
    return {
        body_height: 0,
        year: "",
        month: "", 
        companyGroupId: "",
        companyGroupName:"",
        tableData: "",
        ablityPoint: "",
        avgPoint:"",
    };
},  

  methods: {
      findObject() {
        findOne({
          fdId:this.$route.query.ablityId
        }).then(res => {
          if (!res.success) return;
          this.tableData = res.data ? res.data : [];
        }).catch(err=>{
          layer.msg(`${err.msg}`, {icon: 2,shade:0.3,shadeClose:true});
        });        
      },
      showPoint(key,point){
        let html="",value=0,maxId=4;
        if (point>=85 && point<=100){
          value=85;
          maxId=15;         
        }
        else if (point>=80 && point<=84){
          value=80;
        }
        else if (point>=75 && point<=79){
          value=75;
        }
        else if (point>=70 && point<=75){
          value=70;
        }
        else if (point<=69){
          value=0;
          maxId=69;    
        }
        while (maxId>=0){
            if ((value+maxId)==point){
              html=html+"<option value='"+(value+maxId)+"' selected>"+(value+maxId)+"</option>";
            }
            else {
              html=html+"<option value='"+(value+maxId)+"'>"+(value+maxId)+"</option>";       
            }                               
            maxId=maxId-1;
        }
        this.$refs[key].innerHTML =html;
      },
      editEnergyPoint(type){
        if (type==1){
          let point=this.$refs["txt_energyPoint"].value;
          if (point==""){
            this.$refs["div_energy"].innerHTML="";
          }
          else if (point>=70 && point<=100){
            this.$refs["div_energy"].innerHTML="积极";
          }
          else {
            this.$refs["div_energy"].innerHTML="不积极";
          }   
        }
        else if (type==2){
          let point=this.$refs["txt_stablityPoint"].value;
          if (point==""){
            this.$refs["div_stablity"].innerHTML="";
          }
          else if (point>=70 && point<=100){
            this.$refs["div_stablity"].innerHTML="稳定";
          }
          else {
            this.$refs["div_stablity"].innerHTML="不稳定";
          }   
        }         
      },
      _findOneByAblityId(){
          findOneByAblityId({
            ablityId:this.$route.query.ablityId,
            leaderName:this.$route.query.leaderName
          }).then(res => {
            if (!res.success) return;
              this.ablityPoint = res.data ? res.data : [];
              this.year = this.ablityPoint.fdYear;
              this.month = this.ablityPoint.fdMonth;
              this.companyGroupName=this.ablityPoint.companyGroupName;
              this.avgPoint =this.ablityPoint.avgPoint; 
              
              if (this.ablityPoint.totalPoint!=null){
                this.showPoint('select_1',this.ablityPoint.mindAction);
                this.showPoint('select_2',this.ablityPoint.sharpEye);
                this.showPoint('select_3',this.ablityPoint.mindSkill);
                this.showPoint('select_4',this.ablityPoint.groupImprove);
                this.showPoint('select_5',this.ablityPoint.sharpDiscipline);
                this.showPoint('select_6',this.ablityPoint.groupLeader);
                this.showPoint('select_7',this.ablityPoint.mindNew);
                this.showPoint('select_8',this.ablityPoint.sharpEmbrace);
                this.showPoint('select_9',this.ablityPoint.groupCooperate);
                
                this.$refs["txt_energyPoint"].value =this.ablityPoint.energyPoint;
                this.$refs["txt_energyReason"].value =this.ablityPoint.energyReason;
                this.editEnergyPoint(1);
                this.$refs["txt_stablityPoint"].value =this.ablityPoint.stablityPoint;
                this.$refs["txt_stablityReason"].value =this.ablityPoint.stablityReason;
                this.editEnergyPoint(2);
              }             

          }).catch(err=>{
            layer.msg(`${err.msg}`, {icon: 2,shade:0.3,shadeClose:true});
          });   
      },      
      backPage(){
        const {
            companyGroupId,
            year,
            month,
            positionGradeId
        } = this.$route.params;
        this.companyGroupId = companyGroupId;
        this.year = year;
        this.month = month;
        this.$router.push({
          path: '/'+this.year+'/'+this.month+'/Trainee/Staff/'+this.companyGroupId+'/FlowStartDetails_list',
          query: {
            confirmLeaderId:this.$route.query.confirmLeaderId,
            leaderName:this.$route.query.leaderName,
            sendDate:this.$route.query.sendDate,              
            requireFinishDate:this.$route.query.requireFinishDate,        
          }
        });
      },
      showInstance(textValue){
        Utils.layerBox.layerDialogOpen({
          title:'案例说明',
          area:['600px','300px'],
          btn:['保存','取消'],
          content: `<div style="padding:10px 20px;margin:0 auto;">
          <textarea type="text" style="width:100%;height:170px;">
              `+(textValue||"")+`
          </textarea></div>`});
      },
      showDescrDialog(){
          Utils.layerBox.layerDialogOpen({
          title:'评定说明',
          area:['600px','350px'],
          btn:['关闭'],
          content: `<div style="padding:10px 20px;margin:0 auto;">
          <p style="line-height:24px;">
              1、能力等级说明：能力等级为优、A、B、C级视为合格，D级视为不合格<br/>
              （1）优级：提资时间按照A级（6个月提资一次）执行，同时可考虑提前晋升；<br/>
              （2）A级：6个月提资一次；<br/>
              （3）B级：9个月提资一次；<br/>
              （4）C级：12个月提资一次；<br/>
              （5）D级：原则上劝退；<br/>
              最终能力等级需合议确定。<br/>
              2、积极态度：若该项目合格结果为不积极，则一票否决，原则上劝退。<br/>
              3、稳定性：若该项目合格结果不稳定，则一票否决，原则上劝退。
          </p></div>`});
      },
      showText(key1,key2,span){
        if (this.$refs[key1].style.display == "none"){
          this.$refs[key1].style.display = "block";
          this.$refs[key2].innerHTML = "";
          this.$refs[span].innerHTML = "点击收起全部";
        }
        else {
          this.$refs[key1].style.display = "none";
          this.$refs[key2].innerHTML = "..................";
          this.$refs[span].innerHTML = "点击查看全部";
        }
      },
      initPage() {
        this.pageResize();
        const {
            companyGroupId,
            year,
            month,
            positionGradeId
        } = this.$route.query;
        this.companyGroupId = companyGroupId;
        this.year = year;
        this.month = month;
        this.positionGradeId = positionGradeId;
      },

      pageResize() {
          this.body_height = $(window).height();
      },
    },
    created() {
        this.findObject();
        this._findOneByAblityId();
    },       
};
</script>


<style>
    div.divItem{width:460px;float:left;border:1px solid white;}
    div.fss{color:blue;font-size:14px;}
    span.spanItem{color:blue;cursor:pointer;}
</style>