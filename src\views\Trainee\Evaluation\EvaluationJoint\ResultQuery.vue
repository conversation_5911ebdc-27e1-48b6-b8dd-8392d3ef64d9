<template>
    <div class="result-detail-container">
        <div class="row">
            <div class="col-xs-12 text-left">
                <h4 class="col-xs-5">{{ $route.name }}</h4>
                <div class="col-xs-7 text-right" style="line-height:40px;">
                    <label>查询时间段：</label>
                    <select @change="changeYear" v-model="selectedYear" class="form-control"
                        style="width:120px;display:inline-block;">
                        <!-- <option value="">请选择</option> -->
                        <option v-for="(item, index) in yearList" :value="item.fdYear" :key="index">{{ item.fdYear }}
                        </option>
                    </select>
                    <button class="btn btn-primary btn-xs" @click="showDescrDialog()">说明</button>
                    <button class="btn btn-primary btn-xs"
                        @click="$router.push(`/${$route.params.year}/${$route.params.month}/menu/2`)">返回目录</button>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12">
                <div class="list-table">
                    <div class="scroll-table" style="width:100%;height:100%;overflow-x:hidden;">
                        <table class="scroll-table-header table-bordered">
                            <colgroup>
                                <col width="30" />
                                <col width="55" />
                                <col width="50" />
                                <col width="50" />
                                <col width="55" />
                                <col width="70" />
                                <col width="100" />

                                <col width="50" />
                                <col width="50" />
                                <col width="50" />
                                <col width="120" />
                                <col width="50" />
                                <col width="50" />
                                <col width="50" />
                                <col width="120" />
                            </colgroup>
                            <thead>
                                <tr>
                                    <th rowspan="2">序号</th>
                                    <th colspan="6">评定对象</th>
                                    <th colspan="4">{{ resultYear1 }}</th>
                                    <th colspan="4">{{ resultYear2 }}</th>
                                </tr>
                                <tr>
                                    <th>姓名</th>
                                    <th>届别</th>
                                    <th>岗位级别</th>
                                    <th>学历</th>
                                    <th>部门</th>
                                    <th>职务</th>
                                    <th>年份</th>
                                    <th>能力等级</th>
                                    <th>留任结论</th>
                                    <th>备注</th>
                                    <th>年份</th>
                                    <th>能力等级</th>
                                    <th>留任结论</th>
                                    <th>备注</th>
                                </tr>
                            </thead>
                        </table>
                        <div class="scroll-table-body"
                            :style="{ overflow: 'auto', maxHeight: body_height - 170 + 'px', 'overflow-y': 'scroll', 'overflow-x': 'hidden' }">
                            <table
                                style="margin:0;position: relative;font-size:13px;float:left;border:none;"
                                class="table table-bordered table-hover table-striped">
                                <colgroup>
                                    <col width="30" />
                                    <col width="55" />
                                    <col width="50" />
                                    <col width="50" />
                                    <col width="55" />
                                    <col width="70" />
                                    <col width="100" />

                                    <col width="50" />
                                    <col width="50" />
                                    <col width="50" />
                                    <col width="120" />
                                    <col width="50" />
                                    <col width="50" />
                                    <col width="50" />
                                    <col width="120" />
                                </colgroup>
                                <tbody>
                                    <template v-for="(item, index) in tableData">
                                        <tr>
                                            <td style="text-align:left;font-size:14px;font-weight:bold;" colspan="15">
                                                {{ item.companyName }}
                                            </td>
                                        </tr>
                                        <tr v-for="(data, $index) in item.list" :key="$index + '-' + index">
                                            <td>{{ data.user_name == null ? null : ($index + 1) }}</td>
                                            <td>{{ data.user_name || '' }}</td>
                                            <td>{{ data.graduate_year == null ? "" : (data.graduate_year + "届") }}</td>
                                            <td>{{ data.school_level == 1 ? "一级" : (data.school_level == 2 ? "二级" : (data.school_level == 3 ? "三级" : "")) }}
                                            </td>
                                            <td>{{ data.degree || '' }}</td>
                                            <td>{{ data.dept_name || '' }}</td>
                                            <td>{{ data.job_name || '' }}</td>
                                            <td>{{ data.year_index1 }}</td>
                                            <td>{{ data.adjust_level1 || '' }}</td>
                                            <td>{{ data.adjust_result1 || '' }}</td>
                                            <td>{{ data.remark1 || '' }}</td>
                                            <td>{{ data.year_index2 }}</td>
                                            <td>{{ data.adjust_level2 || '' }}</td>
                                            <td>{{ data.adjust_result2 || '' }}</td>
                                            <td>{{ data.remark2 || '' }}</td>
                                        </tr>
                                    </template>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { findListByFdYear, findResultListByFdYear, findYearList } from '@/api'
export default {
    data() {
        return {
            tableData: [],
            companyGroupId: '1',
            schoolLevel: '',
            graduate_year: '',
            year: '',
            month: '',
            resultYear1: '',
            resultYear2: '',
            approveStatus: '',
            body_height: 0,
            yearList: [],
            selectedYear: "",
        }
    },
    computed: {
        disabledButton() {
            //   if(this.isLoading){
            //     return true;
            //   }
            //   else if(this.tableData&&this.tableData[0]&&this.tableData.length!=0){
            //     return this.tableData[0].list[0].studentStatus >= 11 ? true : false;
            //   }
            return false;
        }
    },
    methods: {
        _findResultListByFdYear() {
            const loading = this.$loading({
                lock: true,
                text: 'Loading',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            });
            /*先判断有权限才可以查询(modify by huangdh@2019-05-24)*/
            if (this.$store.state.doubleCol.arrAuths.point_ablity_findResultListByFdYear) {
                findResultListByFdYear({ fdYear: this.$route.params.year, fdMonth: this.$route.params.month }).then(res => {
                    this.tableData = res.data || [];
                    loading.close();
                })
            }
            else {
                layer.msg(`对不起，您没有权限查询本界面.`, { icon: 2, shade: 0.3, shadeClose: true });
            }
        },
        _findYearList() {
            // const loading = this.$loading({
            //     lock: true,
            //     text: 'Loading',
            //     spinner: 'el-icon-loading',
            //     background: 'rgba(0, 0, 0, 0.7)'
            // });
            findYearList().then(res => {
                this.yearList = res.data || [];
                if (this.yearList.length >= 1) {
                    this.selectedYear = this.yearList[0].fdYear;
                }
                // loading.close()
            })
        },

        showDescrDialog() {
            Utils.layerBox.layerDialogOpen({
                title: '<b>说明：</b>',
                area: ['600px', '200px'],
                btn: [],
                content: `<div style="padding:10px 20px 0 20px;margin:0 auto;">
            <p style="line-height:24px;">
                1、本表作用：作为专用的评定审批结果的查询界面，过程中的评定及合议数据属于保密，除有权限人员外不允许查询。<br/>
                2、本表显示：默认只显示最近一年（两次）的审批结果，若需要查询其他年度 的审批结果，可点击“查询时间段”键进行查询。<br/>
            </p></div>`});
        },
        changeYear() {
            const loading = this.$loading({
                lock: true,
                text: 'Loading',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            });
            let value = this.selectedYear;
            if (value == "请选择") {
                return;
            }
            let queryYear = value.replace('年', '');
            let queryMonth = "6";
            this.resultYear1 = (parseInt(queryYear) - 1) + "-" + queryYear + "年度";
            this.resultYear2 = queryYear + "-" + (parseInt(queryYear) + 1) + "半年度";
            if (queryYear == "2017") {
                queryMonth = "12";
            }
            if (this.$store.state.doubleCol.arrAuths.point_ablity_findResultListByFdYear) {
                findResultListByFdYear({ fdYear: queryYear, fdMonth: queryMonth }).then(res => {
                    this.tableData = res.data || [];
                    loading.close()
                })
            }
            else {
                layer.msg(`对不起，您没有权限查询本界面.`, { icon: 2, shade: 0.3, shadeClose: true });
            }
        },
        initPage() {
            this.body_height = $(window).height();
            const { year, month } = this.$route.params;
            this.year = year;
            this.month = month;
            this._findYearList();
            this._findResultListByFdYear();
            this.resultYear1 = (parseInt(year) - 1) + "-" + year + "年度";
            this.resultYear2 = year + "-" + (parseInt(year) + 1) + "半年度";
        }
    },
    watch: {
        "$route": function () {
            this.initPage();
        },
    },
    mounted() {
        this.initPage();
        $(window).resize(() => {
            this.body_height = $(window).height();
        })
    }
}
</script>

<style scoped>
/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}
.list-year-data {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
.scroll-table-body {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
</style>
