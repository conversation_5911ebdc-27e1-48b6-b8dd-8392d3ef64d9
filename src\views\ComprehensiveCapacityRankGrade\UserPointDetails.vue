<template>
  <div class="grade-evaluation-dialog-container">
    <div class="row">
      <div class="col-xs-12">
        <table class="table table-bordered">
          <thead>
            <tr>
              <th rowspan="2" style="width: 60px">姓名</th>
              <th rowspan="2" style="width: 90px">岗位</th>
              <th rowspan="2" style="width: 90px">职等</th>
              <th colspan="7">评定人及评分</th>
            </tr>
            <tr>
              <th style="width: 40px">序号</th>
              <th style="width: 80px">领导类型</th>
              <th style="width: 60px">姓名</th>
              <th style="width: 50px">评分</th>
              <th style="width: 60px">最高领导</th>
              <th style="width: 60px">平均分值</th>
              <th style="width: 60px">合议分值</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(data, $index) in tableData" :key="$index">
              <td v-if="$index === 0" :rowspan="tableData.length">
                {{ data.user_name }}
              </td>
              <td v-if="$index === 0" :rowspan="tableData.length">
                {{ data.job_name }}
              </td>
              <td v-if="$index === 0" :rowspan="tableData.length">
                {{ data.grade_name }}
              </td>
              <td >
                {{ $index+1 }}
              </td>
              <td >
                {{ data.relation }}
              </td>
              <td >
                {{ data.leader_name }}
              </td>
              <td >
                {{ data.point }}
              </td>
              <td v-if="$index === 0" :rowspan="tableData.length">
                {{ data.max_point }}
              </td>
              <td v-if="$index === 0" :rowspan="tableData.length">
                {{ data.avg_point }}
              </td>
              <td v-if="$index === 0" :rowspan="tableData.length">
                {{ data.meetingPoint2 }}
              </td>
              
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
import { findUserPointList } from "@/api";
export default {
  props: {
    params: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      body_height: 0,
      fdYear: "",
      fdMonth: "",
      tableData: "",
      // mergerArr: ""
    };
  },
  methods: {
    _findUserPointList() {
      findUserPointList({
        fdYear: this.params.fdYear,
        fdMonth: this.params.fdMonth,
        userId: this.params.userId,
      })
        .then((res) => {
          if (!res.success) return;
          let data = JSON.parse(JSON.stringify(res.data || []));
          if (data.length > 0) {            
            this.tableData = data
          } else {
            this.tableData = [];
          }
          // this.tableData = res.data ? res.data : [];
        })
        .catch((err) => {
          layer.msg(`${err.msg}`, { icon: 2, shade: 0.3, shadeClose: true });
        });
    },
  },
  created() {
    this._findUserPointList();
  },
};
</script>

<style lang="scss">
.grade-evaluation-dialog-container {
  padding: 20px 0;
  .row {
    margin: 0;
    padding-left: 0;
    padding-right: 0;
    box-sizing: border-box;
  }
  .input-sm {
    height: 28px;
    padding: 5px 10px 5px 5px;
  }
  div.scroll-table {
    width: 100%;
    height: 320px;
    display: inline-block;
    float: left;
    background: #f4fafb;
    table.scroll-table-header {
      width: 100%;
      font-size: 13px;
      // display:block;
      // padding-right:17px;
    }
    table.scroll-table-body {
    }
  }
  table thead {
    tr {
      th {
        text-align: center;
        border-bottom: none;
        padding: 2px 5px;
        vertical-align: middle;
        background: #b7d8dc;
        height: 32px;
        font-size: 12px;
        color: #333;
        & > * {
          background: #b7d8dc !important;
          outline: none;
        }
      }
    }
    &.hide-thead-th > tr > th {
      height: 0;
      padding: 0;
      background: transparent;
      border: none;
    }
  }
  .table tbody tr {
    td {
      padding: 2px 5px;
      vertical-align: middle;
      height: 27px;
      font-size: 12px;
      text-align: center;
      .btn {
        padding-top: 0;
        padding-bottom: 0;
      }
    }
    &:nth-child(odd) {
      background: #fff;
    }
    &:nth-child(even) {
      background: #f9f9f9;
    }
  }
}
</style>
