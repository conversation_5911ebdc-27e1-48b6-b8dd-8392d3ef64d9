<template>
  <div class="grade-evaluation-flow-query-container">
      <div class="row">
        <div class="col-xs-12">
            <div class="list-table" style="width:740px;margin:0 auto;">
                <div class="scroll-table" style="width:100%;height:100%;overflow-x:hidden;border-bottom:1px solid #a9a9a9;">
                    <table class="scroll-table-header table-bordered" :style="{width:740 - 1.4*12 +'px'}">
                        <colgroup>
                            <col style="width:50px">
                            <col style="width:80px">
                            <col style="width:70px">
                            <col style="width:80px">
                            <col style="width:70px">
                            <col style="width:80px">
                            <col style="width:70px">
                            <col style="width:80px">
                            <col style="width:50px">
                        </colgroup>
                        <thead>
                            <tr>
                                <th rowspan="2">序号</th>
                                <th colspan="2">直接领导职级评定</th>
                                <th colspan="2">审核人职级评定</th>
                                <th colspan="2">审定人职级评定</th>
                                <th rowspan="2">评定状态</th>
                                <th rowspan="2">职级评定表查询</th>
                            </tr>
                            <tr>
                                <th>姓名</th>
                                <th>评定状态</th>
                                <th>姓名</th>
                                <th>评定状态</th>
                                <th>姓名</th>
                                <th>评定状态</th>
                            </tr>
                        </thead>
                    </table>
                    <div class="scroll-table-body" :style="{overflow:'auto',maxHeight:500 - 165 + 'px','overflow-y':'scroll','overflow-x':'hidden'}">
                        <table style="margin:0px 10px 0 0;position: relative;width:100%;font-size:13px;float:left;border:none;" class="table table-bordered table-hover table-striped">
                            <colgroup>
                                <col style="width:50px">
                                <col style="width:80px">
                                <col style="width:70px">
                                <col style="width:80px">
                                <col style="width:70px">
                                <col style="width:80px">
                                <col style="width:70px">
                                <col style="width:80px">
                                <col style="width:50px">
                            </colgroup>
                            <tbody>
                                <tr v-for="(data,index) in tableData" :key="index">
                                    <td>{{index+1}}</td>
                                    <td>{{data.leader_name||''}}</td>
                                    <td style="font-size:20px">{{data.leader_name ? !!data.leader_result ? '√':'×' : ''}}</td>
                                    <td>{{data.checker_name||''}}</td>
                                    <td style="font-size:20px">{{data.lchecker_name ? !!data.lchecker_result ? '√':'×' : ''}}</td>
                                    <td>{{data.approve_name||''}}</td>
                                    <td style="font-size:20px">{{data.approve_name ? !!data.approve_result ? '√':'×' : ''}}</td>
                                    <td>{{data.status_name||''}}</td>
                                    <td><a @click="showDialog(data.ablity_direct_leader_id)">查看</a></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
      </div>
  </div>
</template>

<script>
import GradeEvaluationFlowQueryDetailDialog from './GradeEvaluationFlowQueryDetailDialog'
import { findCheckerPointByConfirmLeaderId } from '@/api'
export default {
  props: {
      params:{
          type:Object,
          required:true
      }
  },
  data(){
    return {
      tableData:[],
    }
  },
  methods:{
      showDialog(ablity_direct_leader_id) {
        let layerDialog = Utils.layerBox.layerDialogOpen({
            title: "管理人员综合能力职级评定流程节点明细查询",
            btn: [],
            maxmin: false, //开启最大化最小化按钮
            area: ["760px", "500px"],
            btn1: (index, layero) => {
            },
            btn2: (index, layero) => {
            }
        });
        this.dialogComponent = Utils.layerBox.layerLoadVueComponent({
            layer: layerDialog,
            component: GradeEvaluationFlowQueryDetailDialog,
            methods: {
            doConfirm: (params) => {
                layer.close(layerDialog);
            },
            doClose: () => {
                layer.close(layerDialog); //关闭对话框
            }
            },
            props: {
                ablityDirectLeaderId:ablity_direct_leader_id
            }
        });
    },
    _findCheckerPointByConfirmLeaderId(){
      findCheckerPointByConfirmLeaderId({confirmLeaderId:this.params.confirmLeaderId||''}).then(res=>{
        if(!res.success) return;
        this.tableData = res.data ? res.data : []; 
      }).catch(err=>{
        layer.msg(`${err.msg}`, {icon: 2,shade:0.3,shadeClose:true});
      })
    },
    initPage(){
      this._findCheckerPointByConfirmLeaderId();
    }
  },
  created(){
    this.initPage();
  }
}
</script>

<style lang="scss">
.grade-evaluation-flow-query-container {
  padding: 20px 0 0 0;
  .row {
    margin: 0;
    padding-left: 0;
    padding-right: 0;
    box-sizing: border-box;
  }
  .input-sm {
    height: 28px;
    padding: 5px 10px 5px 5px;
  }
  div.scroll-table {
    width: 100%;
    height: 320px;
    display: inline-block;
    float: left;
    background: #f4fafb;
    table.scroll-table-header {
      width: 100%;
      font-size: 13px;
      // display:block;
      // padding-right:17px;
    }
    table.scroll-table-body {
    }
  }
  table thead {
    tr {
      th {
        text-align: center;
        border-bottom: none;
        padding: 2px 5px;
        vertical-align: middle;
        background: #b7d8dc;
        height: 32px;
        font-size: 12px;
        color: #333;
        & > * {
          background: #b7d8dc !important;
          outline: none;
        }
      }
    }
    &.hide-thead-th > tr > th {
      height: 0;
      padding: 0;
      background: transparent;
      border: none;
    }
  }
  .table tbody tr {
    td {
      padding: 2px 5px;
      vertical-align: middle;
      height: 27px;
      font-size: 12px;
      text-align: center;
      .btn {
        padding-top: 0;
        padding-bottom: 0;
      }
    }
    &:nth-child(odd) {
      background: #fff;
    }
    &:nth-child(even) {
      background: #f9f9f9;
    }
  }
}
</style>

