<template>
  <div class="planning-detail-dialog-container">
      <div class="row">
        <div class="col-xs-12">
          <table class="table table-bordered">
            <thead>
              <tr>
                <th rowspan="2">序号</th>
                <th rowspan="2">职务等级</th>
                <th rowspan="2">年份</th>
                <th colspan="2">时间</th>
              </tr>
              <tr>
                <th>起</th>
                <th>止</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(data,$index) in tableData" :key="$index">
                <td>{{data.yearIndex||''}}</td>
                <td>{{data.positionGradeName||''}}</td>
                <td>{{data.yearName||''}}</td>
                <td>{{data.startYear||''}}年{{data.startMonth||''}}月</td>
                <td>{{data.endYear ? `${data.endYear}年${data.endMonth}月` : ''}}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
  </div>
</template>

<script>
import { findListByYearAndCategory } from "@/api";
export default {
  props: {
    params: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      tableData: [],
      graduateYear: "",
      schoolLevel: "",
      type: ""
    };
  },
  methods: {
    getTitle() {
      return Utils.getCompanyNameByGroupId(this.companyGroupId);
    },
    _findListByYearAndCategory() {
      findListByYearAndCategory({
        graduateYear: this.graduateYear,
        schoolLevel: this.schoolLevel,
        jobCategory: this.jobCategory
      }).then(res => {
        this.tableData = res.data || [];
      });
    },
    initPage() {
      const { graduateYear, schoolLevel,type } = this.params;
      this.graduateYear = graduateYear;
      this.type = type;
      this.schoolLevel = schoolLevel;
      this._findListByYearAndCategory();
    }
  },
  created() {
    this.initPage();
  }
};
</script>

<style lang="scss">
.planning-detail-dialog-container {
    width:600px;
    margin: 0 auto;
    padding: 10px 0 0 0;
    table thead tr th {
      text-align: center;
      border-bottom: none;
      padding: 2px 5px;
      vertical-align: middle;
      background: #b7d8dc;
      height: 32px;
      font-size: 12px;
      color: #333;
    }

    table thead tr th>* {
      background: #b7d8dc !important;
      outline: none;
    }

    table thead.hide-thead-th>tr>th {
      height: 0;
      padding: 0;
      background: transparent;
      border: none;
    }

    .table tbody tr td {
      padding: 2px 5px;
      vertical-align: middle !important;
      height: 27px;
      font-size: 12px;
      text-align: center;
    }
    .table > thead > tr > th, .table > tbody > tr > th, .table > tfoot > tr > th, .table > thead > tr > td, .table > tbody > tr > td, .table > tfoot > tr > td {
      vertical-align: middle !important;
    }
    .table-bordered > thead > tr > th, .table-bordered > thead > tr > td {
      border-bottom-width:1px !important;
    }
    .table tbody tr td .btn {
      padding-top: 0;
      padding-bottom: 0;
    }

    .table tbody tr:nth-child(odd) {
      background: #fff;
    }

    .table tbody tr:nth-child(even) {
      background: #f9f9f9;
    }
}
</style>
