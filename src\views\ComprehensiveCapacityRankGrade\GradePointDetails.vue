<template>
  <div class="grade-evaluation-dialog-container">
    <div class="row">
      <div class="col-xs-12">
        <table class="table table-bordered">
          <thead>
            <tr>
              <th rowspan="2" style="width: 30px">序号</th>
              <th rowspan="2" style="width: 90px">厂部</th>
              <th rowspan="2" style="width: 50px">姓名</th>
              <th rowspan="2" style="width: 30px">性别</th>
              <th rowspan="2" style="width: 30px">年龄</th>
              <th rowspan="2" style="width: 40px">学历</th>
              <th rowspan="2" style="width: 90px">岗位</th>
              <th colspan="2">现任职等及时长</th>
              <th colspan="2">直接领导</th>
              <th colspan="2">间接领导</th>
              <th colspan="2">关系领导1</th>
              <th colspan="2">关系领导2</th>
              <th colspan="2">评定分值</th>
            </tr>
            <tr>
              <th style="width: 60px">职等</th>
              <th style="width: 70px">时长</th>
              <th style="width: 50px">姓名</th>
              <th style="width: 40px">评分</th>
              <th style="width: 50px">姓名</th>
              <th style="width: 40px">评分</th>
              <th style="width: 50px">姓名</th>
              <th style="width: 40px">评分</th>
              <th style="width: 50px">姓名</th>
              <th style="width: 40px">评分</th>
              <th style="width: 40px">最高领导</th>
              <th style="width: 40px">平均分值</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(data, $index) in tableData" :key="$index">
              <td >
                {{ $index+1 }}
              </td>
              <td >
                {{ data.dept_name }}
              </td>
              <td >
                {{ data.user_name }}
              </td>
              <td >
                {{ data.gender }}
              </td>
              <td >
                {{ data.age }}
              </td>
              <td >
                {{ data.degree }}
              </td>
              <td>
                {{ data.job_name }}
              </td>
              <td >
                {{ data.grade_name }}
              </td>
              <td >
                {{Math.floor(data.work_long / 12) <= 0 ||
                    data.work_long == null
                    ? ""
                    : Math.floor(data.work_long / 12) + "年"
                }}{{data.work_long % 12 === 0
                    ? ""
                    : (data.work_long % 12) + "个月"
                }}
              </td>              
              <td >
                {{ data.relation_leader3 }}
              </td>
              <td >
                {{ data.point3 }}
              </td>
              <td >
                {{ data.relation_leader4 }}
              </td>
              <td >
                {{ data.point4}}
              </td>
              <td >
                {{ data.relation_leader1 }}
              </td>
              <td >
                {{ data.point1 }}
              </td>
              <td >
                {{ data.relation_leader2 }}
              </td>
              <td >
                {{ data.point2 }}
              </td>
              <td>
                {{ data.max_point }}
              </td>
              <td >
                {{ data.avg_point }}
              </td>              
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
import { findGradePointList } from "@/api";
export default {
  props: {
    params: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      body_height: 0,
      fdYear: "",
      fdMonth: "",
      tableData: "",
      // mergerArr: ""
    };
  },
  methods: {
    _findGradePointList() {
      findGradePointList({
        fdYear: this.params.fdYear,
        fdMonth: this.params.fdMonth,
        companyGroupId: this.params.companyGroupId,
        grade: this.params.grade,
      })
        .then((res) => {
          if (!res.success) return;
          let data = JSON.parse(JSON.stringify(res.data || []));
          if (data.length > 0) {            
            this.tableData = data
          } else {
            this.tableData = [];
          }
          // this.tableData = res.data ? res.data : [];
        })
        .catch((err) => {
          layer.msg(`${err.msg}`, { icon: 2, shade: 0.3, shadeClose: true });
        });
    },
  },
  created() {
    this._findGradePointList();
  },
};
</script>

<style lang="scss">
.grade-evaluation-dialog-container {
  padding: 20px 0;
  .row {
    margin: 0;
    padding-left: 0;
    padding-right: 0;
    box-sizing: border-box;
  }
  .input-sm {
    height: 28px;
    padding: 5px 10px 5px 5px;
  }
  div.scroll-table {
    width: 100%;
    height: 320px;
    display: inline-block;
    float: left;
    background: #f4fafb;
    table.scroll-table-header {
      width: 100%;
      font-size: 13px;
      // display:block;
      // padding-right:17px;
    }
    table.scroll-table-body {
    }
  }
  table thead {
    tr {
      th {
        text-align: center;
        border-bottom: none;
        padding: 2px 5px;
        vertical-align: middle;
        background: #b7d8dc;
        height: 32px;
        font-size: 12px;
        color: #333;
        & > * {
          background: #b7d8dc !important;
          outline: none;
        }
      }
    }
    &.hide-thead-th > tr > th {
      height: 0;
      padding: 0;
      background: transparent;
      border: none;
    }
  }
  .table tbody tr {
    td {
      padding: 2px 5px;
      vertical-align: middle;
      height: 27px;
      font-size: 12px;
      text-align: center;
      .btn {
        padding-top: 0;
        padding-bottom: 0;
      }
    }
    &:nth-child(odd) {
      background: #fff;
    }
    &:nth-child(even) {
      background: #f9f9f9;
    }
  }
}
</style>
