<template>
  <div class="evaluation-detail-container">
    <div class="row" style="max-width:1640px">
      <div class="col-xs-12 text-left">
        <h4 class="col-xs-6">【{{ getCompanyName() }}】{{ $route.name }}</h4>
        <div class="col-xs-6 text-right">
          <div class="buts">
            <button class="btn btn-primary btn-xs" @click="showDescrDialog">
              评定说明
            </button>
            <button
              type="button"
              class="btn btn-primary btn-xs"
              @click="sheetIt"
            >
              导出Excel
            </button>
            <el-upload
              :show-file-list="false"
              action=""
              :headers="headers"
              :before-upload="BeforeUpload"
              :http-request="Upload"
            >
              <template #trigger>
                <button type="button" class="btn btn-primary btn-xs">
                  导入模板
                </button>
              </template>
            </el-upload>
            <button
              class="btn btn-primary btn-xs"
              @click="_findAllStudentPointToCheckByCompanyId('1')"
            >
              {{ buttonQueryText }}
            </button>
            <button
              class="btn btn-primary btn-xs"
              @click="_updateStudentRisePositionSave"
              :disabled="disabledButton"
              v-if="
                $store.state.doubleCol.arrAuths
                  .point_ablity_updateStudentRisePositionSave
              "
            >
              保存录入
            </button>

            <button
              class="btn btn-primary btn-xs"
              @click="_updateStudentRisePosition"
              :disabled="disabledButton"
              v-if="
                $store.state.doubleCol.arrAuths
                  .point_ablity_updateStudentRisePosition
              "
            >
              合议完成确定
            </button>
            <button
              class="btn btn-primary btn-xs"
              @click="
                $router.push(
                  `/${$route.params.year}/${$route.params.month}/menu/2`
                )
              "
            >
              返回目录
            </button>
          </div>
        </div>
      </div>
    </div>
    <div class="row myWidth">
      <div class="col-xs-12" style="width: auto">
        <div id="table" class="list-table">
          <div class="scroll-table" style="height: 100%; overflow-x: hidden">
            <table class="scroll-table-header table-bordered">
              <colgroup>
                <col width="30" />
                <col width="45" />
                <col width="40" />
                <col width="40" />
                <col width="40" />
                <col width="85" />
                <col width="60" />
                <col width="60" />
                <col width="75" />
                <col width="45" />

                <col width="50" />
                <col width="40" />
                <col width="40" />
                <col width="40" />
                <col width="40" />
                <col width="45" />
                <col width="40" />
                <col width="40" />
                <col width="40" />
                <col width="40" />

                <col width="40" />
                <col width="55" />
                <col width="35" />
                <col width="55" />
                <col width="68" />
                <col width="54" />
                <col width="40" />
                <col width="54" />
                <col width="45" />
                <col width="40" />
                <col width="40" />
                <col width="45" />
                <col width="45" />
              </colgroup>
              <thead>
                <tr>
                  <th rowspan="2">序号</th>
                  <th colspan="9">评定对象</th>
                  <th colspan="7">评定明细</th>
                  <th rowspan="2">工作亮点</th>
                  <th colspan="3">本期评定</th>
                  <th colspan="6">本期合议</th>
                  <th colspan="4">综合评价</th>
                  <th rowspan="2">上期审批结果</th>
                  <th rowspan="2">是否提交审批</th>
                  <th rowspan="2">备注</th>
                </tr>
                <tr>
                  <th>姓名</th>
                  <th>届别</th>
                  <th>岗位级别</th>
                  <th>学历</th>
                  <th>学校</th>
                  <th>部门</th>
                  <th>岗位</th>
                  <th>职务等级</th>
                  <th>年份</th>

                  <th>评定人</th>
                  <th>评定方式</th>
                  <th>权重</th>
                  <th>评定得分</th>
                  <th>积极态度</th>
                  <th>稳定性</th>
                  <th>明细查看</th>

                  <th>权重得分</th>
                  <th>能力等级</th>
                  <th>积极态度</th>

                  <th>合议得分</th>
                  <th>能力等级</th>
                  <th>积极态度</th>
                  <th>稳定性</th>
                  <th>留任结论</th>
                  <th>综合评价</th>

                  <th>积极态度</th>
                  <th>稳定性</th>
                  <th>优点</th>
                  <th>不足</th>
                </tr>
              </thead>
            </table>
            <div
              class="scroll-table-body"
              :style="{
                overflow: 'auto',
                maxHeight: body_height - 160 + 'px',
                'overflow-y': 'scroll',
                'overflow-x': 'hidden',
              }"
            >
              <table
                style="
                  margin: 0px 10px 0 0;
                  position: relative;
                  font-size: 13px;
                  float: left;
                  border: none;
                "
                class="table table-bordered table-hover table-striped"
              >
                <colgroup>
                  <col width="30" />
                  <col width="45" />
                  <col width="40" />
                  <col width="40" />
                  <col width="40" />
                  <col width="85" />
                  <col width="60" />
                  <col width="60" />
                  <col width="75" />
                  <col width="45" />

                  <col width="50" />
                  <col width="40" />
                  <col width="40" />
                  <col width="40" />
                  <col width="40" />
                  <col width="45" />
                  <col width="40" />
                  <col width="40" />
                  <col width="40" />
                  <col width="40" />

                  <col width="40" />
                  <col width="55" />
                  <col width="35" />
                  <col width="55" />
                  <col width="68" />
                  <col width="54" />
                  <col width="40" />
                  <col width="54" />
                  <col width="45" />
                  <col width="40" />
                  <col width="40" />
                  <col width="45" />
                  <col width="45" />
                </colgroup>
                <tbody>
                  <tr v-for="(data, $index) in tableData" :key="$index">
                    <td
                      v-if="!!data.user_row_count"
                      :rowspan="data.user_row_count"
                    >
                      {{ data.row_index }}
                    </td>
                    <td
                      v-if="!!data.user_row_count"
                      :rowspan="data.user_row_count"
                    >
                      {{ data.user_name || "" }}
                    </td>
                    <td
                      v-if="!!data.user_row_count"
                      :rowspan="data.user_row_count"
                    >
                      {{ data.graduate_year }}
                    </td>
                    <td
                      v-if="!!data.user_row_count"
                      :rowspan="data.user_row_count"
                    >
                      {{ data.school_level_name || "" }}
                    </td>
                    <td
                      v-if="!!data.user_row_count"
                      :rowspan="data.user_row_count"
                    >
                      {{ data.degree || "" }}
                    </td>
                    <td
                      v-if="!!data.user_row_count"
                      :rowspan="data.user_row_count"
                    >
                      {{ data.school || "" }}
                    </td>
                    <td
                      v-if="!!data.user_row_count"
                      :rowspan="data.user_row_count"
                    >
                      {{ data.dept_name || "" }}
                    </td>

                    <td
                      v-if="!!data.user_row_count"
                      :rowspan="data.user_row_count"
                    >
                      {{ data.job_name || "" }}
                    </td>
                    <td
                      v-if="!!data.user_row_count"
                      :rowspan="data.user_row_count"
                    >
                      {{ data.grade_name || "" }}
                    </td>
                    <td
                      v-if="!!data.user_row_count"
                      :rowspan="data.user_row_count"
                    >
                      {{ data.year_index || "" }}
                    </td>

                    <td>{{ data.rater_name || "" }}</td>
                    <td>{{ data.judge_type || "" }}</td>
                    <td>
                      {{
                        data.weight_point ? data.weight_point * 100 + "%" : ""
                      }}
                    </td>
                    <td>{{ data.point || "" }}</td>
                    <td>{{ data.energy_point || "" }}</td>
                    <td>{{ data.stablity || "" }}</td>
                    <td
                      v-if="!!data.user_row_count"
                      :rowspan="data.user_row_count"
                    >
                      <a href="#" @click="viewPointDetails()"
                        ><img
                          src="../../../../assets/images/action_dtl.png"
                          alt=""
                      /></a>
                    </td>

                    <td
                      v-if="!!data.user_row_count"
                      :rowspan="data.user_row_count"
                    >
                      <span
                        @click="viewPointDetails2(data.fd_id, data.user_name)"
                      >
                        <img src="../../../../assets/images/action_dtl.png" />
                      </span>
                    </td>

                    <td
                      :style="{
                        color:
                          toFixed1(data.avg_Point) ==
                          toFixed1(data.meeting_point)
                            ? ''
                            : 'red',
                      }"
                      v-if="!!data.user_row_count"
                      :rowspan="data.user_row_count"
                    >
                      {{ toFixed1(data.avg_point) }}
                    </td>
                    <td
                      :style="{
                        color:
                          data.ablity_level == data.meeting_level ? '' : 'red',
                      }"
                      v-if="!!data.user_row_count"
                      :rowspan="data.user_row_count"
                    >
                      {{ data.ablity_level || "" }}
                    </td>
                    <td
                      :style="{
                        color:
                          toFixed1(data.avg_energy_point) ==
                          toFixed1(data.meeting_energy_point)
                            ? ''
                            : 'red',
                      }"
                      v-if="!!data.user_row_count"
                      :rowspan="data.user_row_count"
                    >
                      {{ toFixed2(data.avg_energy_point) }}
                    </td>

                    <td
                      v-if="!!data.user_row_count"
                      :rowspan="data.user_row_count"
                      style="position: relative"
                    >
                      <input
                        type="number"
                        step="1"
                        v-model="data.meeting_point"
                        class="form-control input-sm"
                        style="width: 100%"
                        @change="editMeetingPoint(data)"
                      />
                    </td>
                    <td
                      :style="{
                        color:
                          data.ablity_level == data.meeting_level ? '' : 'red',
                      }"
                      v-if="!!data.user_row_count"
                      :rowspan="data.user_row_count"
                    >
                      {{ data.meeting_level || "" }}
                    </td>
                    <td
                      v-if="!!data.user_row_count"
                      :rowspan="data.user_row_count"
                    >
                      <input
                        type="number"
                        step="1"
                        v-model="data.meeting_energy_point"
                        class="form-control input-sm"
                        style="width: 100%"
                      />
                    </td>

                    <td
                      v-if="!!data.user_row_count"
                      :rowspan="data.user_row_count"
                    >
                      <select
                        v-model="data.meeting_stablity"
                        style="width: 100%; height: 24px; line-height: 24px"
                      >
                        <option :value="''">请选择</option>
                        <option value="稳定">稳定</option>
                        <option value="不稳定">不稳定</option>
                      </select>
                    </td>
                    <td
                      v-if="!!data.user_row_count"
                      :rowspan="data.user_row_count"
                    >
                      <select
                        v-model="data.adjust_result"
                        style="width: 100%; height: 24px; line-height: 24px"
                      >
                        <option :value="''">请选择</option>
                        <option value="留任">留任</option>
                        <option value="劝退">劝退</option>
                        <option value="留职察看">留职察看</option>
                        <option value="转普通管理人员">转普通管理人员</option>
                      </select>
                    </td>
                    <td
                      v-if="!!data.user_row_count"
                      :rowspan="data.user_row_count"
                    >
                      <a
                        v-if="data.status == 30"
                        @click="addGoodsAbsence(data.fd_id)"
                        ><img
                          src="../../../../assets/images/action_dtl.png"
                          alt=""
                      /></a>
                      <a
                        v-if="data.status != 30"
                        @click="addGoodsAbsence(data.fd_id)"
                        >录入</a
                      >
                    </td>

                    <td
                      v-if="!!data.user_row_count"
                      :rowspan="data.user_row_count"
                      style="padding: 0"
                    >
                      <div class="truncate">{{ data.total_energy || "" }}</div>
                    </td>
                    <td
                      v-if="!!data.user_row_count"
                      :rowspan="data.user_row_count"
                      style="padding: 0"
                    >
                      <div class="truncate">
                        {{ data.total_stablity || "" }}
                      </div>
                    </td>
                    <td
                      v-if="!!data.user_row_count"
                      :rowspan="data.user_row_count"
                      style="padding: 0"
                    >
                      <div class="truncate">{{ data.total_goods || "" }}</div>
                    </td>
                    <td
                      v-if="!!data.user_row_count"
                      :rowspan="data.user_row_count"
                      style="padding: 0"
                    >
                      <div class="truncate">
                        {{ data.total_absence || "" }}
                      </div>
                    </td>

                    <td
                      v-if="!!data.user_row_count"
                      :rowspan="data.user_row_count"
                    >
                      {{
                        data.year_index == "第1年"
                          ? "首次评定"
                          : data.last_meeting_level
                      }}
                    </td>
                    <td
                      v-if="!!data.user_row_count"
                      :rowspan="data.user_row_count"
                    >
                      <select
                        v-model="data.is_commit"
                        style="width: 100%; height: 24px; line-height: 24px"
                      >
                        <option :value="''">请选择</option>
                        <option value="是">是</option>
                        <option value="否">否</option>
                      </select>
                    </td>
                    <td
                      style="text-align: left"
                      v-if="!!data.user_row_count"
                      :rowspan="data.user_row_count"
                    >
                      <span v-if="data.status == 30">{{
                        data.bak_remark
                      }}</span>
                      <a v-else @click="editRemark(data)">{{
                        (data.bak_remark || "") == "" ? "录入" : data.bak_remark
                      }}</a>
                    </td>
                  </tr>
                </tbody>
              </table>
              <span v-if="tableDataLength <= 0">
                {{ tableDataLength == -1 ? "加载中..." : "暂无数据" }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  findAllStudentPointToCheckByCompanyId,
  updateStudentRisePosition,
  updateStudentRisePositionSave,
  updateMeetingRemark,
  importAssessor,
  importMTraineeMeetingResult,
} from "@/api";
import PlanningDetailDialog from "./PlanningDetailDialog";
import { getCookie, TokenKey } from "@/utils/Cookie";
export default {
  data() {
    return {
      isLoading: true, //圈圈
      tableData: [],
      companyGroupId: "",
      graduateYear: "",
      schoolLevel: "",
      body_height: 0,
      mySort: "",
      year: "",
      month: "",
      buttonQueryText: "按届别显示",
      headers: {
        [TokenKey]: getCookie(TokenKey),
      },
      newFile: new FormData(),
      tableDataLength: -1,
    };
  },
  computed: {
    disabledButton() {
      if (this.isLoading) {
        return true;
      } else if (this.tableData == null || this.tableData.length == 0) {
        return true;
      } else if (
        this.tableData &&
        this.tableData[0] &&
        this.tableData.length != 0
      ) {
        return this.tableData[0].status !== 10 ? true : false;
      }
      return false;
    },
  },
  methods: {
    BeforeUpload(file) {
      // const fileSuffix = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()
      // const whiteList = ['csv']
      if (file) {
        this.newFile.append("file", file);
      } else {
        return false;
      }
    },
    Upload() {
      const loading = this.$loading({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      this.newFile.append("companyGroupId", this.companyGroupId);
      this.newFile.append("year", this.year);
      this.newFile.append("month", this.month);
      this.newFile.append("userType", "管培生");
      const newData = this.newFile;
      let params = newData;
      importMTraineeMeetingResult(params)
        .then((res) => {
          loading.close();
          layer.msg(res.data, { icon: 1, shade: 0.3, shadeClose: true });
          this._findAllStudentPointToCheckByCompanyId("1");
        })
        .catch((err) => {
          loading.close();
          layer.msg(`${err.msg}`, { icon: 2, shade: 0.3, shadeClose: true });
        });
    },
    getCompanyName() {
      return Utils.getCompanyNameByGroupId(this.companyGroupId);
    },
    sheetIt() {
      this.exportExcelOne.exportExcel(
        `【${this.getCompanyName()}】管培生综合能力评定结果合议.xlsx`,
        "#table"
      );
    },
    setGradeName(data) {
      if (data.student_is_rise) {
        data.student_grade_name = data.rise_grade_name;
      } else {
        data.student_grade_name = "";
      }
    },
    showDialog(data) {
      if (!data || !data.list || data.length < 0) {
        layer.msg("数据错误!");
        return;
      }
      let graduateYear = data.list[0].graduate_year,
        schoolLevel = data.list[0].school_level,
        type = "";

      let layerDialog = Utils.layerBox.layerDialogOpen({
        title: `【${graduateYear}届${Utils.number2ChNum(
          schoolLevel
        )}级岗位】管培生职务规划表`,
        btn: [],
        maxmin: false, //开启最大化最小化按钮
        area: ["650px", "330px"],
      });
      this.dialogComponent = Utils.layerBox.layerLoadVueComponent({
        layer: layerDialog,
        component: PlanningDetailDialog,
        methods: {
          doConfirm: (params, action) => {},
          doClose: () => {},
        },
        //传递本组件的参数给对话框组件，对话框组件通过props属性params获取值,例如下面这个val属性取值：this.params.val
        props: {
          graduateYear,
          schoolLevel,
          type,
        },
      });
    },
    viewPointDetails() {
      this.$router.push({
        path:
          "/" +
          this.year +
          "/" +
          this.month +
          "/trainee/evaluation/score/" +
          this.companyGroupId +
          "/detail?isview=2",
      });
    },
    viewPointDetails2(fdId, name) {
      this.$router.push({
        path:
          "/" +
          this.year +
          "/" +
          this.month +
          "/trainee/assessor2/" +
          this.companyGroupId +
          "/summary?fdId=" +
          fdId +
          "&name=" +
          name,
      });
    },
    viewGoodAbsence(item) {
      const { companyGroupId, year, month } = this.$route.params;
      this.companyGroupId = companyGroupId;
      this.year = year;
      this.month = month;
      var ablityId = item.fd_id;
      var url =
        "/" +
        this.year +
        "/" +
        this.month +
        "/Trainee/Staff/" +
        this.companyGroupId +
        "/viewGoodAbsence";
      this.$router.push({
        path: url,
        query: {
          ablityId: ablityId,
          year: this.year,
          month: this.month,
          companyGroupId: this.companyGroupId,
        },
      });
    },
    addGoodsAbsence(fdId) {
      var url =
        "/" +
        this.year +
        "/" +
        this.month +
        "/Trainee/Evaluation/" +
        this.companyGroupId +
        "/addGoodsAbsence";
      console.log(url);
      this.$router.push({
        path: url,
        query: {
          ablityId: fdId,
          isadd: 1 /*新增*/,
          year: this.year,
          month: this.month,
          companyGroupId: this.companyGroupId,
        },
      });
    },
    editRemark(item) {
      // if (item.status == 30) {
      //   return;
      // }
      var fdId = item.fd_id;
      var remark = item.bak_remark || "";
      var index = 100;
      Utils.layerBox.layerDialogOpen({
        title: "综合评价：备注",
        area: ["600px", "300px"],
        content:
          `<div style="padding:10px 20px;margin:0 auto;">
        <textarea id="text_remark" type="text" style="width:100%;height:170px;">` +
          remark +
          `</textarea></div>`,
        btn1: function (index) {
          remark = $("textarea[id='text_remark']").val();
          updateMeetingRemark({ fdId: fdId, remark: remark })
            .then((res) => {
              let result = res.success;
              layer.msg(`保存${result ? "成功" : "失败"}！${res.msg || ""}`, {
                icon: result ? 1 : 2,
                shade: 0.3,
                shadeClose: true,
              });
              item.bak_remark = remark;
            })
            .catch((err) => {
              layer.msg("保存失败！" + (err.msg || ""), {
                icon: 2,
                shade: 0.3,
                shadeClose: true,
              });
            });
          layer.close(index);
        },
      });
    },

    showDescrDialog() {
      Utils.layerBox.layerDialogOpen({
        title: "<b>评定说明：</b>",
        area: ["800px", "520px"],
        btn: ["关闭"],
        content: `<div style="padding:10px 20px;margin:0 auto;">
          <p style="line-height:24px;">
          一、评定方式<br/>
          1、成功要素：指的是运用成功要素（三大特质九大要素）对评定对象进行逐项评定。<br/>
          2、综合分：指的是根据评定对象的整体能力表现进行一个综合评定。<br/>
          二、评定人<br/>
          （一）评定人资格<br/>
          1、成功要素评定人资格：原则上需是熟悉成功要素标准（三大特质九大要素）且对评定对象的能力表现有足够了解的评定人。<br/>
          2、综合分评定人资格<br/>
          ①评定人属于首次接触成功要素标准，尚不能完全掌握其核心要点的，采用综合分方式评定。<br/>
          ②评定人熟悉成功要素标准但因对评定对象的能力表现只有整体的认知和链接，无法逐一对应成功要素进行评定的，采用综合分方式评定。<br/>
          （二）评定人人员及人数<br/>
          1、评定人员：从评定对象的业务关联领导、直接领导和间接领导中找到最熟悉其工作能力表现的人员。<br/>
          2、评定人数：2-5人。<br/>
          三、评定时间：每年6月份、12月份各评定1次。<br/>
          四、评定得分<br/>
          （一）评定选项及对应得分：（1）优：85-100分；（2）A：80-84分；（3）B：75-79分；（4）C：70-74分；（5）D：0-69分<br/>
          （二）积极性及稳定性得分：（1）积极性评分在70以上，则为积极；（2）稳定性评分在70分以上，则为稳定。<br/>
          （三）权重及得分<br/>
          1、评定人数为5人的，间接领导权重为30%，直接领导权重为20%，人资领导权重为20%，业务关联领导均为15%；<br>
          2、评定人数为4人的，间接领导权重为40%，直接领导权重为30%，2个业务关联领导均为15%；<br/>
          3、评定人数为3人的<br>
            （1）有间接领导的：间接领导权重为50%，直接领导和业务关联领导均为25%；<br>
            （2）没有间接领导的：直接领导权重为50%，2个关联业务领导各占25%；<br>
          4、评定人数为2人的<br/>
          （1）没有间接领导的：直接领导权重为60%，业务关联领导为40%；<br/>
          （2）有间接领导的：间接领导权重为60%，业务关联领导为40%。<br/>
          五、评定流程简要说明<br/>
          （一）评定人及评定方式确定：根据以上原则由人资部初步选定，选定后提交OA呈报各公司负责的领导审批。<br/>
          （二）能力评定<br/>
          1、评定流程：在评定人及评定方式审批后，系统自动触发OA评定表到各评定人的评定工作台进行评定。<br/>
          2、评定标准<br/>
          （1）以评定对象职务职等规划的当前阶段应该达到的能力水平作为“优秀”标准，比如二级管培生×××入职已经第四年，职务等级为三级经理，需以三级经理应该达到的能力水平作为优秀标准来进行评定。<br/>
          （2）评定人应对同一届别同一岗位级别的管培生进行横向的对比，原则上应该有能力的高低差异区分，人数较少的例外。<br/>
          （三）评定合议<br/>
          1、合议人：评定完成后，由集团统一组织各公司体系领导进行合议。<br/>
          2、合议单位：以同一公司、同一届别、同一岗位级别的人员为基本单位进行合议，理由主要是不同届别和岗位级别的管培生能力存在一定差别，而且培养的目标职务和年限也有所差别，放在一起合议不具备横向的可对比性。<br/>
          3、合议确定内容：评定对象的综合能力评定等级（优、A、B、C、D级）、积极态度以及稳定性。<br/>
          （1）能力等级为优、A、B、C级视为合格，D级视为不合格<br/>
          ①优级：提资时间按照A级（6个月提资一次）执行，同时可考虑提前晋升；<br/>
          ②A级：6个月提资一次；<br/>
          ③B级：9个月提资一次；<br/>
          ④C级：12个月提资一次；<br/>
          ⑤D级：原则上劝退；<br/>
          （2）积极态度和稳定性：具有一票否决权。<br/>
          （四）确定报批名单：默认所有管培生评定结果均需报批，即“是否提交审批”选项默认为“是”，该项为“是”的管培生，才可提交OA报批，若部分管培生经合议后，认为已不需呈报董事长审批的，则将选项调整为“否”，即不需提交OA审批。 <br/>
          （五）评定合议结果报批：在各公司合议完成后，以全集团为单位进行汇总报批。<br/>
          </p></div>`,
      });
    },
    _updateStudentRisePositionSave() {
      /*保存录入*/
      if (this.disabledButton) {
        return;
      }
      this.isLoading = true;
      let list = [];
      let value = 0;
      this.tableData.forEach(
        ({
          fd_id,
          meeting_level,
          meeting_point,
          student_rise_type,
          student_is_rise,
          student_grade_name,
          user_row_count,
          meeting_energy_point,
          meeting_stablity,
          adjust_result,
          is_commit,
        }) => {
          if (!!user_row_count) {
            if (meeting_point > 4 || meeting_point < 0) {
              value = meeting_point;
            }
            list.push({
              fdId: fd_id,
              meetingLevel: meeting_level,
              meetingPoint: meeting_point,
              studentRiseType: student_rise_type,
              studentIsRise: student_is_rise,
              studentGradeName: student_grade_name,
              meetingEnergyPoint: meeting_energy_point,
              meetingStablity: meeting_stablity,
              adjustResult: adjust_result,
              isCommit: is_commit,
            });
          }
        }
      );
      //console.log(value);
      if (value > 99 || value < 60) {
        layer.msg("输入非法得分，得分范围：60~99", {
          icon: 2,
          shade: 0.3,
          shadeClose: true,
        });
        this.isLoading = false;
        return;
      }

      updateStudentRisePositionSave(list)
        .then((res) => {
          let result = res.success;
          layer.msg(`保存${result ? "成功" : "失败"}！${res.msg || ""}`, {
            icon: result ? 1 : 2,
            shade: 0.3,
            shadeClose: true,
          });
          this._findAllStudentPointToCheckByCompanyId("0");
        })
        .catch((err) => {
          layer.msg("保存失败！" + (err.msg || ""), {
            icon: 2,
            shade: 0.3,
            shadeClose: true,
          });
        });
    },
    _updateStudentRisePosition() {
      /*合议完成确定*/
      if (this.disabledButton) {
        return;
      }
      layer.confirm(
        `是否“合议完成确认”，确认后不允许修改?`,
        { icon: 3 },
        (index) => {
          this.isLoading = true;
          let arr = [];
          this.tableData.forEach(
            ({
              fd_id,
              meeting_level,
              meeting_point,
              student_rise_type,
              student_is_rise,
              student_grade_name,
              user_row_count,
              meeting_energy_point,
              meeting_stablity,
              adjust_result,
              is_commit,
              user_name,
            }) => {
              if (!!user_row_count) {
                //arr.push({fdId:fd_id,meetingLevel:meeting_level,meetingPoint:meeting_point})
                arr.push({
                  fdId: fd_id,
                  meetingLevel: meeting_level,
                  meetingPoint: meeting_point,
                  studentRiseType: student_rise_type,
                  studentIsRise: student_is_rise,
                  studentGradeName: student_grade_name,
                  meetingEnergyPoint: meeting_energy_point,
                  meetingStablity: meeting_stablity,
                  adjustResult: adjust_result,
                  isCommit: is_commit,
                  name: user_name,
                });
              }
            }
          );
          let adj = arr.filter(
            (item) =>
              !item.adjustResult ||
              item.adjustResult == "请选择" ||
              item.adjustResult == "请选择"
          );
          let meeting = arr.filter(
            (item) =>
              !item.meetingStablity ||
              item.meetingStablity == "请选择" ||
              item.meetingStablity == "请选择"
          );
          let Commit = arr.filter(
            (item) =>
              !item.isCommit ||
              item.isCommit == "请选择" ||
              item.isCommit == "请选择"
          );
          if (adj.length > 0) {
            layer.msg("请选择“" + adj[0].name + "-留任结论”", {
              icon: 2,
              shade: 0.3,
              shadeClose: true,
            });
            this.isLoading = false;
            return;
          }
          if (meeting.length > 0) {
            layer.msg("请选择“" + meeting[0].name + "-稳定性”", {
              icon: 2,
              shade: 0.3,
              shadeClose: true,
            });
            this.isLoading = false;
            return;
          }
          if (Commit.length > 0) {
            layer.msg("请选择“" + Commit[0].name + "-是否提交审批”", {
              icon: 2,
              shade: 0.3,
              shadeClose: true,
            });
            this.isLoading = false;
            return;
          }
          updateStudentRisePosition(arr)
            .then((res) => {
              let result = res.success;
              layer.msg(
                `确认操作${result ? "成功" : "失败"}！${res.msg || ""}`,
                { icon: result ? 1 : 2, shade: 0.3, shadeClose: true }
              );
              this._findAllStudentPointToCheckByCompanyId("0");
            })
            .catch((err) => {
              layer.msg("确认操作！" + (err.msg || ""), {
                icon: 2,
                shade: 0.3,
                shadeClose: true,
              });
            });
        }
      );
    },
    _findAllStudentPointToCheckByCompanyId(queryType) {
      /*先判断有权限才可以查询(modify by huangdh@2019-05-24)*/
      if (
        this.$store.state.doubleCol.arrAuths
          .point_ablity_findAllStudentPointToCheckByCompanyId
      ) {
        findAllStudentPointToCheckByCompanyId({
          companyGroupId: this.companyGroupId,
          year: this.$route.params.year,
          month: this.$route.params.month,
          sortType: this.mySort,
        }).then((res) => {
          this.isLoading = false;
          this.tableData = res.data || [];
          this.tableData = this.tableData.map((item) => {
            item.meeting_level = item.meeting_level || null;
            item.student_is_rise = item.student_is_rise || null;
            return item;
          });
          if (queryType == "1") {
            if (this.mySort == "1") {
              this.mySort = "2";
              this.buttonQueryText = "按届别显示";
            } else {
              this.mySort = "1";
              this.buttonQueryText = "按评定得分显示";
            }
          }
          this.tableDataLength = this.tableData.length;
        });
      } else {
        layer.msg(`对不起，您没有权限查询本界面.`, {
          icon: 2,
          shade: 0.3,
          shadeClose: true,
        });
      }
    },

    isRealNum(val) {
      // isNaN()函数 把空串 空格 以及NUll 按照0来处理 所以先去除，
      if (val === "" || val == null) {
        return false;
      }
      if (!isNaN(val)) {
        //对于空数组和只有一个数值成员的数组或全是数字组成的字符串，isNaN返回false，例如：'123'、[]、[2]、['123'],isNaN返回false,
        //所以如果不需要val包含这些特殊情况，则这个判断改写为if(!isNaN(val) && typeof val === 'number' )
        return true;
      } else {
        return false;
      }
    },
    toFixed2(num) {
      if (num) {
        let num_str = num.toString().split(".");
        if (num_str.length == 1) {
          return num_str[0] + ".0";
        } else {
          return num_str[0] + "." + num_str[1].substring(0, 1);
        }
      } else {
        return "";
      }
    },
    toFixed1(num) {
      if (num) {
        let num_str = num.toString().split(".");
        if (num_str.length == 1) {
          return num_str[0] + ".0";
        } else {
          return num_str[0] + "." + num_str[1].substring(0, 1);
        }
      } else {
        return "";
      }
    },

    editMeetingPoint(item) {
      let point = item.meeting_point;
      if (point >= 85 && point <= 99) {
        item.meeting_level = "优";
      } else if (point >= 80 && point < 85) {
        item.meeting_level = "A";
      } else if (point >= 75 && point < 80) {
        item.meeting_level = "B";
      } else if (point >= 70 && point < 75) {
        item.meeting_level = "C";
      } else if (point < 70) {
        item.meeting_level = "D";
      } else {
        item.meeting_level = "";
      }
    },
    initPage() {
      this.body_height = $(window).height();
      const { companyGroupId, year, month } = this.$route.params;
      this.companyGroupId = companyGroupId;
      this.year = year;
      this.month = month;
      this.mySort = "1";
      this._findAllStudentPointToCheckByCompanyId("1");
    },
  },
  watch: {
    $route: function () {
      this.initPage();
    },
  },
  mounted() {
    $(window).resize(() => {
      this.body_height = $(window).height();
    });
    this.initPage();
    document.getElementsByClassName("el-upload__input")[0].style =
      "display:none";
  },
};
</script>

<style scoped>
input[type="number"] {
  -moz-appearance: textfield;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.myWidth {
  min-width: 1600px;
}
.buts {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-top: 7px;
}
.buts > div {
  line-height: 14px;
}
.btn {
  margin-right: 10px;
}
.truncate {
  display: inline-block;
  width: 2em;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}
.list-year-data {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
.scroll-table-body {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
th {
  padding: 2px !important;
}
td {
  padding: 2px !important;
}
.myWidth {
  padding: 0 !important;
}
</style>
