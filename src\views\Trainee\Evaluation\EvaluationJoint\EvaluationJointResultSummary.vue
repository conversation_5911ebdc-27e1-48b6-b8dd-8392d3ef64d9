<template>
  <div class="evaluation-detail-container">
    <div class="row">
      <div class="col-xs-12 text-left">
        <h4 class="col-xs-6">【{{ getCompanyName() }}】{{ $route.name }}</h4>
        <div class="col-xs-6 text-right" style="line-height: 40px">
          <button class="btn btn-primary btn-xs" @click="showDescrDialog">
            评定说明
          </button>
          <button
            type="button"
            class="btn btn-primary btn-xs"
            @click="sheetIt"
          >
            导出Excel
          </button>
          <el-upload
            :show-file-list="false"
            action=""
            :headers="headers"
            :before-upload="BeforeUpload"
            :http-request="Upload"
          >
            <template #trigger>
              <button type="button" class="btn btn-primary btn-xs">
                导入模板
              </button>
            </template>
          </el-upload>
          <button
            class="btn btn-primary btn-xs"
            @click="_findAllStudentPointToCheckByCompanyId('1')"
          >
            {{ buttonQueryText }}
          </button>
          <button
            class="btn btn-primary btn-xs"
            @click="_updateStudentRisePositionSave"
            :disabled="disabledButton"
            v-if="
              $store.state.doubleCol.arrAuths
                .point_ablity_updateStudentRisePositionSave
            "
          >
            保存录入
          </button>

          <button
            class="btn btn-primary btn-xs"
            @click="_updateStudentRisePosition"
            :disabled="disabledButton"
            v-if="
              $store.state.doubleCol.arrAuths
                .point_ablity_updateStudentRisePosition
            "
          >
            合议完成确定
          </button>
          <button
            class="btn btn-primary btn-xs"
            @click="
              $router.push(
                `/${$route.params.year}/${$route.params.month}/menu/2`
              )
            "
          >
            返回目录
          </button>
        </div>
      </div>
    </div>
    <div class="col-xs-12">
      <el-table
        id="table"
        :data="processedTableData"
        border
        :cell-style="{ 'text-align': 'center', padding: '4px 0' }"
        :header-cell-style="{ 'text-align': 'center', padding: '10px 0', height: '40px', lineHeight: '1.5' }"
        style="width: max-content"
        :max-height="body_height - 140 + 'px'"
        :span-method="objectSpanMethod"
        @cell-mouse-enter="onCellMouseEnter"
      >
        <!-- 序号列 -->
        <el-table-column label="序号" width="30" prop="index">
          <template slot-scope="scope">
            <template v-if="scope.row.isHeader">
              {{ scope.row.title }}
            </template>
            <template v-else>
              {{ scope.row.displayIndex || scope.row.index }}
            </template>
          </template>
        </el-table-column>

        <!-- 评定对象列组 -->
        <el-table-column label="姓名" width="45" prop="user_name" :formatter="formatHeaderCell"></el-table-column>
        <el-table-column label="届别" width="40" prop="graduate_year" :formatter="formatHeaderCell"></el-table-column>
        <el-table-column label="岗位级别" width="40" prop="school_level_name" :formatter="formatHeaderCell"></el-table-column>
        <el-table-column label="学历" width="40" prop="degree" :formatter="formatHeaderCell"></el-table-column>
        <el-table-column label="学校" width="85" prop="school" :formatter="formatHeaderCell"></el-table-column>
        <el-table-column label="部门" width="60" prop="dept_name" :formatter="formatHeaderCell"></el-table-column>
        <el-table-column label="岗位" width="60" prop="job_name" :formatter="formatHeaderCell"></el-table-column>
        <el-table-column label="职务等级" width="75" prop="grade_name" :formatter="formatHeaderCell"></el-table-column>
        <el-table-column label="年份" width="45" prop="year_index" :formatter="formatHeaderCell"></el-table-column>

        <!-- 评定明细列组 -->
        <el-table-column label="评定人" width="50" prop="rater_name" :formatter="formatHeaderCell"></el-table-column>
        <el-table-column label="评定方式" width="40" prop="judge_type" :formatter="formatHeaderCell"></el-table-column>
        <el-table-column label="权重" width="40" prop="weight_point" :formatter="formatWeightPoint"></el-table-column>
        <el-table-column label="评定得分" width="40" prop="point" :formatter="formatHeaderCell"></el-table-column>
        <el-table-column label="积极态度" width="40" prop="energy_point" :formatter="formatHeaderCell"></el-table-column>
        <el-table-column label="稳定性" width="45" prop="stablity" :formatter="formatHeaderCell"></el-table-column>
        <el-table-column label="明细查看" width="40" column-key="detailView">
          <template slot-scope="scope">
            <div v-if="!scope.row.isHeader" @click="viewPointDetails()" style="cursor: pointer;">
              <img src="../../../../assets/images/action_dtl.png" alt="" />
            </div>
          </template>
        </el-table-column>

        <!-- 工作亮点列 -->
        <el-table-column label="工作亮点" width="40" column-key="workHighlight">
          <template slot-scope="scope">
            <div v-if="!scope.row.isHeader" @click="viewPointDetails2(scope.row.fd_id, scope.row.user_name)" style="cursor: pointer;">
              <img src="../../../../assets/images/action_dtl.png" />
            </div>
          </template>
        </el-table-column>

        <!-- 本期评定列组 -->
        <el-table-column label="权重得分" width="55" prop="avg_point" :formatter="formatAvgPoint"></el-table-column>
        <el-table-column label="能力等级" width="35" prop="ablity_level" :formatter="formatHeaderCell"></el-table-column>
        <el-table-column label="积极态度" width="55" prop="avg_energy_point" :formatter="formatAvgEnergyPoint"></el-table-column>

        <!-- 本期合议列组 -->
        <el-table-column label="合议得分" width="68" column-key="meetingPoint">
          <template slot-scope="scope">
            <div v-if="!scope.row.isHeader">
              <input
                type="number"
                step="1"
                v-model="scope.row.meeting_point"
                class="form-control input-sm"
                style="width: 100%"
                @change="editMeetingPoint(scope.row)"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="能力等级" width="54" prop="meeting_level" :formatter="formatHeaderCell"></el-table-column>
        <el-table-column label="积极态度" width="40" column-key="meetingEnergyPoint">
          <template slot-scope="scope">
            <div v-if="!scope.row.isHeader">
              <input
                type="number"
                step="1"
                v-model="scope.row.meeting_energy_point"
                class="form-control input-sm"
                style="width: 100%"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="稳定性" width="54" column-key="meetingStablity">
          <template slot-scope="scope">
            <div v-if="!scope.row.isHeader">
              <select
                v-model="scope.row.meeting_stablity"
                style="width: 100%; height: 24px; line-height: 24px"
              >
                <option :value="''">请选择</option>
                <option value="稳定">稳定</option>
                <option value="不稳定">不稳定</option>
              </select>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="留任结论" width="45" column-key="adjustResult">
          <template slot-scope="scope">
            <div v-if="!scope.row.isHeader">
              <select
                v-model="scope.row.adjust_result"
                style="width: 100%; height: 24px; line-height: 24px"
              >
                <option :value="''">请选择</option>
                <option value="留任">留任</option>
                <option value="劝退">劝退</option>
                <option value="留职察看">留职察看</option>
                <option value="转普通管理人员">转普通管理人员</option>
              </select>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="综合评价" width="40" column-key="comprehensiveEvaluation">
          <template slot-scope="scope">
            <div v-if="!scope.row.isHeader">
              <a
                v-if="scope.row.status == 30"
                @click="addGoodsAbsence(scope.row.fd_id)"
              >
                <img src="../../../../assets/images/action_dtl.png" alt="" />
              </a>
              <a
                v-if="scope.row.status != 30"
                @click="addGoodsAbsence(scope.row.fd_id)"
              >
                录入
              </a>
            </div>
          </template>
        </el-table-column>

        <!-- 综合评价列组 -->
        <el-table-column label="积极态度" width="40" prop="total_energy" :formatter="formatTruncateCell"></el-table-column>
        <el-table-column label="稳定性" width="40" prop="total_stablity" :formatter="formatTruncateCell"></el-table-column>
        <el-table-column label="优点" width="45" prop="total_goods" :formatter="formatTruncateCell"></el-table-column>
        <el-table-column label="不足" width="45" prop="total_absence" :formatter="formatTruncateCell"></el-table-column>

        <!-- 其他列 -->
        <el-table-column label="上期审批结果" width="45" :formatter="formatLastMeetingLevel"></el-table-column>
        <el-table-column label="是否提交审批" width="45" column-key="isCommit">
          <template slot-scope="scope">
            <div v-if="!scope.row.isHeader">
              <select
                v-model="scope.row.is_commit"
                style="width: 100%; height: 24px; line-height: 24px"
              >
                <option :value="''">请选择</option>
                <option value="是">是</option>
                <option value="否">否</option>
              </select>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="备注" width="45" column-key="remark">
          <template slot-scope="scope">
            <div v-if="!scope.row.isHeader" style="text-align: left">
              <span v-if="scope.row.status == 30">{{ scope.row.bak_remark }}</span>
              <a v-else @click="editRemark(scope.row)">
                {{ (scope.row.bak_remark || "") == "" ? "录入" : scope.row.bak_remark }}
              </a>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div v-if="tableDataLength <= 0" class="text-center p-3">
        {{ tableDataLength == -1 ? "加载中..." : "暂无数据" }}
      </div>
    </div>

  </div>
</template>

<script>
import {
  findAllStudentPointToCheckByCompanyId,
  updateStudentRisePosition,
  updateStudentRisePositionSave,
  updateMeetingRemark,
  importAssessor,
  importMTraineeMeetingResult,
} from "@/api";
import PlanningDetailDialog from "./PlanningDetailDialog";
import { getCookie, TokenKey } from "@/utils/Cookie";
export default {
  data() {
    return {
      isLoading: true, //圈圈
      tableData: [],
      companyGroupId: "",
      graduateYear: "",
      schoolLevel: "",
      body_height: 0,
      mySort: "",
      year: "",
      month: "",
      buttonQueryText: "按届别显示",
      headers: {
        [TokenKey]: getCookie(TokenKey),
      },
      newFile: new FormData(),
      tableDataLength: -1,
    };
  },
  computed: {
    processedTableData() {
      // 处理表格数据，将嵌套结构转换为扁平结构，并添加标题行
      const result = [];

      if (!this.tableData || this.tableData.length === 0) return result;

      // 按用户分组数据
      const userGroups = {};
      this.tableData.forEach((item, index) => {
        const userId = item.fd_id;
        if (!userGroups[userId]) {
          userGroups[userId] = [];
        }
        userGroups[userId].push({
          ...item,
          index: index + 1,
        });
      });

      // 为每个用户组添加标题行和数据行
      let displayIndex = 1;
      Object.keys(userGroups).forEach((userId) => {
        const userItems = userGroups[userId];
        const firstItem = userItems[0];

        // 添加标题行
        result.push({
          isHeader: true,
          title: `${firstItem.user_name || ''} - ${firstItem.dept_name || ''} - ${firstItem.job_name || ''}`,
          headerIndex: displayIndex - 1
        });

        // 添加用户数据行
        userItems.forEach((item, itemIndex) => {
          result.push({
            ...item,
            isStudent: true,
            displayIndex: itemIndex === 0 ? displayIndex : null, // 只在第一行显示序号
          });
        });

        displayIndex++;
      });

      return result;
    },
    disabledButton() {
      if (this.isLoading) {
        return true;
      } else if (this.tableData == null || this.tableData.length == 0) {
        return true;
      } else if (
        this.tableData &&
        this.tableData[0] &&
        this.tableData.length != 0
      ) {
        return this.tableData[0].status !== 10 ? true : false;
      }
      return false;
    },
  },
  methods: {
    // 表格格式化方法
    formatHeaderCell(row, column) {
      if (row.isHeader) {
        return "";
      }
      const value = row[column.property];
      return value || "";
    },
    formatWeightPoint(row, column) {
      if (row.isHeader) {
        return "";
      }
      if (row.weight_point) {
        return (parseFloat(row.weight_point) * 100).toFixed(1) + "%";
      }
      return "";
    },
    formatAvgPoint(row, column) {
      if (row.isHeader) {
        return "";
      }
      if (row.avg_point != null) {
        return this.toFixed1(row.avg_point);
      }
      return "";
    },
    formatAvgEnergyPoint(row, column) {
      if (row.isHeader) {
        return "";
      }
      if (row.avg_energy_point != null) {
        return this.toFixed2(row.avg_energy_point);
      }
      return "";
    },
    formatTruncateCell(row, column) {
      if (row.isHeader) {
        return "";
      }
      const value = row[column.property];
      return value || "";
    },
    formatLastMeetingLevel(row, column) {
      if (row.isHeader) {
        return "";
      }
      return row.year_index == "第1年" ? "首次评定" : row.last_meeting_level;
    },
    // 表格合并方法
    objectSpanMethod(params) {
      const { row, column, rowIndex, columnIndex } = params;

      // 标题行只进行横向合并，不纵向合并
      if (row.isHeader) {
        if (columnIndex === 0) {
          // 使用实际的列数进行合并
          const tableColumns = document.querySelectorAll('.el-table__header-wrapper th').length;
          return {
            rowspan: 1,
            colspan: tableColumns || 33 // 如果无法获取实际列数，使用33作为备选
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }

      // 通过列索引判断是否是前9列
      const isFirstNineColumns = columnIndex < 10;

      // 确定哪些列需要合并
      // 确定表格的总列数
      const allColumns = document.querySelectorAll('.el-table__header-wrapper th').length;

      // 通过索引判断是否是最后几列
      // 使用保守的判断方式
      const isLastColumns = (allColumns > 3 && columnIndex >= allColumns - 12) ||
                            // 使用columnKey作为备选判断方式
                            ['workHighlight', 'meetingPoint', 'meetingEnergyPoint', 'meetingStablity', 'adjustResult', 'comprehensiveEvaluation', 'isCommit', 'remark'].includes(column.columnKey);

      // 处理需要合并的列
      if ((isFirstNineColumns || isLastColumns) && !row.isHeader) {
        // 获取当前行所在的组内索引
        const groupIndex = this.getGroupIndex(row);
        if (groupIndex !== -1) {
          // 判断组内索引的奇偶性
          const isEvenIndex = groupIndex % 2 === 0;

          if (isEvenIndex) {
            // 检查下一行是否存在、不是标题行，且属于同一组
            const nextRowIndex = rowIndex + 1;
            if (nextRowIndex < this.processedTableData.length &&
                !this.processedTableData[nextRowIndex].isHeader &&
                this.getSameGroupRows(row).includes(this.processedTableData[nextRowIndex])) {
              return {
                rowspan: 2,
                colspan: 1
              };
            }
          } else {
            // 奇数索引行不显示
            return {
              rowspan: 0,
              colspan: 0
            };
          }
        }
      }

      // 其他单元格正常显示
      return {
        rowspan: 1,
        colspan: 1
      };
    },

    // 获取行在其分组内的索引
    getGroupIndex(row) {
      if (row.isHeader) return -1;

      // 找到当前行所属的组
      let headerIndex = -1;
      let groupRows = [];

      // 遍历找到当前行前面最近的标题行
      for (let i = 0; i < this.processedTableData.length; i++) {
        const currentRow = this.processedTableData[i];
        if (currentRow === row) {
          break;
        }
        if (currentRow.isHeader) {
          headerIndex = i;
          groupRows = [];
        } else {
          groupRows.push(currentRow);
        }
      }

      // 如果找到了标题行，计算当前行在组内的索引
      if (headerIndex !== -1) {
        return groupRows.length;
      }

      return -1;
    },

    // 获取与当前行属于同一组的所有行
    getSameGroupRows(row) {
      if (row.isHeader) return [];

      // 找到当前行所属的组
      let inCurrentGroup = false;
      let groupRows = [];

      for (let i = 0; i < this.processedTableData.length; i++) {
        const currentRow = this.processedTableData[i];

        if (currentRow.isHeader) {
          // 如果已经找到过当前行，说明已经超出了当前组
          if (inCurrentGroup) {
            break;
          }
          // 开始一个新组
          groupRows = [];
        } else {
          groupRows.push(currentRow);
        }

        // 标记找到当前行
        if (currentRow === row) {
          inCurrentGroup = true;
        }
      }

      return groupRows;
    },
    onCellMouseEnter() {
      // 当鼠标进入单元格时调整合并单元格样式
      this.adjustMergedCellsStyle();
    },
    // 添加专门处理合并单元格样式的方法
    adjustMergedCellsStyle() {
      setTimeout(() => {
        // 获取所有合并的单元格并设置垂直居中样式
        const rows = document.getElementsByClassName("el-table__row");
        for (let i = 0; i < rows.length; i++) {
          const cells = rows[i].cells;
          if (cells) {
            for (let j = 0; j < cells.length; j++) {
              if (cells[j] && cells[j].rowSpan > 1) {
                cells[j].style.verticalAlign = 'middle';
                // 确保图标居中显示
                if (cells[j].querySelector('img')) {
                  cells[j].style.textAlign = 'center';
                }
              }
            }
          }
        }
      }, 300);
    },
    // 添加一个专门的方法来处理表格显示
    adjustTableDisplay() {
      setTimeout(() => {
        // 获取所有行
        let rows = document.getElementsByClassName("el-table__row");

        this.processedTableData.forEach((item, index) => {
          if (item.isHeader && rows[index]) {
            // 标题行处理 - 设置样式
            if (rows[index].cells && rows[index].cells.length > 0) {
              const firstCell = rows[index].cells[0];

              // 设置单元格的背景色
              firstCell.style.background = "#eee";

              // 设置内部内容的样式 - 关键是这里需要操作children[0]
              if (firstCell.children && firstCell.children.length > 0) {
                firstCell.children[0].style.fontWeight = "bold";
                firstCell.children[0].style.textAlign = "left";
                firstCell.children[0].style.marginLeft = "10px";
              }

              // 确保标题行只显示标题内容
              if (firstCell.children && firstCell.children.length > 0) {
                firstCell.children[0].innerText = item.title;
              } else {
                firstCell.textContent = item.title;
              }

              // 尝试获取实际列数并设置colspan
              const tableColumns = document.querySelectorAll('.el-table__header-wrapper th').length;
              if (tableColumns && tableColumns > 0) {
                firstCell.colSpan = tableColumns;

                // 隐藏其他列
                for (let i = 1; i < rows[index].cells.length; i++) {
                  if (rows[index].cells[i]) {
                    rows[index].cells[i].style.display = 'none';
                  }
                }
              }
            }
          } else if (!item.isHeader && rows[index]) {
            // 非标题行处理 - 确保合并的单元格样式一致
            const cells = rows[index].cells;
            if (cells && cells.length > 0) {
              // 设置合并单元格的样式
              for (let i = 0; i < cells.length; i++) {
                // 如果是合并的单元格（有rowspan属性且值大于1）
                if (cells[i] && cells[i].rowSpan > 1) {
                  cells[i].style.verticalAlign = 'middle';
                }
              }
            }
          }
        });
      }, 500);
    },
    pageResize() {
      this.body_height = $(window).height();
    },
    BeforeUpload(file) {
      // const fileSuffix = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()
      // const whiteList = ['csv']
      if (file) {
        this.newFile.append("file", file);
      } else {
        return false;
      }
    },
    Upload() {
      const loading = this.$loading({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      this.newFile.append("companyGroupId", this.companyGroupId);
      this.newFile.append("year", this.year);
      this.newFile.append("month", this.month);
      this.newFile.append("userType", "管培生");
      const newData = this.newFile;
      let params = newData;
      importMTraineeMeetingResult(params)
        .then((res) => {
          loading.close();
          layer.msg(res.data, { icon: 1, shade: 0.3, shadeClose: true });
          this._findAllStudentPointToCheckByCompanyId("1");
        })
        .catch((err) => {
          loading.close();
          layer.msg(`${err.msg}`, { icon: 2, shade: 0.3, shadeClose: true });
        });
    },
    getCompanyName() {
      return Utils.getCompanyNameByGroupId(this.companyGroupId);
    },
    sheetIt() {
      this.exportExcelOne.exportExcel(
        `【${this.getCompanyName()}】管培生综合能力评定结果合议.xlsx`,
        "#table"
      );
    },
    setGradeName(data) {
      if (data.student_is_rise) {
        data.student_grade_name = data.rise_grade_name;
      } else {
        data.student_grade_name = "";
      }
    },
    showDialog(data) {
      if (!data || !data.list || data.length < 0) {
        layer.msg("数据错误!");
        return;
      }
      let graduateYear = data.list[0].graduate_year,
        schoolLevel = data.list[0].school_level,
        type = "";

      let layerDialog = Utils.layerBox.layerDialogOpen({
        title: `【${graduateYear}届${Utils.number2ChNum(
          schoolLevel
        )}级岗位】管培生职务规划表`,
        btn: [],
        maxmin: false, //开启最大化最小化按钮
        area: ["650px", "330px"],
      });
      this.dialogComponent = Utils.layerBox.layerLoadVueComponent({
        layer: layerDialog,
        component: PlanningDetailDialog,
        methods: {
          doConfirm: (params, action) => {},
          doClose: () => {},
        },
        //传递本组件的参数给对话框组件，对话框组件通过props属性params获取值,例如下面这个val属性取值：this.params.val
        props: {
          graduateYear,
          schoolLevel,
          type,
        },
      });
    },
    viewPointDetails() {
      this.$router.push({
        path:
          "/" +
          this.year +
          "/" +
          this.month +
          "/trainee/evaluation/score/" +
          this.companyGroupId +
          "/detail?isview=2",
      });
    },
    viewPointDetails2(fdId, name) {
      this.$router.push({
        path:
          "/" +
          this.year +
          "/" +
          this.month +
          "/trainee/assessor2/" +
          this.companyGroupId +
          "/summary?fdId=" +
          fdId +
          "&name=" +
          name,
      });
    },
    viewGoodAbsence(item) {
      const { companyGroupId, year, month } = this.$route.params;
      this.companyGroupId = companyGroupId;
      this.year = year;
      this.month = month;
      var ablityId = item.fd_id;
      var url =
        "/" +
        this.year +
        "/" +
        this.month +
        "/Trainee/Staff/" +
        this.companyGroupId +
        "/viewGoodAbsence";
      this.$router.push({
        path: url,
        query: {
          ablityId: ablityId,
          year: this.year,
          month: this.month,
          companyGroupId: this.companyGroupId,
        },
      });
    },
    addGoodsAbsence(fdId) {
      var url =
        "/" +
        this.year +
        "/" +
        this.month +
        "/Trainee/Evaluation/" +
        this.companyGroupId +
        "/addGoodsAbsence";
      console.log(url);
      this.$router.push({
        path: url,
        query: {
          ablityId: fdId,
          isadd: 1 /*新增*/,
          year: this.year,
          month: this.month,
          companyGroupId: this.companyGroupId,
        },
      });
    },
    editRemark(item) {
      // if (item.status == 30) {
      //   return;
      // }
      var fdId = item.fd_id;
      var remark = item.bak_remark || "";
      var index = 100;
      Utils.layerBox.layerDialogOpen({
        title: "综合评价：备注",
        area: ["600px", "300px"],
        content:
          `<div style="padding:10px 20px;margin:0 auto;">
        <textarea id="text_remark" type="text" style="width:100%;height:170px;">` +
          remark +
          `</textarea></div>`,
        btn1: function (index) {
          remark = $("textarea[id='text_remark']").val();
          updateMeetingRemark({ fdId: fdId, remark: remark })
            .then((res) => {
              let result = res.success;
              layer.msg(`保存${result ? "成功" : "失败"}！${res.msg || ""}`, {
                icon: result ? 1 : 2,
                shade: 0.3,
                shadeClose: true,
              });
              item.bak_remark = remark;
            })
            .catch((err) => {
              layer.msg("保存失败！" + (err.msg || ""), {
                icon: 2,
                shade: 0.3,
                shadeClose: true,
              });
            });
          layer.close(index);
        },
      });
    },

    showDescrDialog() {
      Utils.layerBox.layerDialogOpen({
        title: "<b>评定说明：</b>",
        area: ["800px", "520px"],
        btn: ["关闭"],
        content: `<div style="padding:10px 20px;margin:0 auto;">
          <p style="line-height:24px;">
          一、评定方式<br/>
          1、成功要素：指的是运用成功要素（三大特质九大要素）对评定对象进行逐项评定。<br/>
          2、综合分：指的是根据评定对象的整体能力表现进行一个综合评定。<br/>
          二、评定人<br/>
          （一）评定人资格<br/>
          1、成功要素评定人资格：原则上需是熟悉成功要素标准（三大特质九大要素）且对评定对象的能力表现有足够了解的评定人。<br/>
          2、综合分评定人资格<br/>
          ①评定人属于首次接触成功要素标准，尚不能完全掌握其核心要点的，采用综合分方式评定。<br/>
          ②评定人熟悉成功要素标准但因对评定对象的能力表现只有整体的认知和链接，无法逐一对应成功要素进行评定的，采用综合分方式评定。<br/>
          （二）评定人人员及人数<br/>
          1、评定人员：从评定对象的业务关联领导、直接领导和间接领导中找到最熟悉其工作能力表现的人员。<br/>
          2、评定人数：2-5人。<br/>
          三、评定时间：每年6月份、12月份各评定1次。<br/>
          四、评定得分<br/>
          （一）评定选项及对应得分：（1）优：85-100分；（2）A：80-84分；（3）B：75-79分；（4）C：70-74分；（5）D：0-69分<br/>
          （二）积极性及稳定性得分：（1）积极性评分在70以上，则为积极；（2）稳定性评分在70分以上，则为稳定。<br/>
          （三）权重及得分<br/>
          1、评定人数为5人的，间接领导权重为30%，直接领导权重为20%，人资领导权重为20%，业务关联领导均为15%；<br>
          2、评定人数为4人的，间接领导权重为40%，直接领导权重为30%，2个业务关联领导均为15%；<br/>
          3、评定人数为3人的<br>
            （1）有间接领导的：间接领导权重为50%，直接领导和业务关联领导均为25%；<br>
            （2）没有间接领导的：直接领导权重为50%，2个关联业务领导各占25%；<br>
          4、评定人数为2人的<br/>
          （1）没有间接领导的：直接领导权重为60%，业务关联领导为40%；<br/>
          （2）有间接领导的：间接领导权重为60%，业务关联领导为40%。<br/>
          五、评定流程简要说明<br/>
          （一）评定人及评定方式确定：根据以上原则由人资部初步选定，选定后提交OA呈报各公司负责的领导审批。<br/>
          （二）能力评定<br/>
          1、评定流程：在评定人及评定方式审批后，系统自动触发OA评定表到各评定人的评定工作台进行评定。<br/>
          2、评定标准<br/>
          （1）以评定对象职务职等规划的当前阶段应该达到的能力水平作为“优秀”标准，比如二级管培生×××入职已经第四年，职务等级为三级经理，需以三级经理应该达到的能力水平作为优秀标准来进行评定。<br/>
          （2）评定人应对同一届别同一岗位级别的管培生进行横向的对比，原则上应该有能力的高低差异区分，人数较少的例外。<br/>
          （三）评定合议<br/>
          1、合议人：评定完成后，由集团统一组织各公司体系领导进行合议。<br/>
          2、合议单位：以同一公司、同一届别、同一岗位级别的人员为基本单位进行合议，理由主要是不同届别和岗位级别的管培生能力存在一定差别，而且培养的目标职务和年限也有所差别，放在一起合议不具备横向的可对比性。<br/>
          3、合议确定内容：评定对象的综合能力评定等级（优、A、B、C、D级）、积极态度以及稳定性。<br/>
          （1）能力等级为优、A、B、C级视为合格，D级视为不合格<br/>
          ①优级：提资时间按照A级（6个月提资一次）执行，同时可考虑提前晋升；<br/>
          ②A级：6个月提资一次；<br/>
          ③B级：9个月提资一次；<br/>
          ④C级：12个月提资一次；<br/>
          ⑤D级：原则上劝退；<br/>
          （2）积极态度和稳定性：具有一票否决权。<br/>
          （四）确定报批名单：默认所有管培生评定结果均需报批，即“是否提交审批”选项默认为“是”，该项为“是”的管培生，才可提交OA报批，若部分管培生经合议后，认为已不需呈报董事长审批的，则将选项调整为“否”，即不需提交OA审批。 <br/>
          （五）评定合议结果报批：在各公司合议完成后，以全集团为单位进行汇总报批。<br/>
          </p></div>`,
      });
    },
    _updateStudentRisePositionSave() {
      /*保存录入*/
      if (this.disabledButton) {
        return;
      }
      this.isLoading = true;
      let list = [];
      let value = 0;
      this.tableData.forEach(
        ({
          fd_id,
          meeting_level,
          meeting_point,
          student_rise_type,
          student_is_rise,
          student_grade_name,
          user_row_count,
          meeting_energy_point,
          meeting_stablity,
          adjust_result,
          is_commit,
        }) => {
          if (!!user_row_count) {
            if (meeting_point > 4 || meeting_point < 0) {
              value = meeting_point;
            }
            list.push({
              fdId: fd_id,
              meetingLevel: meeting_level,
              meetingPoint: meeting_point,
              studentRiseType: student_rise_type,
              studentIsRise: student_is_rise,
              studentGradeName: student_grade_name,
              meetingEnergyPoint: meeting_energy_point,
              meetingStablity: meeting_stablity,
              adjustResult: adjust_result,
              isCommit: is_commit,
            });
          }
        }
      );
      //console.log(value);
      if (value > 99 || value < 60) {
        layer.msg("输入非法得分，得分范围：60~99", {
          icon: 2,
          shade: 0.3,
          shadeClose: true,
        });
        this.isLoading = false;
        return;
      }

      updateStudentRisePositionSave(list)
        .then((res) => {
          let result = res.success;
          layer.msg(`保存${result ? "成功" : "失败"}！${res.msg || ""}`, {
            icon: result ? 1 : 2,
            shade: 0.3,
            shadeClose: true,
          });
          this._findAllStudentPointToCheckByCompanyId("0");
        })
        .catch((err) => {
          layer.msg("保存失败！" + (err.msg || ""), {
            icon: 2,
            shade: 0.3,
            shadeClose: true,
          });
        });
    },
    _updateStudentRisePosition() {
      /*合议完成确定*/
      if (this.disabledButton) {
        return;
      }
      layer.confirm(
        `是否“合议完成确认”，确认后不允许修改?`,
        { icon: 3 },
        (index) => {
          this.isLoading = true;
          let arr = [];
          this.tableData.forEach(
            ({
              fd_id,
              meeting_level,
              meeting_point,
              student_rise_type,
              student_is_rise,
              student_grade_name,
              user_row_count,
              meeting_energy_point,
              meeting_stablity,
              adjust_result,
              is_commit,
              user_name,
            }) => {
              if (!!user_row_count) {
                //arr.push({fdId:fd_id,meetingLevel:meeting_level,meetingPoint:meeting_point})
                arr.push({
                  fdId: fd_id,
                  meetingLevel: meeting_level,
                  meetingPoint: meeting_point,
                  studentRiseType: student_rise_type,
                  studentIsRise: student_is_rise,
                  studentGradeName: student_grade_name,
                  meetingEnergyPoint: meeting_energy_point,
                  meetingStablity: meeting_stablity,
                  adjustResult: adjust_result,
                  isCommit: is_commit,
                  name: user_name,
                });
              }
            }
          );
          let adj = arr.filter(
            (item) =>
              !item.adjustResult ||
              item.adjustResult == "请选择" ||
              item.adjustResult == "请选择"
          );
          let meeting = arr.filter(
            (item) =>
              !item.meetingStablity ||
              item.meetingStablity == "请选择" ||
              item.meetingStablity == "请选择"
          );
          let Commit = arr.filter(
            (item) =>
              !item.isCommit ||
              item.isCommit == "请选择" ||
              item.isCommit == "请选择"
          );
          if (adj.length > 0) {
            layer.msg("请选择“" + adj[0].name + "-留任结论”", {
              icon: 2,
              shade: 0.3,
              shadeClose: true,
            });
            this.isLoading = false;
            return;
          }
          if (meeting.length > 0) {
            layer.msg("请选择“" + meeting[0].name + "-稳定性”", {
              icon: 2,
              shade: 0.3,
              shadeClose: true,
            });
            this.isLoading = false;
            return;
          }
          if (Commit.length > 0) {
            layer.msg("请选择“" + Commit[0].name + "-是否提交审批”", {
              icon: 2,
              shade: 0.3,
              shadeClose: true,
            });
            this.isLoading = false;
            return;
          }
          updateStudentRisePosition(arr)
            .then((res) => {
              let result = res.success;
              layer.msg(
                `确认操作${result ? "成功" : "失败"}！${res.msg || ""}`,
                { icon: result ? 1 : 2, shade: 0.3, shadeClose: true }
              );
              this._findAllStudentPointToCheckByCompanyId("0");
            })
            .catch((err) => {
              layer.msg("确认操作！" + (err.msg || ""), {
                icon: 2,
                shade: 0.3,
                shadeClose: true,
              });
            });
        }
      );
    },
    _findAllStudentPointToCheckByCompanyId(queryType) {
      /*先判断有权限才可以查询(modify by huangdh@2019-05-24)*/
      if (
        this.$store.state.doubleCol.arrAuths
          .point_ablity_findAllStudentPointToCheckByCompanyId
      ) {
        findAllStudentPointToCheckByCompanyId({
          companyGroupId: this.companyGroupId,
          year: this.$route.params.year,
          month: this.$route.params.month,
          sortType: this.mySort,
        }).then((res) => {
          this.isLoading = false;
          this.tableData = res.data || [];
          this.tableData = this.tableData.map((item) => {
            item.meeting_level = item.meeting_level || null;
            item.student_is_rise = item.student_is_rise || null;
            return item;
          });
          if (queryType == "1") {
            if (this.mySort == "1") {
              this.mySort = "2";
              this.buttonQueryText = "按届别显示";
            } else {
              this.mySort = "1";
              this.buttonQueryText = "按评定得分显示";
            }
          }
          this.tableDataLength = this.tableData.length;

          // 数据加载后处理表格显示
          this.$nextTick(() => {
            this.adjustTableDisplay();
            this.adjustMergedCellsStyle();
          });
        });
      } else {
        layer.msg(`对不起，您没有权限查询本界面.`, {
          icon: 2,
          shade: 0.3,
          shadeClose: true,
        });
      }
    },

    isRealNum(val) {
      // isNaN()函数 把空串 空格 以及NUll 按照0来处理 所以先去除，
      if (val === "" || val == null) {
        return false;
      }
      if (!isNaN(val)) {
        //对于空数组和只有一个数值成员的数组或全是数字组成的字符串，isNaN返回false，例如：'123'、[]、[2]、['123'],isNaN返回false,
        //所以如果不需要val包含这些特殊情况，则这个判断改写为if(!isNaN(val) && typeof val === 'number' )
        return true;
      } else {
        return false;
      }
    },
    toFixed2(num) {
      if (num) {
        let num_str = num.toString().split(".");
        if (num_str.length == 1) {
          return num_str[0] + ".0";
        } else {
          return num_str[0] + "." + num_str[1].substring(0, 1);
        }
      } else {
        return "";
      }
    },
    toFixed1(num) {
      if (num) {
        let num_str = num.toString().split(".");
        if (num_str.length == 1) {
          return num_str[0] + ".0";
        } else {
          return num_str[0] + "." + num_str[1].substring(0, 1);
        }
      } else {
        return "";
      }
    },

    editMeetingPoint(item) {
      let point = item.meeting_point;
      if (point >= 85 && point <= 99) {
        item.meeting_level = "优";
      } else if (point >= 80 && point < 85) {
        item.meeting_level = "A";
      } else if (point >= 75 && point < 80) {
        item.meeting_level = "B";
      } else if (point >= 70 && point < 75) {
        item.meeting_level = "C";
      } else if (point < 70) {
        item.meeting_level = "D";
      } else {
        item.meeting_level = "";
      }
    },
    initPage() {
      this.body_height = $(window).height();
      const { companyGroupId, year, month } = this.$route.params;
      this.companyGroupId = companyGroupId;
      this.year = year;
      this.month = month;
      this.mySort = "1";
      this._findAllStudentPointToCheckByCompanyId("1");
    },
  },
  watch: {
    $route: function () {
      this.initPage();
    },
  },
  mounted() {
    window.addEventListener("resize", this.pageResize);
    this.pageResize();
    this.initPage();

    // 隐藏上传组件的input
    this.$nextTick(() => {
      const uploadInput = document.getElementsByClassName("el-upload__input")[0];
      if (uploadInput) {
        uploadInput.style.display = "none";
      }
    });

    // 确保表格正确渲染后调整样式
    this.$nextTick(() => {
      this.adjustTableDisplay();
      this.adjustMergedCellsStyle();
    });
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.pageResize);
  },
};
</script>

<style scoped>
.evaluation-detail-container {
  height: 100%;
  padding: 0 15px;
  overflow: hidden;
}

input[type="number"] {
  -moz-appearance: textfield;
  appearance: textfield;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  appearance: none;
  margin: 0;
}

.btn {
  margin-right: 10px;
}

.truncate {
  display: inline-block;
  width: 2em;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.el-table /deep/ .el-table--border .el-table__cell {
  border-bottom: 1px solid #ebeef5 !important;
}

.el-table /deep/ .el-table .cell {
  padding: 2px !important;
  font-size: 13px;
  line-height: 1.3;
}

/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}

.text-center {
  text-align: center;
}

.p-3 {
  padding: 1rem;
}
</style>
