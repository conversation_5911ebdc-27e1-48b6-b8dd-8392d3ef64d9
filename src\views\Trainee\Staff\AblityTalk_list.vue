<template>
  <div class="staff-people-detail-container">
      <div class="row">
        <div class="col-xs-12 text-left">
          <h4 class="col-xs-6">{{getCompanyName()}}管培生综合能力【评定结果面谈】表</h4>
          <div class="col-xs-6 text-right" style="line-height:40px;">
            <button class="btn btn-primary btn-xs" @click="showDescrDialog()">说明</button>
            <button class="btn btn-primary btn-xs" @click="_triggerTalk()" :disabled="disabledButton">触发面谈</button>
            <button class="btn btn-primary btn-xs" @click="$router.push(`/${$route.params.year}/${$route.params.month}/menu/2`)">返回目录</button>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-xs-12">
            <div class="list-year-head-table" style="background:#b7d8dc;">
              <table class="list-year-head table table-bordered" :style="`margin-bottom:0;margin:0 auto 0 0;`">
                <colgroup>
                    <col width='50'/>
                    <col width='120'/>
                    <col width='60'/>
                    <col width='80'/>
                    <col width='80'/>
                    <col width='100'/>
                    <col width='130'/>
                    <col width='100'/>
                    <col width='70'/>
                    <col width='70'/>

                    <col width='80'/>
                    <col width='130'/>
                    <col width='90'/>
                    <col width='70'/>
                    <col width='70'/>
                    <col width='70'/>
                </colgroup>
                <thead>
                  <tr>
                    <th rowspan="2">序号</th>
                    <th colspan="7">谈话对象</th>
                    <th colspan="2">评定结果</th>
                    <th colspan="4">谈话人</th>
                    <th colspan="2">谈话情况</th>
                  </tr>
                  <tr>
                    <th>管培生类型</th>
                    <th>届别</th>
                    <th>岗位级别</th>
                    <th>姓名</th>
                    <th>部门</th>
                    <th>职务</th>
                    <th>职等</th>
                    <th>能力等级</th>
                    <th>留任结论</th>
                    <th>姓名</th>
                    <th>职务</th>
                    <th>职等</th>
                    <th>调整</th>
                    <th>状态</th>
                    <th>内容</th>
                    <th>删除</th>
                  </tr>
                </thead>
            </table>
          </div>
          <div class="list-year-data"  :style="{maxHeight:body_height - 130 + 'px','overflow-y':'scroll','overflow-x':'hidden','border-bottom':'1px solid #ccc'}">
              <table style="margin-bottom:0;" class="table table-bordered">
                <colgroup>
                    <col width='50'/>
                    <col width='120'/>
                    <col width='60'/>
                    <col width='80'/>
                    <col width='80'/>
                    <col width='100'/>
                    <col width='130'/>
                    <col width='100'/>
                    <col width='70'/>
                    <col width='70'/>

                    <col width='80'/>
                    <col width='130'/>
                    <col width='90'/>
                    <col width='70'/>
                    <col width='70'/>
                    <col width='70'/>
                </colgroup>
                <tbody>
                    <tr v-for="(data,$index) in tableData" :key="$index">
                      <td v-if="data.rowIndex==1" :rowspan="data.rowSpan">{{data.userIndex}}</td>
                      <td v-if="data.rowIndex==1" :rowspan="data.rowSpan">{{data.trainName||''}}</td>
                      <td v-if="data.rowIndex==1" :rowspan="data.rowSpan">{{data.graduateYear}}</td>
                      <td v-if="data.rowIndex==1" :rowspan="data.rowSpan">{{getSchoolLevel(data.schoolLevel)}}</td>
                      <td v-if="data.rowIndex==1" :rowspan="data.rowSpan">{{data.userName}}</td>
                      <td v-if="data.rowIndex==1" :rowspan="data.rowSpan">{{data.deptName||''}}</td>
                      <td v-if="data.rowIndex==1" :rowspan="data.rowSpan">{{data.jobName||''}}</td>
                      <td v-if="data.rowIndex==1" :rowspan="data.rowSpan">{{data.gradeName||''}}</td>
                      <td v-if="data.rowIndex==1" :rowspan="data.rowSpan">{{data.adjustLevel}}</td>
                      <td v-if="data.rowIndex==1" :rowspan="data.rowSpan">{{data.adjustResult}}</td>
                      <td>{{data.adjustUserName==null?data.talkUserName : data.adjustUserName}}</td>
                      <td>{{data.adjustPositionName==null?data.talkPositionName : data.adjustPositionName}}</td>
                      <td>{{data.adjustGradeName==null?data.talkGradeName : data.adjustGradeName}}</td>
                      <td><a @click="showDialog(data)" >{{data.adjustUserName==null?'操作' : '已调整'}}</a></td>
                      <td>{{data.status==30 && data.talkStatus=='已面谈'?'已面谈':'未面谈'}}</td>
                      <td><a @click="toGoodAbsence(data)" >查看</a></td>
                      <td><a v-if="data.talkStatus=='未面谈' || !data.talkStatus" @click="_talkListIsDelete(data.fdId)" >删除</a></td>
                    </tr>
                </tbody>
            </table>
            <span v-if="tableDataLength <= 0">
              {{ tableDataLength == -1 ? "加载中..." : "暂无数据" }}
            </span>
          </div>
        </div>
      </div>
  </div>
</template>

<script>
import AdjustTalker from './AdjustTalker'
import { findTalkListBy,triggerTalk,talkListIsDelete } from '@/api'
export default {
  data(){
    return {
      body_height:0,
      tableData:[],
      companyGroupId:'',
      year:'',
      month:'',
      tableDataLength: -1
    }
  },
  computed:{
    disabledButton(){
        if(this.isLoading){
            return true;
        }
        else if(this.tableData==null || this.authCompanyName === "您无权限查阅"){
            return true;
        }
        else if(this.tableData&&this.tableData[0]&&this.tableData.length!=0){
            return this.tableData[0].status >= 11 ? true : false;
        }
        return false;
    }
  },
  methods:{
    getSchoolLevel(level){
      return Utils.number2ChNum(level)+'级';
    },
    getCompanyName(){
      return Utils.getCompanyNameByGroupId(this.companyGroupId);
    },
    showDialog(data){
      if(data.status>=11){
        return;
      }
      let layerDialog = Utils.layerBox.layerDialogOpen({
        title: `谈话人调整`,
        btn: [],
        maxmin: false, //开启最大化最小化按钮
        area: ["400px", '300px']
      });
      this.dialogComponent = Utils.layerBox.layerLoadVueComponent({
        layer: layerDialog,
        component: AdjustTalker,
        methods: {
          doConfirm: (params, action) => {},
          doClose: () => {}
        },
        //传递本组件的参数给对话框组件，对话框组件通过props属性params获取值,例如下面这个val属性取值：this.params.val
        props: {
          fdId:data.fdId,
          userName:data.userName||'',
          talkUserName: (data.adjustUserName==null? data.talkUserName:data.adjustUserName),
          talkPositionName: (data.adjustPositionName==null? data.talkPositionName:data.adjustPositionName),
          talkGradeName: (data.adjustGradeName==null? data.talkGradeName:data.adjustGradeName),
          closeComponent:()=>{
            layer.close(layerDialog);
            this._findTalkListBy();
          }
        }
      });
    },
    _findTalkListBy(){
      /*先判断有权限才可以查询(modify by huangdh@2019-05-24)*/
      if (this.$store.state.doubleCol.arrAuths.point_ablity_findStudentListByCompanyGroupId)
      {
        findTalkListBy({companyGroupId:this.companyGroupId,year:this.year,month:this.month}).then(res=>{
            this.tableData = res.data || [];
            this.tableDataLength = this.tableData.length
        })
      }
      else {
          layer.msg(`对不起，您没有权限查询本界面.`, {icon: 2,shade:0.3,shadeClose:true});
      }
    },

    showDescrDialog(){
      Utils.layerBox.layerDialogOpen({
          title:'<b>说明：</b>',
          area:['680px','320px'],
          btn:[],
          content: `<div style="padding:10px 20px 0 20px;margin:0 auto;">
          <p style="line-height:24px;">
1、谈话人资格：直接领导、间接领导，若直接领导和间接领导是同一人，则安排直接领导谈话即可。<br/>
2、谈话人调整：若有特殊情况，需要调整谈话人的，由人资部操作人直接点击“调整”，重新选择谈话人<br/>
3、触发面谈：确定谈话人后，由人资部操作人点击“触发谈话”，系统自动推送谈话提醒到谈话人和谈话对象OA工作台<br/>
4、谈话内容：谈话人在与谈话对象沟通时，需要告知其综合能力评定结果、优点、不足以及今后努力的方向<br/>
5、谈话记录：谈话结束后，谈话对象填写《谈话记录表》，传谈话人审批。
          </p></div>`});
    },
    _triggerTalk(){
      if(this.disabledButton){
        return;
      }
      layer.confirm('确认要触发面谈吗？', {
          title:'提示',
          btn: ['确定','取消'] //按钮
        }, (index)=>{
          this.isLoading = true;
          triggerTalk({year:this.year,month:this.month,companyGroupId:this.companyGroupId}).then(res=>{
              let result = res.success;
              layer.msg(`提交OA${result ? '成功' : '失败'}`, {icon: result ? 1 : 2,shade:0.3,shadeClose:true});
              this._findTalkListBy();
              layer.close(index)
          }).catch(err=>{
            layer.msg(`${err.msg}`, {icon: 2,shade:0.3,shadeClose:true});
          })
        });
    },

    _talkListIsDelete(fdId){
      if(this.disabledButton){
        return;
      }
      layer.confirm('确认删除吗？', {
          title:'提示',
          btn: ['确定','取消'] //按钮
        }, (index)=>{
          this.isLoading = true;
          talkListIsDelete({fdId:fdId}).then(res=>{
              let result = res.success;
              layer.msg(`操作${result ? '成功' : '失败'}`, {icon: result ? 1 : 2,shade:0.3,shadeClose:true});
              this._findTalkListBy();
              layer.close(index)
          }).catch(err=>{
            layer.msg(`${err.msg}`, {icon: 2,shade:0.3,shadeClose:true});
          })
        });
    },


    toGoodAbsence(item){
          var ablityTalkId=item.fdId;
          var url='/'+this.year+'/'+this.month+'/Trainee/Staff/'+this.companyGroupId+'/talkRecord';

          this.$router.push({path:url,
            query:{fdId:ablityTalkId }
          });
    },
    initPage(){
      this.body_height = $(window).height();
      const {companyGroupId,year,month} = this.$route.params;
      this.companyGroupId = companyGroupId;
      this.year = year;
      this.month = month;
      this._findTalkListBy();
    }
  },
  watch:{
    "$route":function(){
      this.initPage();
    }
  },
  mounted(){
    this.initPage();
    $(window).resize(()=>{
      this.body_height = $(window).height();
    });
  }
}
</script>

<style scoped>
/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}
.list-year-data {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
.scroll-table-body {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
</style>
