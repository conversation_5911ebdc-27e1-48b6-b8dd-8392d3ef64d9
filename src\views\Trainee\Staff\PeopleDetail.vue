<template>
  <div class="staff-people-detail-container">
    <div class="row">
      <div class="col-xs-12 text-left">
        <h4 class="col-xs-6">【{{ getCompanyName() }}】管培生明细表</h4>
        <div class="col-xs-6 text-right" style="line-height: 40px">
          <button class="btn btn-primary btn-xs" @click="showDescrDialog2()">
            新增人员
          </button>
          <button class="btn btn-primary btn-xs" @click="showDescrDialog()">
            说明
          </button>
          <button
            class="btn btn-primary btn-xs"
            @click="
              $router.push(
                `/${$route.params.year}/${$route.params.month}/menu/2`
              )
            "
          >
            返回目录
          </button>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-xs-12">
        <div class="list-year-head-table" style="background: #b7d8dc">
          <table
            class="list-year-head table table-bordered"
            :style="`margin-bottom:0;margin:0 auto 0 0;`"
          >
            <colgroup>
              <col width="50" />
              <col width="120" />
              <col width="80" />
              <col width="80" />
              <col width="100" />
              <col width="60" />
              <col width="100" />
              <col width="130" />
              <col width="130" />
              <col width="80" />
              <col width="100" />
              <col width="100" />
              <col width="70" />
              <col width="80" />
            </colgroup>
            <thead>
              <tr>
                <th>序号</th>
                <th>部门</th>
                <th>岗位级别</th>
                <th>届别</th>
                <th>姓名</th>
                <th>性别</th>
                <th>学历</th>
                <th>岗位</th>
                <th>职务等级</th>
                <th>年份</th>
                <th>是否参加<br />本次评定</th>
                <th>备注</th>
                <th>评定调整</th>
                <th>转管理人员</th>
              </tr>
            </thead>
          </table>
        </div>
        <div
          class="list-year-data"
          :style="{
            maxHeight: body_height - 130 + 'px',
            'overflow-y': 'scroll',
            'overflow-x': 'hidden',
            'border-bottom': '1px solid #ccc',
          }"
        >
          <table style="margin-bottom: 0" class="table table-bordered">
            <colgroup>
              <col width="50" />
              <col width="120" />
              <col width="80" />
              <col width="80" />
              <col width="100" />
              <col width="60" />
              <col width="100" />
              <col width="130" />
              <col width="130" />
              <col width="80" />
              <col width="100" />
              <col width="100" />
              <col width="70" />
              <col width="80" />
            </colgroup>
            <tbody>
              <tr v-for="(data, $index) in tableData" :key="$index">
                <td>{{ $index + 1 }}</td>
                <td v-if="!data.useDay1" :rowspan="data.useDay2">
                  {{ data.deptName || "" }}
                </td>
                <td v-if="!data.useDay3" :rowspan="data.sortIndex">
                  {{ getSchoolLevel(data.schoolLevel) || "" }}
                </td>
                <td>{{ data.graduateYear || "--" }}</td>
                <td>{{ data.userName || "" }}</td>
                <td>{{ data.gender || "" }}</td>
                <td>{{ data.degree || "" }}</td>
                <td>{{ data.jobName || "" }}</td>
                <td>{{ data.gradeName || "" }}</td>
                <td>
                  {{
                    data.graduateYear
                      ? `第${data.fdYear - data.graduateYear}年`
                      : "--"
                  }}
                </td>
                <td>{{ data.status == 0 ? "不参加" : "参加" }}</td>
                <td>{{ getRemark(data.fdId) || "" }}</td>
                <td>
                  <a
                    @click="showDialog(data)"
                    v-if="
                      $store.state.doubleCol.arrAuths
                        .base_adjustGraduateYear_save
                    "
                    >调整</a
                  >
                </td>
                <td>
                  <a
                    @click="showToManagerDate(data)"
                    v-if="
                      $store.state.doubleCol.arrAuths
                        .base_adjustGraduateYear_save
                    "
                    >转管理人员</a
                  >
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import GraduateYearAdjust from "./GraduateYearAdjust";
import {
  findStudentListByCompanyGroupId,
  findStudentListByCompanyGroupId2,
  addSocialManager,
  findAllAdjustGraduateYear,
  updateToManagerDateNew,
} from "@/api";
export default {
  data() {
    return {
      body_height: 0,
      tableData: [],
      companyGroupId: "",
      year: "",
      month: "",
      userName: "",
      remarks: [],
    };
  },
  methods: {
    getRemark(ablityId) {
      let result = this.remarks.find((item) => {
        return item.ablityId == ablityId;
      });
      return result ? result.remark : "";
    },
    getSchoolLevel(level) {
      if (!level) {
        return "--";
      }
      return Utils.number2ChNum(level) + "级";
    },
    getCompanyName() {
      return Utils.getCompanyNameByGroupId(this.companyGroupId);
    },
    showDialog(data) {
      let layerDialog = Utils.layerBox.layerDialogOpen({
        title: `评定调整`,
        btn: [],
        maxmin: false, //开启最大化最小化按钮
        area: ["500px", "400px"],
      });
      //console.log("status:"+data.status);
      this.dialogComponent = Utils.layerBox.layerLoadVueComponent({
        layer: layerDialog,
        component: GraduateYearAdjust,
        methods: {
          doConfirm: (params, action) => {},
          doClose: () => {},
        },
        //传递本组件的参数给对话框组件，对话框组件通过props属性params获取值,例如下面这个val属性取值：this.params.val
        props: {
          userId: data.userId || "",
          userName: data.userName || "",
          companyGroupId: data.companyGroupId || "",
          schoolLevel: data.schoolLevel || "",
          graduateYear: data.graduateYear || "",
          ablityId: data.fdId,
          status: data.status == 0 ? "0" : data.status,
          closeComponent: () => {
            layer.close(layerDialog);
            this._findAllAdjustGraduateYear(
              this._findStudentListByCompanyGroupId()
            );
          },
        },
      });
    },

    showToManagerDate(data) {
      if (this.disabledButton) {
        return;
      }
      var date = new Date();
      var seperator1 = "-";
      var year = date.getFullYear();
      var month = date.getMonth() + 1;
      var strDate = date.getDate();
      if (month >= 1 && month <= 9) {
        month = "0" + month;
      }
      if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
      }
      var currentdate = year + seperator1 + month + seperator1 + strDate;

      Utils.layerBox.layerDialogOpen({
        title: "管培生转管理人员",
        area: ["450px", "280px"],
        btn: ["确定", "关闭"],
        content:
          `<div style="padding:10px 20px;margin:0 auto;">
                <div style="padding-bottom:10px;margin-left:70px;">姓名：` +
          data.userName +
          `</div>
                <div>转管理人员日期：<input type="text" id="validDate" style="height:22px;"></div>
                <div style="padding-top:10px"><span style="padding-left:42px">转入公司：</span>
                <select id="positionGradeId2" class="form-control input-sm" style="width:170px;display:inline-block;">
                  <option value=''>请选择</option>
                  <option value='10002'>集团总部</option>
                  <option value='11000'>销售公司</option>
                  <option value='16000'>赣州纸业纸品</option>
                  <option value='15000'>广西竹林</option>
                  <option value='22747'>崇左纸业</option>
                </select></div>
                <div style="padding-top:10px">转管理人员职等：
                <select id="positionGradeId" class="form-control input-sm" style="width:170px;display:inline-block;">
                  <option value=''>请选择</option>
                  <option value='0032'>高级经理级</option>
                  <option value='0009'>经理级</option>
                  <option value='0010'>副经理级</option>
                  <option value='0011'>主任级</option>
                  <option value='0026'>厂长</option>
                  <option value='0034'>副厂长</option>
                </select></div>
                <div style="margin-top:10px;color:red;">注：管培生转为管理人员后，数据将不在本界面显示.</div>
                </div>`,
        success: () => {
          laydate.render({
            elem: "#validDate", //指定元素
          });
        },
        btn1: (index, layero) => {
          if ($(validDate).val() == "") {
            layer.msg(`请选择转管理人员日期.`, {
              icon: 2,
              shade: 0.3,
              shadeClose: true,
            });
            return;
          }
          let selectedDate = $(validDate).val();

          layer.confirm(
            "管培生转为管理人员，请确认？",
            {
              title: "提示",
              btn: ["确定", "取消"], //按钮
            },
            (index) => {
              this.isLoading = true;
              updateToManagerDateNew({
                userId: data.userId,
                fdYear: this.year,
                fdMonth: this.month,
                toManagerDate: selectedDate,
                newCompanyId: $(positionGradeId2)[0].value,
                newPositionGradeId: $(positionGradeId)[0].value,
              })
                .then((res) => {
                  let result = res.success;
                  layer.msg(`操作${result ? "成功" : "失败"}`, {
                    icon: result ? 1 : 2,
                    shade: 0.3,
                    shadeClose: true,
                  });
                  this._findStudentListByCompanyGroupId();
                  layer.close(index);
                  layer.close(index - 1);
                })
                .catch((err) => {
                  layer.msg(`${err.msg}`, {
                    icon: 2,
                    shade: 0.3,
                    shadeClose: true,
                  });
                  layer.close(index);
                });
            }
          );
        },
        btn2: (index, layero) => {
          layer.close(index);
        },
      });
    },

    _findStudentListByCompanyGroupId() {
      // console.log("this.companyGroupId:" + this.companyGroupId);
      /*先判断有权限才可以查询(modify by huangdh@2019-05-24)*/
      if (
        this.$store.state.doubleCol.arrAuths
          .point_ablity_findStudentListByCompanyGroupId
      ) {
        findStudentListByCompanyGroupId({
          companyGroupId: this.companyGroupId,
          year: this.year,
          month: this.month,
          status: 0,
        }).then((res) => {
          this.tableData = res.data || [];
        });
      } else {
        layer.msg(`对不起，您没有权限查询本界面.`, {
          icon: 2,
          shade: 0.3,
          shadeClose: true,
        });
      }
    },
    _findAllAdjustGraduateYear(cb) {
      findAllAdjustGraduateYear().then((res) => {
        this.remarks = res.data || [];
        cb && cb();
      });
    },
    showDescrDialog() {
      Utils.layerBox.layerDialogOpen({
        title: "<b>说明：</b>",
        area: ["600px", "280px"],
        btn: [],
        content: `<div style="padding:10px 20px 0 20px;margin:0 auto;">
          <p style="line-height:24px;">
            1、信息来源：自动读取PS系统，仅显示当前月份的在职人员。<br/>
            2、评定调整：<br/>
            （1）公司调整：对于部分跨公司调动的人员，可能会调整到原任职公司一起评定对比。<br/>
            （2）届别或岗位级别调整：部分社招的人员，但进入公司后请示按管培生体系培养，会需要调整届别、岗位级别信息，如上海郝卓林等。<br/>
            （3）是否参加本次评定：默认所有管培生参加评定，但部分管培生由于已达到培养年限或职务，经合议后，认为已不需参加评定，则将选项调整“不参加”。
          </p></div>`,
      });
    },
    showDescrDialog2() {
      Utils.layerBox.layerDialogOpen({
        title: "<b>新增管培生：</b>",
        area: ["240px", "180px"],
        btn: ["确定", "关闭"],
        content: `<div id="mySelect" style="padding-top: 10px">
      <div style="display: flex; padding: 10px 0 0 10px">
        <div style="width: 80px; text-align: right">姓名：</div>
        <input
          id="userIpt"
          style="width: 100px"
          v-model="userName"
          placeholder="请输入姓名"
        />
      </div>
      <div style="display: flex; padding: 10px 0 0 10px">
        <div style="width: 80px; text-align: right">岗位级别：</div>
        <select
          id="positionGradeId3"
          class="form-control input-sm"
          style="width: 100px; display: inline-block"
        >
          <option value="">请选择</option>
          <option value="一级">一级</option>
          <option value="二级">二级</option>
          <option value="三级">三级</option>
        </select>
      </div>
    </div>`,
        btn1: (index) => {
          addSocialManager({
            year: this.year,
            month: this.month,
            userName: $(userIpt)[0].value,
            positionLevel: $(positionGradeId3)[0].value,
          })
            .then((res) => {
              let result = res.success;
              layer.msg(`操作${result ? "成功" : "失败"}`, {
                icon: result ? 1 : 2,
                shade: 0.3,
                shadeClose: true,
              });
              this._findStudentListByCompanyGroupId();
              layer.close(index);
              layer.close(index - 1);
            })
            .catch((err) => {
              layer.msg(`${err.msg}`, {
                icon: 2,
                shade: 0.3,
                shadeClose: true,
              });
              layer.close(index);
            });
        },
        btn2: (index, layero) => {
          layer.close(index);
        },
      });
    },
    initPage() {
      this.body_height = $(window).height();
      const { companyGroupId, year, month } = this.$route.params;
      this.companyGroupId = companyGroupId;
      if (companyGroupId == "shezhao") {
        this.$router.replace({
          path: "../shezhao",
          params: {
            year: year,
            month: month,
            name: "shezhao",
          },
        });
      }
      this.year = year;
      this.month = month;
      this._findAllAdjustGraduateYear(this._findStudentListByCompanyGroupId());
    },
  },
  watch: {
    $route: function () {
      this.initPage();
    },
  },
  mounted() {
    this.initPage();
    $(window).resize(() => {
      this.body_height = $(window).height();
    });
  },
};
</script>

<style scoped>
/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}
.list-year-data {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
.scroll-table-body {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
</style>