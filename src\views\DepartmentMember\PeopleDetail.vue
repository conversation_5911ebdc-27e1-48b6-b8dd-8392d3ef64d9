<template>
  <div class="people-detail-container">
      <div class="row">
        <div class="col-xs-12 text-left">
          <h4 class="col-xs-7">【{{getCompanyName()}}】各部门人员明细</h4>
          <div class="col-xs-5 text-right" style="padding: 0;line-height: 38px;">
            <button class="btn btn-primary btn-xs" @click="$router.push(`/${$route.params.year}/${$route.params.month}/menu/1`)">返回目录</button>
          </div>
        </div>
        <div class="col-xs-6"></div>
      </div>
      <div class="row">
        <div class="col-xs-12">
          <div class="list-table">
            <div class="scroll-table" style="height:100%;overflow-x:hidden;">
                <table class="scroll-table-header table-bordered" :style="{width:`calc(100% - ${1.4*12 +'px'})`}">
                  <colgroup>
                    <col style="width:45px"/>
                    <col style="width:13.5%"/>
                    <col style="width:13.5%"/>
                    <col style="width:13.5%"/>
                    <col style="width:13.5%"/>
                    <col style="width:13.5%"/>
                    <col style="width:13.5%"/>
                    <col style="width:13.5%"/>
                  </colgroup>
                  <thead>
                    <tr>
                      <th>序号</th>
                      <th>厂/部门</th>
                      <th>姓名</th>
                      <th>性别</th>
                      <th>岗位</th>
                      <th>职务</th>
                      <th>职等</th>
                      <th>任职时长</th>
                    </tr>
                  </thead>
                </table>
                <div class="scroll-table-body" :style="{overflow:'auto',maxHeight:body_height - 140 + 'px','overflow-y':'scroll','overflow-x':'hidden'}">
                    <table style="margin:0px 10px 0 0;position: relative;width:100%;font-size:13px;float:left;border:none;" class="table table-bordered table-hover table-striped">
                        <colgroup>
                          <col style="width:45px"/>
                          <col style="width:13.5%"/>
                          <col style="width:13.5%"/>
                          <col style="width:13.5%"/>
                          <col style="width:13.5%"/>
                          <col style="width:13.5%"/>
                          <col style="width:13.5%"/>
                          <col style="width:13.5%"/>
                        </colgroup>
                        <tbody>
                          <tr v-for="(data,$index) in tableData" :key="$index">
                            <td>{{$index+1}}</td>
                            <td>{{data.deptName}}</td>
                            <td>{{data.userName}}</td>
                            <td>{{data.gender}}</td>
                            <td>{{data.jobName}}</td>
                            <td>{{data.positionName}}</td>
                            <td>{{data.gradeName}}</td>
                            <td>{{Math.floor(data.workLong/12) <= 0 ? '' : (Math.floor(data.workLong/12)+'年')}}{{data.workLong%12===0?'':(data.workLong%12+'个月')}}</td>
                          </tr>
                      </tbody>
                    </table>
                </div>
            </div>
          </div>
        </div>
      </div>
  </div>
</template>

<script>
import { findDeptMemberDetailListByCompanyGroupId } from '@/api'
export default {
  data(){
    return {
      body_height: 0,
      tableData:[],
      companyGroupId:'',
      year:'',
      month:'',
    }
  },
  methods:{
    getCompanyName(){
      return Utils.getCompanyNameByGroupId(this.companyGroupId);
    },
    _findDeptMemberDetailListByCompanyGroupId(){
      findDeptMemberDetailListByCompanyGroupId({companyGroupId:this.companyGroupId,year:this.year,month:this.month}).then(res=>{
        if(!res.success) return;
        this.tableData = res.data ? res.data : []; 
      })
    },
    initPage(){
      const {companyGroupId,year,month} = this.$route.params;
      this.companyGroupId = companyGroupId;
      this.year = year;
      this.month = month;
      this._findDeptMemberDetailListByCompanyGroupId();
    },
    pageResize(){
      this.body_height = $(window).height();  
    }
  },
  watch:{
    "$route":function(){
      this.pageResize();
      this.initPage();
    }
  },
  created(){
    this.initPage();
  },
  mounted(){
      $(window).resize(this.pageResize);
      this.pageResize();
  }
}
</script>

<style>

</style>
