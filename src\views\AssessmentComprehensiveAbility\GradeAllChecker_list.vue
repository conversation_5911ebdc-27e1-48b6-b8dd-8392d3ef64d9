<template>
  <div class="benchmarking-personnel-container">
    <div class="row">
      <div class="col-xs-12 text-left">
        <h4 class="col-xs-6">
          【{{ getCompanyName() }}】{{ getGradeName() }}{{ $route.name }}
        </h4>
        <div class="col-xs-6 text-right" style="line-height: 40px">
          <button
            type="button"
            class="btn btn-primary btn-xs"
            @click="showDescrDialog"
          >
            说明
          </button>
          <button
            type="button"
            class="btn btn-primary btn-xs"
            :disabled="disabledButton"
            @click="_updateDirectLeaderByHalfYear()"
            v-if="
              $store.state.doubleCol.arrAuths
                .point_ablity_updateStandardEmployeeToOA
            "
          >
            职级评定发起
          </button>
          <button
            class="btn btn-primary btn-xs"
            @click="$router.push(`/${$route.params.year}/${month}/menu/1`)"
          >
            返回目录
          </button>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-xs-12">
        <div class="list-table">
          <div class="scroll-table" style="height: 100%; overflow-x: hidden">
            <table class="scroll-table-header table-bordered">
              <colgroup>
                <col style="width: 40px" />
                <col style="width: 90px" />
                <col style="width: 60px" />
                <col style="width: 40px" />
                <col style="width: 90px" />
                <col style="width: 90px" />
                <col style="width: 70px" />

                <col style="width: 60px" />
                <col style="width: 80px" />
                <col style="width: 60px" />
                <col style="width: 60px" />
                <col style="width: 80px" />
                <col style="width: 60px" />
                <col style="width: 60px" />
                <col style="width: 80px" />
                <col style="width: 60px" />
              </colgroup>
              <thead>
                <tr>
                  <th rowspan="2">序号</th>
                  <th rowspan="2">厂/部门</th>
                  <th colspan="5">评定对象</th>
                  <th colspan="3">评定人</th>
                  <th colspan="3">审核人</th>
                  <th colspan="3">审定人</th>
                </tr>
                <tr>
                  <th>姓名</th>
                  <th>性别</th>
                  <th>岗位</th>
                  <th>职务</th>
                  <th>职等</th>
                  <th>姓名</th>
                  <th>完成时间</th>
                  <th>用时（天）</th>
                  <th>姓名</th>
                  <th>完成时间</th>
                  <th>用时（天）</th>
                  <th>姓名</th>
                  <th>完成时间</th>
                  <th>用时（天）</th>
                </tr>
              </thead>
            </table>
            <div
              class="scroll-table-body"
              :style="{
                overflow: 'auto',
                maxHeight: body_height - 160 + 'px',
                'overflow-y': 'scroll',
                'overflow-x': 'hidden',
              }"
            >
              <table
                style="
                  margin: 0px 10px 0 0;
                  position: relative;
                  font-size: 13px;
                  float: left;
                  border: none;
                "
                class="table table-bordered table-hover table-striped"
              >
                <colgroup>
                  <col style="width: 40px" />
                  <col style="width: 90px" />
                  <col style="width: 60px" />
                  <col style="width: 40px" />
                  <col style="width: 90px" />
                  <col style="width: 90px" />
                  <col style="width: 70px" />

                  <col style="width: 60px" />
                  <col style="width: 80px" />
                  <col style="width: 60px" />
                  <col style="width: 60px" />
                  <col style="width: 80px" />
                  <col style="width: 60px" />
                  <col style="width: 60px" />
                  <col style="width: 80px" />
                  <col style="width: 60px" />
                </colgroup>
                <tbody>
                  <tr v-for="(data, $index) in tableData" :key="$index">
                    <td>{{ $index + 1 }}</td>
                    <td>{{ data.deptName }}</td>
                    <td>{{ data.userName }}</td>
                    <td>{{ data.gender }}</td>
                    <td>{{ data.jobName }}</td>
                    <td>{{ data.positionName }}</td>
                    <td>{{ data.gradeName }}</td>
                    <td>{{ data.leaderName }}</td>
                    <td>
                      {{
                        data.leaderDate != null
                          ? data.leaderDate.substring(0, 10)
                          : ""
                      }}
                    </td>
                    <td>
                      {{ DateDiff(data.actionGradeDate, data.leaderDate) }}
                    </td>
                    <td>{{ data.checkerName }}</td>
                    <td>
                      {{
                        data.checkerDate != null
                          ? data.checkerDate.substring(0, 10)
                          : ""
                      }}
                    </td>
                    <td>{{ DateDiff(data.leaderDate, data.checkerDate) }}</td>
                    <td>{{ data.approveName }}</td>
                    <td>
                      {{
                        data.approveDate != null
                          ? data.approveDate.substring(0, 10)
                          : ""
                      }}
                    </td>
                    <td>{{ DateDiff(data.checkerDate, data.approveDate) }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  findMergeListByCompanyGroupId,
  updateDirectLeaderByHalfYear,
  updateNotCommitSave,
} from "@/api";
export default {
  data() {
    return {
      isLoading: true,
      body_height: 0,
      originData: "",
      tableData: [],
      year: "",
      month: "",
      companyGroupId: "",
      positionGradeId: "",
      deptArea: Utils.getQueryParams("deptArea"),
    };
  },
  watch: {
    originData(val) {
      let data = JSON.parse(val);
      this.tableData = data.sort((a, b) => {
        return (
          parseInt(b.deptId) - parseInt(a.deptId) ||
          b.jobName.localeCompare(a.jobName, "zh")
        );
      });
    },
    $route: function () {
      this.initPage();
    },
  },
  computed: {
    disabledButton() {
      /*提交OA或职级评定发起 */
      if (this.isLoading || this.tableData.length == 0) {
        return true;
      } else if (this.tableData && this.tableData.length != 0) {
        let flag = 0;
        this.tableData.forEach((item) => {
          if (
            (item.confirmLeaderStatus == 30 && item.actionGradeDate != null) ||
            (item.confirmLeaderStatus != 30 && item.actionGradeDate == null)
          ) {
            flag = 1;
          }
        });
        return flag == 1 ? true : false;
      }
      return true;
    },

    gradeRank() {
      return this.$route.meta.gradeRank;
    },
  },
  methods: {
    getCompanyName() {
      return Utils.getCompanyNameByGroupId(this.companyGroupId);
    },
    getGradeName() {
      return Utils.getGradeLevlByGradeId(this.positionGradeId);
    },

    _updateDirectLeaderByHalfYear() {
      if (this.disabledButton) {
        return;
      }
      let that = this;
      layer.confirm(
        `确认要职级评定发起吗?`,
        {
          title: "提示",
          btn: ["确定", "取消"], //按钮
        },
        (index) => {
          this.isLoading = true;
          let func = updateDirectLeaderByHalfYear;

          that.tableData.forEach((item) => {
            item.fdYear = that.year;
          });
          func(that.tableData, { positionGradeId: that.positionGradeId })
            .then((res) => {
              let result = res.success;
              layer.msg(`提交OA${result ? "成功" : "失败"}`, {
                icon: result ? 1 : 2,
                shade: 0.3,
                shadeClose: true,
              });
              that._findMergeListByCompanyGroupId();
              layer.close(index);
            })
            .catch((err) => {
              layer.msg(`${err.msg}`, {
                icon: 2,
                shade: 0.3,
                shadeClose: true,
              });
              layer.close(index);
            });
        }
      );
    },

    _findMergeListByCompanyGroupId() {
      /*先判断有权限才可以查询(modify by huangdh@2019-05-24)*/
      if (
        this.$store.state.doubleCol.arrAuths
          .point_ablity_findMergeListByCompanyGroupId
      ) {
        findMergeListByCompanyGroupId({
          CompanyGroupId: this.companyGroupId,
          Year: this.year,
          Month: this.month,
          PositionGradeId: this.positionGradeId,
          DeptArea: this.deptArea,
        })
          .then((res) => {
            this.isLoading = false;
            if (!res.success) return;
            this.originData = JSON.stringify(res.data ? res.data : []);
          })
          .catch((err) => {
            layer.msg(`${err.msg}`, { icon: 2, shade: 0.3, shadeClose: true });
          });
      } else {
        layer.msg(`对不起，您没有权限查询本界面.`, {
          icon: 2,
          shade: 0.3,
          shadeClose: true,
        });
      }
    },
    DateDiff(sDate1, sDate2) {
      //sDate1和sDate2是2002-12-18格式
      if (sDate1 == null || sDate2 == null) {
        return null;
      }
      let aDate, oDate1, oDate2, iDays;
      aDate = sDate1.split("-");
      oDate1 = new Date(aDate[1] + "-" + aDate[2] + "-" + aDate[0]); //转换为12-18-2002格式
      aDate = sDate2.split("-");
      oDate2 = new Date(aDate[1] + "-" + aDate[2] + "-" + aDate[0]);
      iDays = parseInt(Math.abs(oDate1 - oDate2) / 1000 / 60 / 60 / 24); //把相差的毫秒数转换为天数
      return iDays + 0;
    },

    showDescrDialog() {
      Utils.layerBox.layerDialogOpen({
        title: "评定人资格说明",
        area: ["530px", this.gradeRank == "grade" ? "320px" : "330px"],
        btn: ["关闭"],
        content: `<div style="padding:10px 20px;margin:0 auto;">
          ${
            this.gradeRank == "grade"
              ? `
          <p style="line-height:24px;">
            1、职级评定人为直接领导，系统会自动根据PS系统的汇报关系生产直接领导，可人工进行调整。<br/>
            2、直接领导入职不满3个月的原则上不能作为评定人，出现此情况的人资部需组织相关人员合议调整评定人。<br/>
            3、直接领导确定后点击“保存录入”，数据自动保存。<br/>
            4、在直接领导确定后，点击“触发OA职级评定”键，弹框提示“请确定职级评定人是否已确定完成并开始评定”，点击“确定”后，系统自动触发OA职级评定表给各评定人进行评定。<br/>
          </p>`
              : `<p>
             评定规则及顺序说明：<br/>
             1、评定规则：原则上由“评定人、审核人、审定人”3位领导进行评定，但为减轻高层领导的工作量，故当“评定人“为总助级领导时，无需填写“审核人和审定人”；当“审核人“为总助级领导时，无需填写“审定人”。<br/>
2、评定表传递顺序：<br/>
（1）第一层：先触发给只有“评定人”身份的人员。<br/>
（2）第二层：第一层人员评定结束后，再传递给同时拥有“评定人、审核人”身份的人员。<br/>
（3）第三层：第二层人员评定结束后，最终传递给同时拥有”评定人、审核人、审定人“身份的人员。
              <br/>
            </p>`
          }
          </div>`,
      });
    },
    initPage() {
      this.pageResize();
      const { companyGroupId, year, positionGradeId } = this.$route.params;
      this.companyGroupId = companyGroupId;
      this.year = year;
      this.month = this.$route.meta.month;
      this.positionGradeId = positionGradeId;
      // this.deptArea = "";
      this._findMergeListByCompanyGroupId();
    },
    pageResize() {
      this.body_height = $(window).height();
    },
  },
  mounted() {
    this.initPage();
    $(window).resize(this.pageResize);
  },
};
</script>

<style scoped>
.rank-setting-container .input-sm {
  height: 24px !important;
}
/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}
.list-year-data {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
.scroll-table-body {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
</style>
