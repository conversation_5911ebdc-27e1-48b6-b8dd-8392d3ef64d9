<template>
  <div class="assessor-detail-container">
    <div class="row">
      <div class="col-xs-12 text-left">
        <h4 class="col-xs-6">
          【{{ getCompanyName() }}】管培生综合能力评定得分明细表
        </h4>
        <div class="col-xs-6 text-right" style="line-height: 40px">
          <button class="btn btn-primary btn-xs" @click="showDescrDialog">
            权重说明
          </button>
          <button type="button" class="btn btn-primary btn-xs" @click="sheetIt">
            导出Excel
          </button>
          <button class="btn btn-primary btn-xs" @click="showDialog">
            成功要素标准查询
          </button>
          <button class="btn btn-primary btn-xs" @click="backPage()">
            {{ isview == 1 || isview == 2 ? "返回上级" : "返回目录" }}
          </button>
        </div>
      </div>
    </div>

    <div class="col-xs-12">
      <el-table
        id="table"
        :data="processedTableData"
        border
        :cell-style="{ 'text-align': 'center', padding: '4px 0' }"
        :header-cell-style="{ 'text-align': 'center', padding: '10px 0', height: '40px', lineHeight: '1.5' }"
        style="width: max-content"
        :max-height="body_height - 140 + 'px'"
        :span-method="objectSpanMethod"
        @cell-mouse-enter="onCellMouseEnter"
      >
        <!-- 序号列 -->
        <el-table-column label="序号" width="35" prop="index">
          <template slot-scope="scope">
            <template v-if="scope.row.isHeader">
              {{ scope.row.title }}
            </template>
            <template v-else>
              {{ scope.row.displayIndex || scope.row.index }}
            </template>
          </template>
        </el-table-column>

        <!-- 前9列使用统一格式化处理 -->
        <el-table-column label="届别" width="40" prop="graduateYear" :formatter="formatHeaderCell"></el-table-column>
        <el-table-column label="岗位级别" width="40" prop="schoolLevel" :formatter="formatSchoolLevel"></el-table-column>
        <el-table-column label="姓名" width="50" prop="userName" :formatter="formatHeaderCell"></el-table-column>
        <el-table-column label="性别" width="40" prop="gender" :formatter="formatHeaderCell"></el-table-column>
        <el-table-column label="学历" width="40" prop="degree" :formatter="formatHeaderCell"></el-table-column>
        <el-table-column label="部门" width="80" prop="deptName" :formatter="formatHeaderCell"></el-table-column>
        <el-table-column label="岗位" width="90" prop="jobName" :formatter="formatHeaderCell"></el-table-column>
        <el-table-column label="职务等级" width="80" prop="gradeName" :formatter="formatHeaderCell"></el-table-column>

        <!-- 评定人列 -->
        <el-table-column label="评定人" width="60" prop="raterName" :formatter="formatHeaderCell"></el-table-column>

        <!-- 评定方式列 -->
        <el-table-column label="评定方式" width="67" prop="judgeType" :formatter="formatHeaderCell"></el-table-column>

        <!-- 权重比例列 -->
        <el-table-column label="权重比例" width="45" prop="weightPoint" :formatter="formatWeightPoint"></el-table-column>

        <!-- 成功要素评定 -->
        <el-table-column label="成功要素评定" width="410">
          <el-table-column label="思维与行动" width="50" prop="mindAction" :formatter="formatHeaderCell"></el-table-column>
          <el-table-column label="洞察力" width="40" prop="sharpEye" :formatter="formatHeaderCell"></el-table-column>
          <el-table-column label="巧用专长" width="40" prop="mindSkill" :formatter="formatHeaderCell"></el-table-column>
          <el-table-column label="提高能力" width="40" prop="groupImprove" :formatter="formatHeaderCell"></el-table-column>
          <el-table-column label="纪律性" width="40" prop="sharpDiscipline" :formatter="formatHeaderCell"></el-table-column>
          <el-table-column label="领导力" width="40" prop="groupLeader" :formatter="formatHeaderCell"></el-table-column>
          <el-table-column label="创新与借鉴" width="50" prop="mindNew" :formatter="formatHeaderCell"></el-table-column>
          <el-table-column label="拥抱变化" width="40" prop="sharpEmbrace" :formatter="formatHeaderCell"></el-table-column>
          <el-table-column label="营造多样化" width="50" prop="groupCooperate" :formatter="formatHeaderCell"></el-table-column>
        </el-table-column>

        <!-- 能力得分列 -->
        <el-table-column label="能力得分" width="40" prop="avgPoint" :formatter="formatAvgPoint"></el-table-column>

        <!-- 能力等级列 -->
        <el-table-column label="能力等级" width="40" prop="ablityLevel" :formatter="formatHeaderCell"></el-table-column>

        <!-- 积极态度 -->
        <el-table-column label="积极态度">
          <el-table-column label="得分" width="40" prop="energyPoint" :formatter="formatHeaderCell"></el-table-column>
          <el-table-column label="结论" width="40" :formatter="formatEnergyConclusion"></el-table-column>
        </el-table-column>

        <!-- 稳定性 -->
        <el-table-column label="稳定性">
          <el-table-column label="得分" width="40" prop="stablityPoint" :formatter="formatHeaderCell"></el-table-column>
          <el-table-column label="结论" width="40" prop="stablity" :formatter="formatHeaderCell"></el-table-column>
        </el-table-column>

        <!-- 能力权重得分列 -->
        <el-table-column label="能力权重得分" width="40" prop="finalPoint" column-key="weightScoreColumn" :formatter="formatFinalPoint"></el-table-column>

        <!-- 评价明细列 -->
        <el-table-column label="评价明细" width="40" column-key="evaluationDetail">
          <template slot-scope="scope">
            <div v-if="!scope.row.isHeader" @click="toGoodAbsence(scope.row)" style="cursor: pointer;">
              <img src="../../../../assets/images/action_dtl.png" />
            </div>
          </template>
        </el-table-column>

        <!-- 工作总结列 -->
        <el-table-column label="工作总结" width="40" column-key="workSummary">
          <template slot-scope="scope">
            <div v-if="!scope.row.isHeader" @click="toGoodAbsence2(scope.row)" style="cursor: pointer;">
              <img src="../../../../assets/images/action_dtl.png" />
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div v-if="tableDataLength <= 0" class="text-center p-3">
          {{ tableDataLength == -1 ? "加载中..." : "暂无数据" }}
      </div>
    </div>
  </div>
</template>

<script>
import { findStudentPointListByCompanyGroupId } from "@/api";
import ScoreDescrDialog from "./ScoreDescrDialog";
export default {
  data() {
    return {
      tableData: [],
      year: "",
      month: "",
      body_height: 0,
      companyGroupId: "",
      isview: "",
      tableDataLength: -1,
    };
  },
  computed: {
    processedTableData() {
      // 处理表格数据，将嵌套结构转换为扁平结构，并添加标题行
      const result = [];

      if (!this.tableData || this.tableData.length === 0) return result;

      this.tableData.forEach((item, index) => {
        // 添加标题行
        result.push({
          isHeader: true,
          gradeName: item.gradeName,
          title: item.title || item.gradeName,
          headerIndex: index
        });

        // 添加学生数据
        if (item.list && item.list.length > 0) {
          let groupDisplayIndex = 1; // 每组学生从1开始计数

          item.list.forEach((student, studentIndex) => {
            // 对于每一对学生，只增加一次displayIndex
            const actualDisplayIndex = Math.floor((studentIndex) / 2) + 1;

            result.push({
              ...student,
              isStudent: true,
              index: studentIndex + 1,
              // 添加用于显示的连续序号，每组学生从1开始
              displayIndex: actualDisplayIndex
            });
          });
        }
      });

      return result;
    }
  },
  methods: {
    getLevelName(num) {
      return Utils.number2ChNum(num) + "级";
    },
    getCompanyName() {
      return Utils.getCompanyNameByGroupId(this.companyGroupId);
    },
    isPositionLevel(name) {
      if (!name) return false;
      return (
        name.includes("级") || name.includes("人") || name.includes("经理级")
      );
    },
    getUniqueCompanies(students) {
      if (!students || !students.length) return [];
      const companies = new Set();
      students.forEach((student) => {
        if (student.gradeName) companies.add(student.gradeName);
      });
      return Array.from(companies);
    },
    getStudentsByCompany(students, gradeName) {
      if (!students || !students.length) return [];
      return students.filter((student) => student.gradeName === gradeName);
    },
    isFirstPositionLevelForCompany(gradeName, currentIndex) {
      for (let i = 0; i < currentIndex; i++) {
        const item = this.tableData[i];
        if (this.isPositionLevel(item.gradeName)) {
          const companies = this.getUniqueCompanies(item.list);
          if (companies.includes(gradeName)) {
            return false;
          }
        } else if (item.gradeName === gradeName) {
          return false;
        }
      }
      return true;
    },
    getCompanyTotalRows(gradeName) {
      let totalRows = 0;
      this.tableData.forEach((item) => {
        if (this.isPositionLevel(item.gradeName)) {
          const students = this.getStudentsByCompany(item.list, gradeName);
          if (students.length > 0) {
            totalRows += 1 + students.length;
          }
        } else if (item.gradeName === gradeName) {
          totalRows += item.list ? item.list.length : 0;
        }
      });
      return totalRows;
    },
    _findStudentPointListByCompanyGroupId() {
      if (
        this.$store.state.doubleCol.arrAuths
          .point_studentPoint_findStudentPointListByCompanyGroupId
      ) {
        findStudentPointListByCompanyGroupId({
          companyGroupId: this.$route.params.companyGroupId,
          graduateYear: this.year - 1,
          year: this.year,
          month: this.month,
        }).then((res) => {
          this.tableData = res.data || [];
          this.tableDataLength = this.tableData.length;

          // 数据加载后处理表格显示
          this.$nextTick(() => {
            this.adjustTableDisplay();
            this.adjustMergedCellsStyle();
          });
        });
      } else {
        layer.msg(`对不起，您没有权限查询本界面.`, {
          icon: 2,
          shade: 0.3,
          shadeClose: true,
        });
      }
    },
    showDialog() {
      let layerDialog = Utils.layerBox.layerDialogOpen({
        title: `成功要素`,
        btn: [],
        maxmin: false,
        area: ["1200px", "660px"],
      });
      this.dialogComponent = Utils.layerBox.layerLoadVueComponent({
        layer: layerDialog,
        component: ScoreDescrDialog,
        methods: {
          doConfirm: (params, action) => {},
          doClose: () => {},
        },
        props: {},
      });
    },
    pageResize() {
      this.body_height = $(window).height();
    },
    resizeTable() {
      this.pageResize();
    },
    toFixed2(num) {
      return num == null ? null : ((num * 100) / 100).toFixed(1);
    },
    toFixed1(num) {
      return num == null ? null : (Math.floor(num * 10) / 10).toFixed(1);
    },
    sheetIt() {
      this.exportExcelOne.exportExcel(
        `【${this.getCompanyName()}】管培生综合能力评定得分明细表.xlsx`,
        "#table"
      );
    },
    toGoodAbsence(item) {
      const { companyGroupId, year, month } = this.$route.params;
      this.companyGroupId = companyGroupId;
      this.year = year;
      this.month = month;
      var ablityId = item.ablityId;
      var url =
        "/" +
        this.year +
        "/" +
        this.month +
        "/Trainee/Staff/" +
        this.companyGroupId +
        "/viewGoodAbsence";

      this.$router.push({
        path: url,
        query: {
          ablityId: ablityId,
          year: this.year,
          month: this.month,
          companyGroupId: this.companyGroupId,
        },
      });
    },
    toGoodAbsence2(item) {
      const { companyGroupId, year, month } = this.$route.params;
      this.companyGroupId = companyGroupId;
      this.year = year;
      this.month = month;
      let fdId = item.fdId;
      let name = item.userName;
      let url =
        "/" +
        this.year +
        "/" +
        this.month +
        "/trainee/assessor2/" +
        this.companyGroupId +
        "/summary";

      this.$router.push({
        path: url,
        query: {
          fdId: fdId,
          name: name,
          year: this.year,
          month: this.month,
          companyGroupId: this.companyGroupId,
        },
      });
    },
    showDescrDialog() {
      Utils.layerBox.layerDialogOpen({
        title: "<b>权重说明：</b>",
        area: ["620px", "350px"],
        btn: ["关闭"],
        content: `<div style="padding:10px 20px;margin:0 auto;">
          <p style="padding: 0 20px;">
              <strong>评定人权重说明：</strong><br>
                1、评定人数为5人的，间接领导权重为30%，直接领导权重为20%，人资领导权重为20%，业务关联领导均为15%；<br>
                2、评定人数为4人的，间接领导权重为40%，直接领导权重为30%，业务关联领导均为15%；<br>
                3、评定人数为3人的<br>
                （1）有间接领导的：间接领导权重为50%，直接领导和业务关联领导均为25%；<br>
                （2）没有间接领导的：直接领导权重为50%，2个关联业务领导各占25%；<br>
                4、评定人数为2人的<br>
                （1）有间接领导的：间接领导权重为60%，业务关联领导或直接领导为40%；<br>
                （2）没有间接领导的：直接领导权重为60%，业务关联领导为40%。 <br>
        </p></div>`,
      });
    },
    backPage() {
      if (this.isview == 1) {
        this.$router.push({
          path:
            `/` +
            this.year +
            `/` +
            this.month +
            `/trainee/evaluation/meetingResult`,
        });
      } else if (this.isview == 2) {
        this.$router.push({
          path:
            `/` +
            this.year +
            `/` +
            this.month +
            `/trainee/evaluation/` +
            this.companyGroupId +
            `/allJoint`,
        });
      } else {
        this.$router.push({
          path: `/` + this.year + `/` + this.month + `/menu/2`,
        });
      }
    },
    initPage() {
      this.resizeTable();
      const { companyGroupId, year, month } = this.$route.params;
      this.companyGroupId = companyGroupId;
      this.year = year;
      this.month = month;
      const { isview } = this.$route.query;
      this.isview = isview;

      this._findStudentPointListByCompanyGroupId();
    },
    objectSpanMethod(params) {
      const { row, column, rowIndex, columnIndex } = params;

      // 标题行只进行横向合并，不纵向合并
      if (row.isHeader) {
        if (columnIndex === 0) {
          // 使用实际的列数进行合并
          const tableColumns = document.querySelectorAll('.el-table__header-wrapper th').length;
          return {
            rowspan: 1,
            colspan: tableColumns || 26 // 如果无法获取实际列数，使用26作为备选
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }

      // 通过列索引判断是否是前9列
      const isFirstNineColumns = columnIndex < 9;

      // 确定哪些列需要合并
      // 确定表格的总列数
      const allColumns = document.querySelectorAll('.el-table__header-wrapper th').length;

      // 通过索引判断是否是最后3列
      // 使用保守的判断方式
      const isLastThreeColumns = (allColumns > 3 && columnIndex >= allColumns - 3) ||
                              // 使用columnKey作为备选判断方式
                              ['weightScoreColumn', 'evaluationDetail', 'workSummary'].includes(column.columnKey);

      // 处理需要合并的列
      if ((isFirstNineColumns || isLastThreeColumns) && !row.isHeader) {
        // 获取当前行所在的组内索引
        const groupIndex = this.getGroupIndex(row);
        if (groupIndex !== -1) {
          // 判断组内索引的奇偶性
          const isEvenIndex = groupIndex % 2 === 0;

          if (isEvenIndex) {
            // 检查下一行是否存在、不是标题行，且属于同一组
            const nextRowIndex = rowIndex + 1;
            if (nextRowIndex < this.processedTableData.length &&
                !this.processedTableData[nextRowIndex].isHeader &&
                this.getSameGroupRows(row).includes(this.processedTableData[nextRowIndex])) {
              return {
                rowspan: 2,
                colspan: 1
              };
            }
          } else {
            // 奇数索引行不显示
            return {
              rowspan: 0,
              colspan: 0
            };
          }
        }
      }

      // 其他单元格正常显示
      return {
        rowspan: 1,
        colspan: 1
      };
    },

    // 获取行在其分组内的索引
    getGroupIndex(row) {
      if (row.isHeader) return -1;

      // 找到当前行所属的组
      let headerIndex = -1;
      let groupRows = [];

      // 遍历找到当前行前面最近的标题行
      for (let i = 0; i < this.processedTableData.length; i++) {
        const currentRow = this.processedTableData[i];
        if (currentRow === row) {
          break;
        }
        if (currentRow.isHeader) {
          headerIndex = i;
          groupRows = [];
        } else {
          groupRows.push(currentRow);
        }
      }

      // 如果找到了标题行，计算当前行在组内的索引
      if (headerIndex !== -1) {
        return groupRows.length;
      }

      return -1;
    },

    // 获取与当前行属于同一组的所有行
    getSameGroupRows(row) {
      if (row.isHeader) return [];

      // 找到当前行所属的组
      let inCurrentGroup = false;
      let groupRows = [];

      for (let i = 0; i < this.processedTableData.length; i++) {
        const currentRow = this.processedTableData[i];

        if (currentRow.isHeader) {
          // 如果已经找到过当前行，说明已经超出了当前组
          if (inCurrentGroup) {
            break;
          }
          // 开始一个新组
          groupRows = [];
        } else {
          groupRows.push(currentRow);
        }

        // 标记找到当前行
        if (currentRow === row) {
          inCurrentGroup = true;
        }
      }

      return groupRows;
    },
    formatHeaderCell(row, column) {
      if (row.isHeader) {
        return "";
      }
      const value = row[column.property];
      return value || "";
    },
    formatSchoolLevel(row, column) {
      if (row.isHeader) {
        return "";
      }
      if (row.schoolLevel) {
        return this.getLevelName(row.schoolLevel);
      }
      return "";
    },
    formatWeightPoint(row, column) {
      if (row.isHeader) {
        return "";
      }
      if (row.weightPoint) {
        return (parseFloat(row.weightPoint) * 100).toFixed(1) + "%";
      }
      return "";
    },
    formatAvgPoint(row, column) {
      if (row.isHeader) {
        return "";
      }
      if (row.avgPoint != null) {
        return this.toFixed2(row.avgPoint);
      }
      return "";
    },
    formatEnergyConclusion(row, column) {
      if (row.isHeader) {
        return "";
      }
      if (row.energyPoint != null) {
        return row.energyPoint >= 70 ? "积极" : "不积极";
      }
      return "";
    },
    formatFinalPoint(row, column) {
      if (row.isHeader) {
        return "";
      }
      if (row.finalPoint != null) {
        return this.toFixed1(row.finalPoint);
      }
      return "";
    },
    // 添加一个专门的方法来处理表格显示
    adjustTableDisplay() {
      setTimeout(() => {
        // 获取所有行
        let rows = document.getElementsByClassName("el-table__row");

        this.processedTableData.forEach((item, index) => {
          if (item.isHeader && rows[index]) {
            // 标题行处理 - 设置样式
            if (rows[index].cells && rows[index].cells.length > 0) {
              const firstCell = rows[index].cells[0];

              // 设置单元格的背景色
              firstCell.style.background = "#eee";

              // 设置内部内容的样式 - 关键是这里需要操作children[0]
              if (firstCell.children && firstCell.children.length > 0) {
                firstCell.children[0].style.fontWeight = "bold";
                firstCell.children[0].style.textAlign = "left";
                firstCell.children[0].style.marginLeft = "10px";
              }

              // 确保标题行只显示标题内容
              if (firstCell.children && firstCell.children.length > 0) {
                firstCell.children[0].innerText = item.title || item.gradeName;
              } else {
                firstCell.textContent = item.title || item.gradeName;
              }

              // 尝试获取实际列数并设置colspan
              const tableColumns = document.querySelectorAll('.el-table__header-wrapper th').length;
              if (tableColumns && tableColumns > 0) {
                firstCell.colSpan = tableColumns;

                // 隐藏其他列
                for (let i = 1; i < rows[index].cells.length; i++) {
                  if (rows[index].cells[i]) {
                    rows[index].cells[i].style.display = 'none';
                  }
                }
              }
            }
          } else if (!item.isHeader && rows[index]) {
            // 非标题行处理 - 确保合并的单元格样式一致
            const cells = rows[index].cells;
            if (cells && cells.length > 0) {
              // 设置合并单元格的样式
              for (let i = 0; i < cells.length; i++) {
                // 如果是合并的单元格（有rowspan属性且值大于1）
                if (cells[i] && cells[i].rowSpan > 1) {
                  cells[i].style.verticalAlign = 'middle';
                }
              }
            }
          }
        });
      }, 500);
    },
    // 添加专门处理合并单元格样式的方法
    adjustMergedCellsStyle() {
      setTimeout(() => {
        // 获取总列数，用于标识
        const totalColumns = document.querySelectorAll('.el-table__header-wrapper th').length || 26;

        // 获取所有合并的单元格并设置垂直居中样式
        const rows = document.getElementsByClassName("el-table__row");
        for (let i = 0; i < rows.length; i++) {
          const cells = rows[i].cells;
          if (cells) {
            for (let j = 0; j < cells.length; j++) {
              // 删除所有调试标记和样式代码
              if (cells[j] && cells[j].rowSpan > 1) {
                cells[j].style.verticalAlign = 'middle';
                // 确保图标居中显示
                if (cells[j].querySelector('img')) {
                  cells[j].style.textAlign = 'center';
                }
              }
            }
          }
        }
      }, 300);
    },
    onCellMouseEnter() {
      // 当鼠标进入单元格时调整合并单元格样式
      this.adjustMergedCellsStyle();
    },
  },
  watch: {
    $route: function () {
      this.initPage();
    },
  },
  mounted() {
    window.addEventListener("resize", this.pageResize);
    this.pageResize();
    this.initPage();

    // 确保表格正确渲染后调整样式
    this.$nextTick(() => {
      this.adjustTableDisplay();
      this.adjustMergedCellsStyle();
    });
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.pageResize);
  },
};
</script>

<style scoped>
.assessor-detail-container {
  height: 100%;
  padding: 0 15px;
  overflow: hidden;
}

.el-table /deep/.el-table--border .el-table__cell {
  border-bottom: 1px solid #ebeef5 !important;
}

.el-table /deep/.el-table .cell {
  padding:2px !important;
  font-size: 13px;
  line-height: 1.3;
}
</style>

