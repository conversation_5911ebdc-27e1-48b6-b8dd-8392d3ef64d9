<template>
  <div class="ratio-scale-container">
      <div class="row" style="line-height:40px;">
        <div class="col-xs-12 text-left">
          <h4 class="col-xs-9" style="padding: 0;">【{{getCompanyName()}}】管理人员综合能力级别、职级及比例标准表</h4>
          <div class="col-xs-3 text-right" style="padding: 0;">
            <button class="btn btn-primary btn-xs" @click="dialogShow(true)"
            v-if="$store.state.doubleCol.arrAuths.base_baseLevel_updateAdjustRate" >级别职级比例录入调整</button>
            <!-- $router.push(`/${$route.params.year}/${$route.params.month}/menu/1`) -->
            <button class="btn btn-primary btn-xs" @click="$router.back()">返回目录</button>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-xs-12">
          <table class="table table-bordered">
            <thead>
              <tr v-for="(heads,index) in tableHead" :key="index">
                <th rowspan="2" v-if="index===0">序号</th>
                <th :colspan="index===0?3:1" v-for="(head,$index) in heads" :key="$index">{{head}}</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(data,$index) in tableData" :key="$index">
                <td v-if="data.start" :rowspan="data.rowspan">{{data.order}}</td>
                <td v-if="data.start || (prop_index+1)%3 == 2" :rowspan="(prop_index+1)%3 != 2 ? data.rowspan : 1" v-for="(prop,prop_index) in tableProps" :key="prop_index" >
                  {{$index===0
                  ?((prop_index+1)%3 === 1?data[`level_${prop}`]:(prop_index+1)%3 === 2?data[`levelgrade_${prop}`]:(prop_index+1)%3 === 0? (data[`rate_${prop}`]||'-'): '')
                   : ((prop_index+1)%3 === 1?data[`level_${prop}`]:(prop_index+1)%3 === 2?data[`levelgrade_${prop}`]:(prop_index+1)%3 === 0? (data[`rate_${prop}`]+(data[`rate_${prop}`]>0?'%':'')): '')
                  }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
  </div>
</template>

<script>
import { findBaseLevelRateListByCompanyGroupId,updateAdjustRate,findListByPositionGradeId } from '@/api'
export default {
  data(){
    return {
      isLoading:true,
      tableData:[],
      tableHead:[],
      tableProps:[],
      gradeList:[],
      detailList:[],
      year:"",
      month:"",
      companyGroupId:"",
    }
  },
  methods:{
    getCompanyName(){
      return Utils.getCompanyNameByGroupId(this.companyGroupId);
    },
    _updateBatch(list=[]){
      if(this.isLoading){
        return;
      }
      let sum_rate = 0;
      try{
        list.forEach(item=>{ 
          if(item.rate){
            sum_rate += parseInt(item.rate);
          }
        });
        if(sum_rate!=100){
          layer.msg('所有比例相加必须等于100%');
          return false;
        }
      }catch(err){
        layer.msg('请输入整数');
        return false;
      }
      this.isLoading = true;
      updateAdjustRate(list).then(res =>{
        let result = res.success;
        layer.msg(`编辑${result ? '成功' : '失败'}`, {icon: result ? 1 : 2,shade:0.3,shadeClose:true});
        this._findBaseLevelRateListByCompanyGroupId();
      })
    },
    dialogShow(){
      let that = this;
      Utils.layerBox.layerDialogOpen({
          area:['400px','300px'],
          content:`<div style="width:100%;height:100%;text-align:center;line-height:32px;box-sizing: border-box;padding: 12px 0">
            <div class="row" style="margin:0">
              <label class="col-xs-4 text-right">职等选择：</label>
              <div class="col-xs-8 text-left"><select class="input-sm" id="positionGrade" style="width:160px;">
                  ${this.gradeList.map((item,grade_index)=> `<option value="${item.gradeCode}" ${grade_index == 0 ? 'selected' : ''}>${item.gradeName}</option>`).join("\n")}
                </select></div>
            </div>
            <div style="margin:0;line-height: 20px;margin-top:10px;" id="level_wrap">
            </div>
          </div>`,
          btn1:function(index){
            if(that.detailList){
              let rateArr = [];
              that.detailList.forEach(item=>{
                rateArr.push({
                  fdId:item.fdId,
                  rate:$("#"+item.fdId).val()||''
                })
              })
              let result = rateArr ? that._updateBatch(rateArr) : null;
              if(result === false){return}
            }
            layer.close(index)
          },
          success:function(layero,index){
            let appendOption = function(e){
              let gradeIdEl = e ? e.target : $("#positionGrade")[0];
              findListByPositionGradeId({CompanyGroupId:that.companyGroupId,Year:that.year,Month:that.month,PositionGradeId:$(gradeIdEl).val()}).then(res=>{
                  $("#level_wrap").empty();
                  that.detailList = res.data || [];
                  if(that.detailList&&that.detailList.length!=0){
                      that.detailList.forEach(detail=>{
                        if(detail.level!='优'){
                          $("#level_wrap").append(`<div class="row" style="margin:0;line-height: 20px;margin-top:10px;">
                              <label class="col-xs-4 text-right">${detail.level}级别：</label>
                              <div class="col-xs-8 text-left">
                                <input type="text" id="${detail.fdId}" value="${detail.rate||''}" class="form-control input-sm" style="width:160px;display:inline-block;"/>&nbsp;(%)
                              </div>
                            </div>`);
                        }
                    })
                  }else{
                    $("#level_wrap").append(`<div class="row" style="margin:0;margin-top: 10px;height: 140px;line-height: 120px;color: #d82d2d;">没有这个职等的级别信息</div>`);
                  }
              })
              
            }
            $("#positionGrade").off('change').on("change",appendOption);
            appendOption();
          }
        });
    },
    _findBaseLevelRateListByCompanyGroupId(){
      /*先判断有权限才可以查询(modify by huangdh@2019-05-24)*/
      if (this.$store.state.doubleCol.arrAuths.base_baseLevel_findBaseLevelRateListByCompanyGroupId)
      {
          findBaseLevelRateListByCompanyGroupId({CompanyGroupId:this.companyGroupId,Year:this.year,Month:this.month}).then(res=>{
            this.isLoading = false;
            if(!res.success) return;
            this.gradeList = [];
            this.tableHead = [];
            let sort_arr = res.data || []; 
            let table_props = [],
                prop_arr = res.code ? res.code.split("|") : [],
                first_row_head = res.msg ? res.msg.split("|") : [];
            prop_arr.forEach((prop,index)=>{
              this.gradeList.push({gradeCode:prop,gradeName:first_row_head[index]});
              for(let i=0 ;i<3; i++){
                table_props.push(prop);
              }
            })
            this.tableProps = table_props;
            sort_arr.sort((a,b)=>{
              return a.level>b.level;
            })
            if(sort_arr[sort_arr.length-1]&&sort_arr[sort_arr.length-1].level === '优'){
              sort_arr.unshift(sort_arr.pop())
            }
            this.tableData = sort_arr;
            this._convert_header(first_row_head);
            this._eachData(this.tableData)
          })
      }
      else {
          layer.msg(`对不起，您没有权限查询本界面.`, {icon: 2,shade:0.3,shadeClose:true});
      }
    },
    _convert_header(first_row_head){
      let second_row_head = [];
      first_row_head.forEach((item)=>{
        second_row_head.push("级别");
        second_row_head.push("职级");
        second_row_head.push("比例");
      });
      this.tableHead.push(first_row_head);
      this.tableHead.push(second_row_head);
    },
    _eachData(data){
      let start = 0, end = 0, start_level = 0, end_level = 0;
      data.forEach((d,index) =>{
        if(index === 0 ){
          d.order = 1;
          d.start = true;
          start = 0;
          d.start_level = true;
          start_level = 0;

        }else{
          if(d.level != data[start_level].level){
              end_level = index;
              d.start_level = true;
              data[start_level].rowspan_level = end_level - start_level;
              start_level = index;
          }else {
            if(data.length === index + 1){
              data[start_level].rowspan_level = index + 1 - start_level;
            }
          }
          if(d.level == data[start].level){
            d.order = data[index-1].order;
            if(data.length === index + 1){
              data[start].rowspan = index + 1 - start;//因为索引从0开始，所以index+1
            }
          }else{
            d.start = true;
            d.order = data[index-1].order+1;
            end = index;
            data[start].rowspan = end - start;
            start = index;
          }
        }
      })
    },
    initPage(){
      const {companyGroupId,year,month} = this.$route.params;
      this.companyGroupId = companyGroupId;
      this.year = year;
      this.month = month;
      this._findBaseLevelRateListByCompanyGroupId();
    }
  },
  watch:{
    "$route":function(){
      this.initPage();
    }
  },
  created(){
    this.initPage();
  }
}
</script>

<style>

</style>
