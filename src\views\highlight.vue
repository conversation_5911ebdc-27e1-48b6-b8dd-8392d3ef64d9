<template>
  <div class="assessor-detail-container">
    <div class="row">
      <div class="col-xs-12 text-left" style="width: 1020px">
        <h4 v-if="$route.query.type"  class="col-xs-6">{{ year }}年度全集团高级经理职等以上【工作亮点】表</h4>
        <h4 v-else class="col-xs-6">{{ title }}</h4>
        <div class="col-xs-6 text-right" style="line-height: 40px">
          <button type="button" class="btn btn-primary btn-xs" @click="myPrint">
            导出Excel
          </button>
          <button
            :disabled="status == 1"
            class="btn btn-primary btn-xs"
            @click="_sendOa"
          >
            提交OA
          </button>
          <button class="btn btn-primary btn-xs" @click="back">返回目录</button>
        </div>
      </div>
    </div>

    <div class="col-xs-12">
      <el-table
        id="table"
        :data="tableData"
        border
        :cell-style="{ 'text-align': 'center', padding: '4px 0' }"
        style="width: max-content"
        :max-height="body_height - 140 + 'px'"
      >
        <el-table-column label="序号" width="45">
          <template slot-scope="scope">
            {{ scope.row.sort }}
          </template>
        </el-table-column>
        <el-table-column width="560" label="评定对象">
          <el-table-column
            prop="deptName"
            label="部门"
            width="100"
          ></el-table-column>
          <el-table-column
            prop="userName"
            label="姓名"
            width="65"
          ></el-table-column>
          <el-table-column prop="sex" label="性别" width="65"></el-table-column>
          <el-table-column prop="age" label="年龄" width="65"></el-table-column>
          <el-table-column
            prop="degree"
            label="学历"
            width="65"
          ></el-table-column>
          <el-table-column
            prop="jobName"
            label="岗位"
            width="150"
          ></el-table-column>
          <el-table-column
            prop="ablityGrade"
            label="职等"
            width="120"
          ></el-table-column>
          <el-table-column
            prop="hwAge"
            label="华劲工龄"
            width="90"
          ></el-table-column>
          <el-table-column
            prop="gradeAge"
            label="职等工龄"
            width="90"
          ></el-table-column>
        </el-table-column>

        <el-table-column label="工作亮点" width="65">
          <template slot-scope="scope">
            <el-button
              @click="
                $router.push(
                  'summary?fdId=' +
                    scope.row.fdId +
                    '&name=' +
                    scope.row.userName
                )
              "
              type="text"
              size="small"
              >查看</el-button
            >
          </template>
        </el-table-column>
        <el-table-column
          prop="statusText"
          label="提交状态"
          width="70"
        ></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { findList, updateNotCommitSave, sendOa, exportData } from "@/api";
export default {
  data() {
    return {
      isLoading: true,
      body_height: 0,
      status: "",
      tableData: [],
      companyGroupId: "",
      judgeTypes: ["综合分", "成功要素"],
      year: "",
      month: "",
      title: "",
    };
  },
  computed: {
    disabledButton() {
      /*提交OA*/
      if (this.isLoading) {
        return true;
      } else if (this.tableData == null || this.tableData.length == 0) {
        return true;
      } else if (this.tableData && this.tableData.length != 0) {
        //return this.tableData[0].confirmLeaderId ? true : false;
        return this.tableData[0].confirmLeaderStatus > 10 ? true : false;
      }
      return false;
    },
  },
  methods: {
    sheetIt() {
      this.exportExcelOne.exportExcel(`${this.title}.xlsx`, "#table");
    },

    myPrint() {
      exportData({
        companyGroupId: this.companyGroupId,
        year: this.year,
        month: this.month,
        userType: "管理人员",
      })
        .then((res) => {
          // let result = res.success;
          // layer.msg(`导出${result ? '成功' : '失败'}`, { icon: result ? 1 : 2, shade: 0.3, shadeClose: true });
          const result = res.data;
          let raw = window.atob(result);
          let uInt8Array = new Uint8Array(result.length);
          for (let i = 0; i < raw.length; i++) {
            uInt8Array[i] = raw.charCodeAt(i);
          }
          const link = document.createElement("a");
          const blob = new Blob([uInt8Array], {
            type: "application/vnd.ms-excel",
          });

          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          link.setAttribute("download", this.title + ".xlsx");
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          // layer.close(index)
        })
        .catch((err) => {
          // layer.msg(`${err.msg}`, { icon: 2, shade: 0.3, shadeClose: true });
        });
    },

    getSchoolLevel(level) {
      return Utils.number2ChNum(level) + "级";
    },
    getCompanyName() {
      return Utils.getCompanyNameByGroupId(this.companyGroupId);
    },
    back() {
      this.$router.replace(
        `/${this.$route.params.year}/${this.$route.params.month}/menu/1`
      );
    },
    _sendOa() {
      if (this.disabledButton) {
        return;
      }
      layer.confirm(
        "是否确定提交OA？",
        {
          title: "提示",
          btn: ["确定", "取消"], //按钮
        },
        (index) => {
          this.isLoading = true;
          sendOa({
            companyGroupId: this.companyGroupId,
            year: this.year,
            month: this.month,
            userType: "管理人员",
          })
            .then((res) => {
              let result = res.success;
              layer.msg(`提交OA${result ? "成功" : "失败"}`, {
                icon: result ? 1 : 2,
                shade: 0.3,
                shadeClose: true,
              });
              this._findList();
              layer.close(index);
            })
            .catch((err) => {
              layer.msg(`${err.msg}`, {
                icon: 2,
                shade: 0.3,
                shadeClose: true,
              });
            });
        }
      );
    },

    _findList() {
      /*先判断有权限才可以查询(modify by huangdh@2019-05-24)*/
      // if (this.$store.state.doubleCol.arrAuths.point_ablity_findList) {
      findList({
        companyGroupId: this.$route.query.type ? "0" : this.companyGroupId,
        year: this.year,
        month: this.month,
        userType: "管理人员",
      }).then((res) => {
        this.isLoading = false;
        this.tableData = res.data || [];
        this.status = this.tableData[1].status;
        setTimeout(() => {
          let row = document.getElementsByClassName("el-table__row");
          this.tableData.forEach((item, index) => {
            if (!item.sort) {
              row[index].cells[0].colSpan = "12";
              row[index].cells[0].children[0].style =
                "font-weight: bold;text-align: left;margin-left: 10px";
              row[index].cells[0].style = "background:#eee";
              row[index].cells[0].children[0].innerText = item.ablityGrade;
              for (let i = 1; i < row[index].cells.length; i++) {
                row[index].cells[i].style = "display: none";
              }
            }
          });
        });
      });
      // }
      // else {
      //   layer.msg(`对不起，您没有权限查询本界面.`, { icon: 2, shade: 0.3, shadeClose: true });
      // }
    },
    showDescrDialog() {
      Utils.layerBox.layerDialogOpen({
        title: "<b>评定人说明：</b>",
        area: ["850px", "540px"],
        btn: ["关闭"],
        content: `<div style="padding:10px 20px;margin:0 auto;">
            <p style="padding: 0 20px;">
                <strong>1、评定人资格：</strong><br>
                （1）关联业务领导：指的是与评定对象有关联业务接触的部门领导。<br>
                （2）直接领导：指的是评定对象的部门主管，直接汇报上级。<br>
                （3）间接领导：指的是评定对象的体系领导。<br>
                （4）人资领导：指的是了解人资对象的人资体系领导。评定人数少于5人时，则不选。<br>
                <strong>2、评定人人数及权重设置</strong><br>
                （1）评定人人数：2-5人，不允许1个人进行评定。<br>
                （2）评定人数为5人的，间接领导权重为30%，直接领导权重为20%，人资领导权重为20%，业务关联领导均为15%；<br>
                （3）评定人数为4人的，间接领导权重为40%，直接领导权重为30%，业务关联领导均为15%；<br>
                （4）评定人数为3人的<br>
                ①有间接领导的：间接领导权重为50%，直接领导和业务关联领导均为25%；<br>
                ②没有间接领导的：直接领导权重为50%，2个关联业务领导各占25%； <br>
                （5）评定人数为2人的 <br>
                ①有间接领导的：间接领导权重为60%，业务关联领导或直接领导为40%； <br>
                ②没有间接领导的：直接领导权重为60%，业务关联领导为40%。 <br>
                <strong>3、评定方式确定</strong><br>
                （1）成功要素：指的是运用成功要素（九大要素）对评定对象进行逐项评定，原则上熟悉成功要素的评定人采用“成功要素”进行评定。<br>
                （2）综合分：指的是根据日常工作中评定对象的工作表现，直接用综合分进行评定。评定人属于首次接触成功要素，尚不能完全掌握其核心要点，采用“综合分”进行评定。<br>
                        
          </p></div>`,
      });
    },
    initPage() {
      this.pageResize();
      const { companyGroupId, year, month } = this.$route.params;
      this.companyGroupId = companyGroupId;
      this.year = year;
      this.month = month;
      this._findList();
      this.title =
        year +
        (month == `6` ? `半` : ``) +
        "年度" +
        this.getCompanyName() +
        this.$route.name;
    },
    pageResize() {
      this.body_height = $(window).height();
    },
  },
  watch: {
    $route: function () {
      this.initPage();
    },
  },
  mounted() {
    this.initPage();
    $(window).resize(this.pageResize);
  },
};
</script>

<style scoped>
.el-table /deep/.el-table--border .el-table__cell {
  border-bottom: 1px solid #ebeef5 !important;
}

.el-table /deep/.el-table .cell {
  padding: 0 !important;
  font-size: 13px;
}
</style>
