<template>
  <div class="grade-evaluation-dialog-container">
      <div class="row">
        <div class="col-xs-12">
          <table class="table table-bordered">
            <thead>
              <tr>
                <th colspan="4" >评定对象</th>
                <th colspan="3" >审批结果</th>
              </tr>
              <tr>
                <th style="width:90px;">姓名</th>
                <th style="width:120px;">厂/部</th>
                <th >岗位</th>
                <th style="width:90px;">职等</th>
                <th style="width:80px;">调整前后</th>
                <th style="width:90px;">级别</th>
                <th style="width:90px;">职级</th>
              </tr>
            </thead>
            <tbody>
              <tr >
                <td rowspan="2">{{tableData.userName}} </td>
                <td rowspan="2">{{tableData.deptName}}</td>
                <td rowspan="2">{{tableData.jobName}}</td>
                <td rowspan="2">{{tableData.gradeName}} </td>
                <td >调整前</td>
                <td >{{tableData.adjustLevel}} </td>
                <td >{{tableData.adjustResult}} </td>
              </tr>
              <tr>
                <td >调整后</td>
                <td > 
                    <select style="width:80px;height:24px;">
                        <option :value="优">优</option>
                        <option value="A">A</option>
                        <option value="B">B</option>
                        <option value="C">C</option>
                    </select>
                </td>
                <td > 
                    <select style="width:80px;height:24px;">
                        <option :value="请选择">请选择</option>

                    </select>
                </td>
              </tr>
            </tbody>
          </table>
          <div style="text-align:center">
            <button class="btn btn-primary btn-xs" >
              确定
            </button>
             <button class="btn btn-primary btn-xs" >
              取消
            </button>

          </div>
        </div>
      </div>
  </div>
</template>

<script>
import { findOne } from "@/api";
export default {
  props: {
      params:{
          type:Object,
          required:true
      }
  },
  data() {
    return {
        body_height: 0,
        fdYear: "",
        fdMonth: "", 
        tableData: ""
    };
  },
  methods: {
    findObject() {
        findOne({
          fdId:this.params.ablityId
        }).then(res => {
          if (!res.success) return;
          this.tableData = res.data ? res.data : [];
        }).catch(err=>{
          layer.msg(`${err.msg}`, {icon: 2,shade:0.3,shadeClose:true});
        });
    }
  },
  created() {
    this.findObject();
  }
};
</script>

<style lang="scss">
.grade-evaluation-dialog-container {
  padding: 20px 0;
  .row {
    margin: 0;
    padding-left: 0;
    padding-right: 0;
    box-sizing: border-box;
  }
  .input-sm {
    height: 28px;
    padding: 5px 10px 5px 5px;
  }
  div.scroll-table {
    width: 100%;
    height: 320px;
    display: inline-block;
    float: left;
    background: #f4fafb;
    table.scroll-table-header {
      width: 100%;
      font-size: 13px;
      // display:block;
      // padding-right:17px;
    }
    table.scroll-table-body {
    }
  }
  table thead {
    tr {
      th {
        text-align: center;
        border-bottom: none;
        padding: 2px 5px;
        vertical-align: middle;
        background: #b7d8dc;
        height: 32px;
        font-size: 12px;
        color: #333;
        & > * {
          background: #b7d8dc !important;
          outline: none;
        }
      }
    }
    &.hide-thead-th > tr > th {
      height: 0;
      padding: 0;
      background: transparent;
      border: none;
    }
  }
  .table tbody tr {
    td {
      padding: 2px 5px;
      vertical-align: middle;
      height: 27px;
      font-size: 12px;
      text-align: center;
      .btn {
        padding-top: 0;
        padding-bottom: 0;
      }
    }
    &:nth-child(odd) {
      background: #fff;
    }
    &:nth-child(even) {
      background: #f9f9f9;
    }
  }
}
</style>
