<template>
  <div class="grade-evaluation-dialog-container">
      <div class="row">
        <div style="font-size:20px;font-weight:bold;">
            {{this.tableData.userName}}{{this.tableData.fdMonth==6?((parseInt(this.tableData.fdYear)-1)+"-"+this.tableData.fdYear+"年度"):(this.tableData.fdYear+"-"+(parseInt(this.tableData.fdYear)+1)+"半年度")}}管培生综合能力评定结果谈话记录表
        </div>
        <div class="col-xs-12">
            <div style="float:right;margin-right:250px;margin-top:10px;margin-bottom:5px;">
               <button type="button" class="btn btn-primary btn-xs" @click="$router.go(-1)"> 返回上级</button>
           </div><br>
            <table style="margin-top:30px;margin-bottom:10px;margin-left:auto;margin-right:auto;width:1100px;" >
              <tr >
                <td class="bcol1" >谈话对象：</td>
                <td class="bcol2">{{this.tableData.userName}}</td>
                <td class="bcol1">部门：</td>
                <td class="bcol2">{{this.tableData.deptName}}</td>
                <td class="bcol1">职务：</td>
                <td class="bcol2">{{this.tableData.jobName}}</td>
              </tr>
              <tr >
                <td class="bcol1">谈话人：</td>
                <td class="bcol2">{{this.tableData.adjustUserName==null?this.tableData.talkUserName : this.tableData.adjustUserName}}</td>
                <td class="bcol1">部门：</td>
                <td class="bcol2">{{this.tableData.adjustDeptName==null?this.tableData.talkDeptName : this.tableData.adjustDeptName}}</td>
                <td class="bcol1">职务：</td>
                <td class="bcol2">{{this.tableData.adjustPositionName==null?this.tableData.talkPositionName : this.tableData.adjustPositionName}}</td>
              </tr>
              <tr >
                <td class="bcol1" >能力等级：</td>
                <td class="bcol2">
                    {{this.tableData.talkLevel}}
                </td>
                <td class="bcol1" >留任结论：</td>
                <td class="bcol2">
                    {{this.tableData.talkResult}}
                </td>
                <td colspan="2" >
                    
                </td>
              </tr>
          </table>

          <table class="table table-bordered" style="width:1000px;margin:auto;">
            <thead>
              <tr>
                <th style="width:60px;">序号</th>
                <th style="width:160px;">项目</th>
                <th >内容</th>
              </tr>
            </thead>
            <tbody>
              <tr >
                <td>1</td>
                <td>优点</td>
                <td style="white-space: pre-line;text-align:left;">
                  {{this.tableData.good}}
                </td>
              </tr>
              <tr >
                <td>2</td>
                <td>不足</td>
                <td style="white-space: pre-line;text-align:left;">
                  {{this.tableData.absence}}
                </td>
              </tr>
              <tr >
                <td>3</td>
                <td>下一年度改善与提升计划</td>
                <td style="white-space: pre-line;text-align:left;">
                  {{this.tableData.workTarget}}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
  </div>
</template>

<script>
import { findAblityTalk } from "@/api";

export default {
  data() {
    return {
      body_height: 0,
      tableData: {},
    };
  }, 

  methods: {
    _findAblityTalk() {      
        findAblityTalk({
            fdId:this.$route.query.fdId
            }).then(res => {
                if (!res.success) return;
                this.tableData = res.data || {};             
            }).catch(err=>{
                layer.msg(`${err.msg}`, {icon: 2,shade:0.3,shadeClose:true});
            });
        },        
    },
    
    initPage() {
        this.pageResize();  
     },
    pageResize() {
        this.body_height = $(window).height();
    },
    created() {
        this._findAblityTalk();
    }
};

</script>

<style>
    .bcol1{font-weight: bold;width:150px;text-align:right;height: 26px;}
    .bcol2{width:200px;text-align:left;padding-left:10px;}
</style>

