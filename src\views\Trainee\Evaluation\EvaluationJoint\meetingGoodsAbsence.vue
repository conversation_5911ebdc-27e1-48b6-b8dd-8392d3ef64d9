<template>
  <div class="grade-evaluation-dialog-container">
      <div class="row">
        <div style="font-size:20px;font-weight:bold;">
          {{tableData.companyName}}（{{tableData.userName}}）综合能力评定【综合评价】</div>
        <div class="col-xs-12">
          <div style="float:left;margin-right:30px;margin-top:10px;">
              <span style="margin-left:30px;font-weight:bold;">姓名：</span>{{tableData.userName}}
              <span style="margin-left:30px;font-weight:bold;">部门：</span>{{tableData.deptName}}
              <span style="margin-left:30px;font-weight:bold;">职务：</span>{{tableData.jobName}}
              <!-- <span style="margin-left:30px;font-weight:bold;">职等：</span>{{tableData.gradeName}}               -->
           </div>
           <div style="float:right;margin-right:30px;margin-top:10px;margin-bottom:10px;">
              <button type="button" class="btn btn-primary btn-xs" @click="showDescrDialog">填写说明</button>
              <button type="button" class="btn btn-primary btn-xs" @click="_updateMeetingText2()" :disabled="disabledButton">保存</button>
              <button type="button" class="btn btn-primary btn-xs" @click="backPage()">返回上级</button>
           </div>
          <table class="table table-bordered">
            <thead>
              <tr>
                <th style="width:60px;">序号</th>
                <th style="width:150px;">评价项目</th>
                <th >综合评价</th>
              </tr>
            </thead>
            <tbody>
              <tr>         
                <td>1</td>
                <td>积极态度</td>
                <td style="text-align:left;">
                  <textarea ref="txt_totalEnergy" style="width:100%;height:100px;"/>
                </td>
              </tr>
              <tr>         
                <td>2</td>
                <td>稳定性</td>
                <td style="text-align:left;">
                  <textarea ref="txt_totalStablity" style="width:100%;height:100px;"/>
                </td>
              </tr>
              <tr>         
                <td>3</td>
                <td>优点</td>
                <td style="text-align:left;">
                  <textarea ref="txt_totalGoods" style="width:100%;height:100px;"/>
                </td>
              </tr>
              <tr>         
                <td>4</td>
                <td>不足</td>
                <td style="text-align:left;">
                  <textarea ref="txt_totalAbsence" style="width:100%;height:100px;"/>
                </td>
              </tr>

            </tbody>
          </table>
        </div>
      </div>
  </div>
</template>

<script>
import { findOne,updateMeetingText,updateMeetingText2 } from "@/api";
export default {
  data() {
    return {
        body_height: 0,
        year: "",
        month: "", 
        companyGroupId: "",
        isadd:"",
        companyGroupName: "",
        tableData: "",
        ablityPoint: "",
        userName:""
    };
  },  
    computed:{
        disabledButton(){
            if(this.isLoading){
                return true;
            }
            else if(this.tableData==null || this.tableData.length==0){
                return true;
            }
            else if(this.tableData){
                let isadd=this.$route.query.isadd;
                if (isadd==1){
                  return this.tableData.status !== 10 ? true : false;
                }
                else {
                  return true;
                }                
            }
            return false;
        }
    },
    methods: {
        findObject() {
            findOne({
            fdId:this.$route.query.ablityId
            }).then(res => {
            if (!res.success) return;
            this.tableData = res.data ? res.data : [];
            this.$refs["txt_totalEnergy"].value =this.tableData.totalEnergy||"";
            this.$refs["txt_totalStablity"].value =this.tableData.totalStablity||"";
            this.$refs["txt_totalGoods"].value =this.tableData.totalGoods||"";
            this.$refs["txt_totalAbsence"].value =this.tableData.totalAbsence||"";
            }).catch(err=>{
            layer.msg(`${err.msg}`, {icon: 2,shade:0.3,shadeClose:true});
            });
    },
    // _updateMeetingText(){
    //   this.isLoading = true;
    //   let fdId=this.$route.query.ablityId;
    //   let totalEnergy=this.$refs["txt_totalEnergy"].value;
    //   let totalStablity=this.$refs["txt_totalStablity"].value;
    //   let totalGoods=this.$refs["txt_totalGoods"].value;
    //   let totalAbsence=this.$refs["txt_totalAbsence"].value;
    //   updateMeetingText({fdId:fdId,totalEnergy:totalEnergy,totalStablity:totalStablity,
    //     totalGoods:totalGoods,totalAbsence:totalAbsence}).then(res=>{
    //     let result = res.success;
    //     layer.msg(`保存${result ? '成功' : '失败'}`, {icon: result ? 1 : 2,shade:0.3,shadeClose:true});
    //     if(result){
    //       this.findObject();
    //       //layer.close(layerindex);
    //     }
    //   }).catch(err=>{
    //     layer.msg(`${err.msg}`, {icon: 2,shade:0.3,shadeClose:true});
    //     //layer.close(layerindex);
    //   })
    // },
    _updateMeetingText2(){
      this.isLoading = true;
      let fdId=this.$route.query.ablityId;
      let totalEnergy=this.$refs["txt_totalEnergy"].value;
      let totalStablity=this.$refs["txt_totalStablity"].value;
      let totalGoods=this.$refs["txt_totalGoods"].value;
      let totalAbsence=this.$refs["txt_totalAbsence"].value;
      let params={
        fdId:this.$route.query.ablityId,
        totalEnergy:this.$refs["txt_totalEnergy"].value,
        totalStablity:this.$refs["txt_totalStablity"].value,
        totalGoods:this.$refs["txt_totalGoods"].value,
        totalAbsence:this.$refs["txt_totalAbsence"].value
      }
      updateMeetingText2(params).then(res=>{
        let result = res.success;
        layer.msg(`保存${result ? '成功' : '失败'}`, {icon: result ? 1 : 2,shade:0.3,shadeClose:true});
        if(result){
          this.findObject();
          //layer.close(layerindex);
        }
      }).catch(err=>{
        layer.msg(`${err.msg}`, {icon: 2,shade:0.3,shadeClose:true});
        //layer.close(layerindex);
      })
    },
    showDescrDialog(){
      Utils.layerBox.layerDialogOpen({
          title:'填写说明',
          area:['600px','300px'],
          btn:['关闭'],
          content: `<div style="padding:10px 20px;margin:0 auto;">
          <p style="line-height:24px;">
            1、积极态度：需描述管培生在积极态度方面的整体表现，若是评定人评定为不积极或者合议后认定为不积极，必须写明原因；<br/>
            2、稳定性：需描述管培生的婚恋情况、职业发展、工作安排认可度以及企业文化认可度等可证明稳定性的内容，若是评定人评定为不稳定或者合议后认定为不稳定，必须写明原因；<br/>
            3、优点、不足：结合评定人的评价以及人资访谈情况进行综合描述，每一条优点或者不足都必须附上相应事例。<br/>
          </p></div>`});
    },
      backPage(){
        const {companyGroupId,year, month, isadd} = this.$route.query;
        this.isadd = isadd;
        this.year = year;
        this.month = month;
        this.companyGroupId = companyGroupId;
        if (this.isadd==1){  /*来自新增*/ 
          this.$router.push({
            path: '/'+this.year+'/'+this.month+'/trainee/evaluation/'+this.companyGroupId+'/allJoint'
          });
        }
        else {  /*来自查看*/ 
          this.$router.push({
            path: '/'+this.year+'/'+this.month+'/trainee/evaluation/meetingResult'
          });
        }        
      },
      initPage() {
        this.pageResize();
        const {companyGroupId,year, month, isadd} = this.$route.query;
        this.isadd=isadd;
        this.year = year;
        this.month = month;
        this.companyGroupId = companyGroupId;
      },
      pageResize() {
          this.body_height = $(window).height();
      },
    },
    
    created() {
        this.findObject();
    }
};
</script>

<style>
    div.divItem{width:360px;float:left;border:1px solid white; }
    div.fss{color:blue;font-size:14px;}
    span.spanItem{color:blue;cursor:pointer;}    
</style>