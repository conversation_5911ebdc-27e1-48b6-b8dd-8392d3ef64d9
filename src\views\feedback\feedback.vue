<template>
  <div class="years-result-summary-container">
    <div class="row" style="max-width: 1335px;">
      <div class="col-xs-12 text-left">
        <!-- <h4 class="col-xs-7">
                    {{ $route.params.year }}{{ month == `6` ? `半` : `` }}年度{{ getCompanyName() }}{{
                        $route.name }}
                </h4> -->
        <h4 class="col-xs-7">
          {{ $route.params.year }}{{ month == `6` ? `半` : `` }}年度{{
            getCompanyName()
          }}{{
            $route.query.type == "1"
              ? "(高级经理职等以上)管理人员综合能力【评定结果反馈】操作表"
              : $route.name
          }}
        </h4>
        <div class="col-xs-5 text-right" style="padding: 0; line-height: 38px">
          <button class="btn btn-primary btn-xs" @click="showDescrAction">
            操作说明
          </button>
          <button
            class="btn btn-primary btn-xs"
            :disabled="!sentOa"
            @click="_sentFeedbackToOA"
          >
            触发OA
          </button>
          <!-- push(`/${$route.params.year}/${$route.params.month}/menu/1`) -->
          <button
            class="btn btn-primary btn-xs"
            @click="
              $router.replace(
                `/${$route.params.year}/${$route.params.month}/menu/1`
              )
            "
          >
            返回上级
          </button>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-xs-12" style="width: auto;padding:0">
        <div class="list-table">
          <div class="scroll-table" style="height: 100%; overflow-x: hidden">
            <table class="scroll-table-header table-bordered">
              <colgroup>
                <col style="width: 40px" />
                <col style="width: 120px" />
                <col style="width: 70px" />
                <col style="width: 120px" />
                <col style="width: 120px" />
                <col style="width: 70px" />
                <col style="width: 70px" />
                <col style="width: 70px" />
                <col style="width: 120px" />
                <col style="width: 120px" />
                <col style="width: 70px" />
                <col style="width: 70px" />
                <col style="width: 60px" />
                <col style="width: 60px" />
                <col style="width: 100px" />
              </colgroup>
              <thead>
                <tr>
                  <th rowspan="2">序号</th>
                  <th rowspan="2">部门</th>
                  <th colspan="5">部门主管</th>
                  <th colspan="5">体系领导</th>
                  <th rowspan="2">部门员工确认情况</th>
                  <th rowspan="2">删除部门</th>
                  <th rowspan="2">备注</th>
                </tr>
                <tr>
                  <th>姓名</th>
                  <th>岗位</th>
                  <th>职等</th>
                  <th>操作</th>
                  <th>确认情况</th>
                  <th>姓名</th>
                  <th>岗位</th>
                  <th>职等</th>
                  <th>操作</th>
                  <th>确认情况</th>
                </tr>
              </thead>
            </table>
            <div
              class="scroll-table-body"
              :style="{
                overflow: 'auto',
                maxHeight: body_height - 160 + 'px',
                'overflow-y': 'scroll',
                'overflow-x': 'hidden',
              }"
            >
              <table
                style="
                  margin: 0px 10px 0 0;
                  position: relative;
                  font-size: 13px;
                  float: left;
                  border: none;
                "
                class="table table-bordered table-hover table-striped"
              >
                <colgroup>
                  <col style="width: 40px" />
                  <col style="width: 120px" />
                  <col style="width: 70px" />
                  <col style="width: 120px" />
                  <col style="width: 120px" />
                  <col style="width: 70px" />
                  <col style="width: 70px" />
                  <col style="width: 70px" />
                  <col style="width: 120px" />
                  <col style="width: 120px" />
                  <col style="width: 70px" />
                  <col style="width: 70px" />
                  <col style="width: 60px" />
                  <col style="width: 60px" />
                  <col style="width: 100px" />
                </colgroup>
                <tbody>
                  <tr v-for="(data, $index) in tableData" :key="$index">
                    <td>{{ $index + 1 }}</td>
                    <td>{{ data.deptName }}</td>
                    <td>{{ data.deptLeader }}</td>
                    <td>{{ data.deptLeaderPost }}</td>
                    <td>{{ data.deptLeaderLevelName }}</td>
                    <td
                      @click="showDescrAction2(1, data)"
                      style="color: dodgerblue; cursor: pointer"
                    >
                      调整
                    </td>
                    <td>{{ data.deptConfirmStatusText }}</td>
                    <td>{{ data.sysLeader }}</td>
                    <td>{{ data.sysLeaderPost }}</td>
                    <td>{{ data.sysLeaderLevelName }}</td>
                    <td style="color: dodgerblue">
                      <div
                        style="
                          display: flex;
                          justify-content: space-around;
                          align-items: center;
                        "
                      >
                        <span
                          @click="showDescrAction2(2, data)"
                          style="cursor: pointer"
                          >调整</span
                        >
                        <span @click="deleteUser(data)" style="cursor: pointer"
                          >清空</span
                        >
                      </div>
                    </td>
                    <td>{{ data.sysConfirmStatusText }}</td>
                    <td
                      @click="
                        $router.push(
                          'feedbackInfo?id=' +
                            data.fdId +
                            '&deptName=' +
                            data.deptName
                        )
                      "
                      style="color: dodgerblue; cursor: pointer"
                    >
                      查阅
                    </td>
                    <td
                      @click="deleteItem(data)"
                      style="color: dodgerblue; cursor: pointer"
                    >
                      删除
                    </td>
                    <td>{{ data.remark }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="isNotData" style="text-align: center; padding: 15px 0">
      暂无数据......
    </div>
    <div v-if="type" class="showWind">
      <div style="width: 600px; background: #fff">
        <div class="showTitle">
          {{ type == 1 ? "部门主管调整" : type == 2 ? "体系领导调整" : "" }}
          <span
            @click="type = null"
            style="
              float: right;
              font-size: 24px;
              line-height: 1;
              cursor: pointer;
            "
            >×</span
          >
        </div>
        <div class="setForm">
          <div class="formList">
            <div class="items">情形</div>
            <div class="items">部门</div>
            <div class="items">姓名</div>
            <div class="items">岗位</div>
            <div class="items">职等</div>
          </div>
          <div class="formList">
            <div class="items">调整前</div>
            <div class="items">{{ listObj.deptName }}</div>
            <div class="items">
              {{
                type == 1
                  ? listObj.deptLeader
                  : type == 2
                  ? listObj.sysLeader
                  : ""
              }}
            </div>
            <div class="items">
              {{
                type == 1
                  ? listObj.deptLeaderPost
                  : type == 2
                  ? listObj.sysLeaderPost
                  : ""
              }}
            </div>
            <div class="items">
              {{
                type == 1
                  ? listObj.deptLeaderLevelName
                  : type == 2
                  ? listObj.sysLeaderLevelName
                  : ""
              }}
            </div>
          </div>
          <div class="formList">
            <div class="items">调整后</div>
            <div class="items">{{ listObj.deptName }}</div>
            <div
              @click="showAddressBookDialog"
              class="items"
              style="color: dodgerblue; cursor: pointer"
            >
              {{ userInfo.userName || "点击录入" }}
            </div>
            <div class="items">{{ userInfo.positionName || "" }}</div>
            <div class="items">{{ userInfo.levelName || "" }}</div>
          </div>
        </div>
        <div style="text-align: right; padding: 30px 15px 6px 0">
          <button
            class="btn btn-primary btn-xs"
            @click="_updateFeedback(false)"
          >
            确认
          </button>
          <button class="btn btn-xs" @click="type = null">关闭</button>
        </div>
      </div>
    </div>
    <div style="display: none">
      <div class="showText" style="padding: 10px 20px 0 20px; margin: 0 auto">
        一、评定结果触发对象：员工个人、部门主管、体系领导<br />
        二、触发操作<br />
        （1）员工个人：点击“触发OA”后，会自动触发到员工个人OA工作台 。<br />
        （2）部门主管：<br />
        ①系统根据组织架构系统自动读取，若有特殊情况（如：部门主管即将离职等情况），可操作调整。<br />
        ②点击“触发OA”后，会自动触发到部门主管OA工作台 。<br />
        （3）体系领导：<br />
        ①系统根据组织架构系统自动读取，若有特殊情况（如：体系领导即将离职等情况），可操作调整；若体系领导默认为总经理，且无人可替换时，可点击“清空”，不触发。<br />
        ②点击“触发OA”后，会自动触发到体系领导OA工作台 。
      </div>
    </div>
  </div>
</template>

<script>
import {
  findFeedbackList,
  getUserInfo,
  updateFeedback,
  sentFeedbackToOA,
  isAllowSentOa,
  deleteOne,
} from "@/api";
import AddressBook from "@/components/AddressBook";
import { set } from "nprogress";

export default {
  components: {
    AddressBook,
  },
  data() {
    return {
      tableData: [],
      body_height: 0,
      firstRowHeader: [],
      secondRowHeader: [],
      yearData: [],
      listObj: {},
      userInfo: {},
      type: null,
      sentOa: true,
      year: "",
      month: "",
      companyGroupId: "",
      positionGradeId: "",
      deptArea: Utils.getQueryParams("deptArea"),
      isNotData: false,
    };
  },
  methods: {
    showAddressBookDialog() {
      let layerDialog = Utils.layerBox.layerDialogOpen({
        title: "人员选择器",
        btn: ["确定", "取消"],
        maxmin: false, //开启最大化最小化按钮
        area: ["860px", "590px"],
        btn1: (index, layero) => {
          this.dialogComponent &&
            this.dialogComponent.$children[0].doSubmit &&
            this.dialogComponent.$children[0].doSubmit();
        },
        btn2: (index, layero) => {
          this.dialogComponent &&
            this.dialogComponent.$children[0].doCancel &&
            this.dialogComponent.$children[0].doCancel();
        },
      });
      this.dialogComponent = Utils.layerBox.layerLoadVueComponent({
        layer: layerDialog,
        component: AddressBook,
        methods: {
          //必要参数
          doConfirm: (result) => {
            //doConfirm可获取组件页面传过来的参数，作用：例如执行回调方法或者传递结果到父组件
            this._getUserInfo(result[0].fdEmployeeNumber);
            layer.close(layerDialog); //关闭对话框
          },
          //必要参数
          doClose: () => {
            layer.close(layerDialog); //关闭对话框
          },
        },
        //传递本组件的参数给对话框组件，对话框组件通过props属性params获取值,例如下面这个val属性取值：this.params.val
        props: {
          type: "2", //1:部门,2://人员,3:部门和人员
        },
      });
    },

    getCompanyName() {
      let companyName = Utils.getCompanyNameByGroupId(this.companyGroupId);
      if (
        this.positionGradeId == "0006" ||
        this.positionGradeId == "0007" ||
        this.positionGradeId == "0032" ||
        this.positionGradeId == "0033"
      ) {
        companyName = "全集团";
      }
      return companyName;
    },
    getMonthScore(data, userData) {
      let result = this.yearData.find((item) => {
        if (
          item.fd_month == data.month &&
          item.fd_year == data.year &&
          item.user_id == userData.userId
        ) {
          return item;
        }
      });
      return (result &&
        result.fd_year + "-" + result.fd_month !==
          this.year + "-" + this.fd_month) ||
        userData.status == 30
        ? result
          ? result.last_result
          : ""
        : "";
    },
    _findFeedbackList() {
      const loading = this.$loading({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      /*先判断有权限才可以查询(modify by huangdh@2019-05-24)*/
      // if (this.$store.state.doubleCol.arrAuths.point_ablity_findYearReport) {
      findFeedbackList({
        companyGroupId: this.companyGroupId,
        year: this.year,
        month: this.month,
      })
        .then((res) => {
          if (!res.success) return;
          this.tableData = res.data || [];
          loading.close();
          if (res.data.length == 0) {
            this.isNotData = true;
          }
        })
        .catch((err) => {
          loading.close();
          layer.msg(`${err.msg}`, { icon: 2, shade: 0.3, shadeClose: true });
        });
      // }
      // else {
      //     layer.msg(`对不起，您没有权限查询本界面.`, { icon: 2, shade: 0.3, shadeClose: true });
      // }
    },
    _getUserInfo(id) {
      getUserInfo({ userId: id })
        .then((res) => {
          if (!res.success) return;
          this.userInfo = res.data || {};
        })
        .catch((err) => {
          layer.msg(`${err.msg}`, { icon: 2, shade: 0.3, shadeClose: true });
        });
    },
    _updateFeedback(type) {
      let params = { ...this.listObj };
      if (type === true) {
        params.sysLeader = "";
        params.sysLeaderLevelName = "";
        params.sysLeaderPost = "";
      } else {
        if (this.type == 1) {
          if (this.userInfo.userName == params.deptLeader) {
            layer.msg(`当前部门主管已经是“${params.deptLeader}”不可重复提交`, {
              icon: 2,
              shade: 0.3,
              shadeClose: true,
            });
            return;
          }
          params.deptLeader = this.userInfo.userName;
          params.deptLeaderLevelName = this.userInfo.levelName;
          params.deptLeaderLevel = this.userInfo.levelCode;
          params.deptLeaderPost = this.userInfo.positionName;
          params.deptLeaderId = this.userInfo.userId;
        }
        if (this.type == 2) {
          if (this.userInfo.userName == params.sysLeader) {
            layer.msg(`当前体系领导已经是“${params.sysLeader}”不可重复提交`, {
              icon: 2,
              shade: 0.3,
              shadeClose: true,
            });
            return;
          }
          params.sysLeader = this.userInfo.userName;
          params.sysLeaderLevelName = this.userInfo.levelName;
          params.sysLeaderLevel = this.userInfo.levelCode;
          params.sysLeaderPost = this.userInfo.positionName;
          params.sysLeaderId = this.userInfo.userId;
        }
        if (!this.userInfo.userName) {
          layer.msg(`请选择人员`, { icon: 2, shade: 0.3, shadeClose: true });
          return;
        }
      }

      updateFeedback(params)
        .then((res) => {
          if (!res.success) return;
          this.type = null;
          layer.msg(`操作成功`, { icon: 1, shade: 0.3, shadeClose: true });
          this._findFeedbackList();
          this.userInfo = {};
        })
        .catch((err) => {
          layer.msg(`${err.msg}`, { icon: 2, shade: 0.3, shadeClose: true });
        });
    },
    deleteUser(list) {
      this.listObj = list;
      layer.confirm(
        "确认清空当前体系领导吗？",
        {
          title: "提示",
          btn: ["确定", "取消"], //按钮
        },
        (index) => {
          this._updateFeedback(true);
          layer.close(index);
        }
      );
    },
    deleteItem(list) {
      layer.confirm(
        `是否删除${list.deptName}？`,
        {
          title: "提示",
          btn: ["删除", "取消"], //按钮
        },
        (index) => {
          this._deleteOne(list.fdId);
          layer.close(index);
        }
      );
    },

    _deleteOne(fdId) {
      deleteOne({ fdId: fdId })
        .then((res) => {
          if (!res.success) return;
          layer.msg(`操作成功`, { icon: 1, shade: 0.3, shadeClose: true });
          this._findFeedbackList();
          this.userInfo = {};
        })
        .catch((err) => {
          layer.msg(`${err.msg || "操作失败"}`, {
            icon: 2,
            shade: 0.3,
            shadeClose: true,
          });
        });
    },

    _sentFeedbackToOA() {
      layer.confirm(
        "确认提交至OA吗？",
        {
          title: "提示",
          btn: ["确定", "取消"], //按钮
        },
        (index) => {
          sentFeedbackToOA({
            companyGroupId: this.companyGroupId,
            year: this.year,
            month: this.month,
          })
            .then((res) => {
              if (!res.success) return;
              this.type = null;
              layer.msg(res.data || `操作成功`, {
                icon: 1,
                shade: 0.3,
                shadeClose: true,
              });
              this._findFeedbackList();
              this._isAllowSentOa();
            })
            .catch((err) => {
              layer.msg(`${err.msg || "操作失败"}`, {
                icon: 2,
                shade: 0.3,
                shadeClose: true,
              });
            });
          layer.close(index);
        }
      );
    },

    _isAllowSentOa() {
      isAllowSentOa({
        companyGroupId: this.companyGroupId,
        year: this.year,
        month: this.month,
      })
        .then((res) => {
          if (!res.success) return;
          this.sentOa = res.data;
        })
        .catch((err) => {
          layer.msg(`${err.msg || "操作失败"}`, {
            icon: 2,
            shade: 0.3,
            shadeClose: true,
          });
        });
    },

    initPage() {
      this.pageResize();
      const { companyGroupId, year, month, positionGradeId } =
        this.$route.params;
      this.companyGroupId = companyGroupId;
      this.year = year;
      this.month = month;
      this.positionGradeId = positionGradeId;
      // this.deptArea = "";
      this._findFeedbackList();
      this._isAllowSentOa();
    },
    pageResize() {
      this.body_height = $(window).height();
    },
    showDescrAction() {
      Utils.layerBox.layerDialogOpen({
        title: "操作说明",
        area: ["750px", "400px"],
        btn: [],
        content: `${$(".showText")[0].outerHTML}`,
      });
    },
    showDescrAction2(type, list) {
      this.listObj = list;
      this.type = type;
    },
  },
  watch: {
    $route: function () {
      if (!this.$route.query.type) {
        this.initPage();
      }
    },
  },
  mounted() {
    if (!this.$route.query.type) {
      this.initPage();
    }
    $(window).resize(this.pageResize);
  },
};
</script>

<style scoped>
.formList {
  display: flex;
  width: 550px;
  margin: auto;
  border-left: 1px solid#999;
  border-top: 1px solid#999;
}

.formList:nth-child(1) {
  margin-top: 10px;
  background: #b7d8dc;
}

.formList:last-child {
  border-bottom: 1px solid#999;
}

.items {
  flex: 1;
  padding: 4px;
  border-right: 1px solid #999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.showWind {
  position: fixed;
  left: 0;
  top: 0;
  background: rgba(0, 0, 0, 0.4);
  width: 100%;
  height: 100%;
  z-index: 99;
  display: flex;
  align-items: center;
  justify-content: center;
}

.showTitle {
  background: #eee;
  padding: 6px;
  border-bottom: 1px solid#ccc;
}
/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}
.list-year-data {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
.scroll-table-body {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
</style>
