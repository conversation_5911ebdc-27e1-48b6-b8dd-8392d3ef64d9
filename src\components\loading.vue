<template>
  <div class="loader-inner line-spin-fade-loader">
      <div v-for="i in 8" :key="i"></div>
  </div>
</template>

<script>
export default {

}
</script>

<style>
@-webkit-keyframes line-spin-fade-loader {
  50% {
    opacity: 0.3; }

  100% {
    opacity: 1; } }

@keyframes line-spin-fade-loader {
  50% {
    opacity: 0.3; }

  100% {
    opacity: 1; } }
.loader-inner {
    position: relative;
    display: inline-block;
    width: 20px;
    height: 15px;
    top: 2px;
    left:-1px;
}
.line-spin-fade-loader {
  position: relative; }
  .line-spin-fade-loader > div:nth-child(1) {
    top: 6px;
    left: 4.65px;
    animation: line-spin-fade-loader 1.2s 0.12s infinite ease-in-out; }
  .line-spin-fade-loader > div:nth-child(2) {
    top: 5.43636px;
    left: 9px;
    transform: rotate(-45deg);
    animation: line-spin-fade-loader 1.2s 0.24s infinite ease-in-out; }
  .line-spin-fade-loader > div:nth-child(3) {
    top: 0.6px;
    left: 11.6px;
    transform: rotate(90deg);
    animation: line-spin-fade-loader 1.2s 0.36s infinite ease-in-out; }
  .line-spin-fade-loader > div:nth-child(4) {
    top: -3.63636px;
    left: 10.63636px;
    transform: rotate(45deg);
    animation: line-spin-fade-loader 1.2s 0.48s infinite ease-in-out; }
  .line-spin-fade-loader > div:nth-child(5) {
    top: -6px;
    left: 5px;
    animation: line-spin-fade-loader 1.2s 0.6s infinite ease-in-out; }
  .line-spin-fade-loader > div:nth-child(6) {
    top: -4.63636px;
    left: 0.63636px;
    transform: rotate(-45deg);
    animation: line-spin-fade-loader 1.2s 0.72s infinite ease-in-out; }
  .line-spin-fade-loader > div:nth-child(7) {
    top: -0.465px;
    left: -1.6px;
    transform: rotate(90deg);
    animation: line-spin-fade-loader 1.2s 0.84s infinite ease-in-out; }
  .line-spin-fade-loader > div:nth-child(8) {
    top: 4.43636px;
    left: -0.44364px;
    transform: rotate(45deg);
    animation: line-spin-fade-loader 1.2s 0.96s infinite ease-in-out; }
  .line-spin-fade-loader > div {
    background-color: #333;
    width: 2px;
    height: 5px;
    border-radius: 2px;
    margin: 5px;
    animation-fill-mode: both;
    position: absolute; }
</style>
