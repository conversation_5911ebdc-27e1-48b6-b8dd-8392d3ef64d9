<template>
  <div class="grade-evaluation-dialog-container">
      <div class="row">
        <div class="col-xs-12">
           <table style="width:98%;margin-bottom:10px;">
             <tr>
               <td style="width:12%;text-align:right;">直接领导：</td>
               <td style="width:20%">{{this.params.leaderName}}</td>
               <td style="width:12%;text-align:right;">审核人：</td>
               <td style="width:20%">{{this.params.checkerName}}</td>
               <td style="width:12%;text-align:right;">审定人：</td>
               <td >{{this.params.approveName}}</td>
             </tr>
           </table> 
          <table class="table table-bordered">
            <thead>
              <colgroup>
                <col style="width:4%"/>
                <col style="width:10%"/>
                <col style="width:8%"/>
                <col style="width:5%"/>
                <col style="width:5%"/>
                <col style="width:8%"/>
                <col style="width:14%"/>
                <col style="width:10%"/>
                <col style="width:6%"/>
                <col style="width:6%"/>
                <col style="width:8%"/>
                <col style="width:8%"/>
                <col style="width:8%"/>
              </colgroup>
              <tr>
                <th rowspan="2">序号</th>
                <th colspan="7">评定对象信息</th>
                <th colspan="2">级别评定结果</th>
                <th rowspan="2">直接领导<br>评定结果</th>
                <th rowspan="2">审核人<br>评定结果</th>
                <th rowspan="2">审定人<br>评定结果</th>
              </tr>
              <tr>
                <th>部门</th>
                <th>姓名</th>
                <th>性别</th>
                <th>年龄</th>
                <th>学历</th>
                <th>岗位</th>
                <th>职等</th>
                <th>级别</th>
                <th>分值</th>
              </tr>
            </thead>
            <tbody>
              <colgroup>
                <col style="width:4%"/>
                <col style="width:10%"/>
                <col style="width:8%"/>
                <col style="width:5%"/>
                <col style="width:5%"/>
                <col style="width:8%"/>
                <col style="width:14%"/>
                <col style="width:10%"/>
                <col style="width:6%"/>
                <col style="width:6%"/>
                <col style="width:8%"/>
                <col style="width:8%"/>
                <col style="width:8%"/>
              </colgroup>
              <tr v-for="(data,$index) in tableData" :key="$index">
                <td>{{$index+1}}</td>
                <td>{{data.deptName}}</td>
                <td>{{data.userName}}</td>
                <td>{{data.gender}}</td>
                <td>{{data.age}}</td>
                <td>{{data.degree}}</td>
                <td>{{data.jobName}}</td>
                <td>{{data.gradeName}}</td>
                <td>{{data.meetingLevel}}</td>
                <td>{{data.avgPoint}}</td>
                <td>{{data.leaderResult}}</td>
                <td>{{data.checkerResult}}</td>
                <td>{{data.approveResult}}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
  </div>
</template>

<script>
import { findFinishConfirmDetailsBy } from "@/api";
export default {
  props: {
      params:{
          type:Object,
          required:true
      }
  },
  data() {
    return {
      tableData: []
    };
  },
  methods: {
    _findFinishConfirmDetailsBy() {
      findFinishConfirmDetailsBy({
        confirmLeaderId:this.params.confirmLeaderId,
        leader3Name: this.params.leader3Name
      }).then(res => {
        if (!res.success) return;
        this.tableData = res.data ? res.data : [];
      }).catch(err=>{
        layer.msg(`${err.msg}`, {icon: 2,shade:0.3,shadeClose:true});
      });
    }
  },
  created() {
    this._findFinishConfirmDetailsBy();
  }
};
</script>

<style lang="scss">
.grade-evaluation-dialog-container {
  padding: 20px 0;
  .row {
    margin: 0;
    padding-left: 0;
    padding-right: 0;
    box-sizing: border-box;
  }
  .input-sm {
    height: 28px;
    padding: 5px 10px 5px 5px;
  }
  div.scroll-table {
    width: 100%;
    height: 320px;
    display: inline-block;
    float: left;
    background: #f4fafb;
    table.scroll-table-header {
      width: 100%;
      font-size: 13px;
      // display:block;
      // padding-right:17px;
    }
    table.scroll-table-body {
    }
  }
  table thead {
    tr {
      th {
        text-align: center;
        border-bottom: none;
        padding: 2px 5px;
        vertical-align: middle;
        background: #b7d8dc;
        height: 32px;
        font-size: 12px;
        color: #333;
        & > * {
          background: #b7d8dc !important;
          outline: none;
        }
      }
    }
    &.hide-thead-th > tr > th {
      height: 0;
      padding: 0;
      background: transparent;
      border: none;
    }
  }
  .table tbody tr {
    td {
      padding: 2px 5px;
      vertical-align: middle;
      height: 27px;
      font-size: 12px;
      text-align: center;
      .btn {
        padding-top: 0;
        padding-bottom: 0;
      }
    }
    &:nth-child(odd) {
      background: #fff;
    }
    &:nth-child(even) {
      background: #f9f9f9;
    }
  }
}
</style>
