window.ajax = function(options, cb) {
    var req_options = {
        url: options.url || '',
        dataType: options.dataType || 'json',
        type: options.type || 'get',
        contentType: options.contentType || 'application/json',
        timeout: options.timeout || 5000,
        async: options.async || true,
        cache: options.cache || true,
        data: options.data,
        crossDomain: true,
        xhrFields: {
            withCredentials: true
        }
    }
    if (options.dataType == 'jsonp') {
        req_options.type = 'get';
        req_options.jsonp = options.jsonp || 'callback';
        req_options.jsonpCallback = options.jsonpCallback || 'jsonpCallback';
    }
    return $.ajax(req_options).always(function(data){});
}
var getUrlParams = function(){
    var url_params = window.location.search;
    url_params = url_params.substring(1,url_params.length);
    var params_arr = url_params.split('&');
    var obj = {};
    $.each(params_arr,function(index,item){
        var obj_arr = item.split('=');
        obj[obj_arr[0]] = obj_arr[1];
    })
    return obj;
}