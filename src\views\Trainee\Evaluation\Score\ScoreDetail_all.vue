<template>
  <div class="score-detail-container">
    <div class="row">
      <div class="col-xs-12 text-left">
        <h4 class="col-xs-6">管培生综合能力评定得分明细表</h4>
        <div class="col-xs-6 text-right" style="line-height: 40px">
          <button
            class="btn btn-primary btn-xs"
            @click="_findCompanyStudentList('1')"
          >
            {{ buttonQueryText }}
          </button>
          <button class="btn btn-primary btn-xs" @click="backPage()">
            返回上级
          </button>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="list-year-head-table" style="background: #b7d8dc">
        <table
          class="list-year-head table table-bordered"
          style="margin-bottom: 0; margin: 0 auto 0 0"
        >
          <colgroup>
            <col width="40" />
            <col width="50" />
            <col width="45" />
            <col width="60" />
            <col width="40" />
            <col width="45" />
            <col width="65" />
            <col width="65" />
            <col width="65" />
            <col width="60" />
            <col width="67" />

            <col width="50" />
            <col width="50" />
            <col width="40" />
            <col width="40" />
            <col width="40" />
            <col width="40" />
            <col width="40" />
            <col width="50" />
            <col width="40" />
            <col width="50" />
            <col width="40" />

            <col width="40" />
            <col width="40" />
            <col width="60" />
            <col width="40" />
            <col width="40" />
          </colgroup>
          <thead>
            <tr>
              <th rowspan="2">序号</th>
              <th rowspan="2">届别</th>
              <th rowspan="2">岗位<br />级别</th>
              <th rowspan="2">姓名</th>
              <th rowspan="2">性别</th>
              <th rowspan="2">学历</th>
              <th rowspan="2">部门</th>
              <th rowspan="2">岗位</th>
              <th rowspan="2">职务等级</th>
              <th rowspan="2">评定人</th>
              <th rowspan="2">评定方式</th>
              <th rowspan="2">权重<br />比例</th>
              <th colspan="9">成功要素评定</th>
              <th rowspan="2">能力<br />得分</th>
              <th rowspan="2">能力<br />等级</th>
              <th rowspan="2">积极<br />态度</th>
              <th rowspan="2">稳定性</th>
              <th rowspan="2">能力权重得分</th>
              <th rowspan="2">评价<br />明细</th>
            </tr>
            <tr>
              <th>思维与行动</th>
              <th>洞察力</th>
              <th>巧用专长</th>
              <th>提高能力</th>
              <th>纪律性</th>
              <th>领导力</th>
              <th>创新与借鉴</th>
              <th>拥抱变化</th>
              <th>营造多样化</th>
            </tr>
          </thead>
        </table>
      </div>
      <div
        class="list-year-data"
        :style="{ maxHeight: listy_ear_height + 'px' }"
        style="
          overflow-x: hidden;
          overflow-y: scroll;
          border-bottom: 1px solid #ccc;
        "
      >
        <table style="margin-bottom: 0" class="table table-bordered">
          <colgroup>
            <col width="40" />
            <col width="50" />
            <col width="45" />
            <col width="60" />
            <col width="40" />
            <col width="45" />
            <col width="65" />
            <col width="65" />
            <col width="65" />
            <col width="60" />
            <col width="67" />

            <col width="50" />
            <col width="50" />
            <col width="40" />
            <col width="40" />
            <col width="40" />
            <col width="40" />
            <col width="40" />
            <col width="50" />
            <col width="40" />
            <col width="50" />
            <col width="40" />

            <col width="40" />
            <col width="40" />
            <col width="60" />
            <col width="40" />
            <col width="40" />
          </colgroup>
          <tbody>
            <template v-for="(data, index) in tableData">
              <tr :key="index">
                <td
                  style="text-align: left; font-size: 14px; font-weight: bold"
                  colspan="27"
                >
                  {{ data.companyName }}
                </td>
              </tr>
              <tr v-if="data.have === '无'" :key="$index + '-' + index">
                <td
                  style="
                    text-align: center;
                    font-size: 14px;
                    font-weight: bold;
                    color: red;
                  "
                  colspan="27"
                >
                  无
                </td>
              </tr>
              <tr
                v-else
                v-for="(item, $index) in data.list"
                :key="$index + '-' + index"
              >
                <td :rowspan="item.rowspan" v-if="item.raterIndex == 1">
                  {{ item.userIndex }}
                </td>
                <td :rowspan="item.rowspan" v-if="item.raterIndex == 1">
                  {{ item.graduateYear || "" }}
                </td>
                <td :rowspan="item.rowspan" v-if="item.raterIndex == 1">
                  {{ getLevelName(item.schoolLevel) || "" }}
                </td>
                <td :rowspan="item.rowspan" v-if="item.raterIndex == 1">
                  {{ item.userName || "" }}
                </td>
                <td :rowspan="item.rowspan" v-if="item.raterIndex == 1">
                  {{ item.gender || "" }}
                </td>
                <td :rowspan="item.rowspan" v-if="item.raterIndex == 1">
                  {{ item.degree || "" }}
                </td>
                <td :rowspan="item.rowspan" v-if="item.raterIndex == 1">
                  {{ item.deptName || "" }}
                </td>
                <td :rowspan="item.rowspan" v-if="item.raterIndex == 1">
                  {{ item.jobName || "" }}
                </td>
                <td :rowspan="item.rowspan" v-if="item.raterIndex == 1">
                  {{ item.gradeName || "" }}
                </td>
                <td>{{ item.raterName }}</td>
                <td>{{ item.judgeType }}</td>

                <td>
                  {{
                    item.weightPoint
                      ? parseFloat(item.weightPoint) * 100 + "%"
                      : ""
                  }}
                </td>
                <td>{{ item.mindAction || "" }}</td>
                <td>{{ item.sharpEye || "" }}</td>
                <td>{{ item.mindSkill || "" }}</td>
                <td>{{ item.groupImprove || "" }}</td>
                <td>{{ item.sharpDiscipline || "" }}</td>
                <td>{{ item.groupLeader || "" }}</td>
                <td>{{ item.mindNew || "" }}</td>
                <td>{{ item.sharpEmbrace || "" }}</td>
                <td>{{ item.groupCooperate || "" }}</td>
                <td>{{ toFixed2(item.avgPoint) }}</td>

                <td>{{ item.ablityLevel || "" }}</td>
                <td>{{ item.energyPoint }}</td>
                <td>{{ item.stablity }}</td>
                <td :rowspan="item.rowspan" v-if="item.raterIndex == 1">
                  {{ item.finalPoint.toFixed(1) }}
                </td>
                <td :rowspan="item.rowspan" v-if="item.raterIndex == 1">
                  <a @click="toGoodAbsence(item)"
                    ><img src="../../../../assets/images/action_dtl.png" alt=""
                  /></a>
                </td>
              </tr>
            </template>
          </tbody>
        </table>
        <span v-if="tableDataLength <= 0">
          {{ tableDataLength == -1 ? "加载中..." : "暂无数据" }}
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import {
  findStudentPointListByCompanyGroupId,
  findCompanyStudentList,
} from "@/api";
import ScoreDescrDialog from "./ScoreDescrDialog";
export default {
  data() {
    return {
      tableData: [],
      year: "",
      month: "",
      head_width: 0,
      listy_ear_height: 0,
      companyGroupId: "",
      isview: "",
      mySort: "",
      buttonQueryText: "按届别显示",
      tableDataLength: -1
    };
  },
  methods: {
    getLevelName(num) {
      return Utils.number2ChNum(num) + "级";
    },
    // eachData(data){
    //   let start = 0, end = 0;
    //   data.forEach((d,index) =>{
    //     if(index === 0 ){
    //       d.order = 1;
    //       d.start = true;
    //       start = 0;
    //     }else{
    //       if(d.userName == data[start].userName){
    //         d.order = data[index-1].order;
    //         if(data.length === index + 1){
    //           data[start].end_index = index+1;
    //         }
    //       }else{
    //         d.start = true;
    //         d.order = data[index-1].order+1;
    //         end = index;
    //         data[start].end_index = end - start;
    //         start = index;
    //       }
    //     }
    //   })
    // },
    _findCompanyStudentList(queryType) {
      findCompanyStudentList({
        fdYear: this.year,
        fdMonth: this.month,
        sortType: this.mySort,
      }).then((res) => {
        this.tableData = res.data || [];
        //this.eachData(this.tableData)
        console.log(queryType);
        if (queryType == "1") {
          if (this.mySort == "1") {
            this.mySort = "2";
            this.buttonQueryText = "按届别显示";
          } else {
            this.mySort = "1";
            this.buttonQueryText = "按任职公司显示";
          }
        }
        this.tableDataLength = this.tableData.length
      });
    },
    resizeTable() {
      this.listy_ear_height = $(window).height() - 170;
      this.head_width = $(".list-year-data>table").width() + 2;
    },
    toFixed2(num) {
      return num == null ? null : ((num * 100) / 100).toFixed(1);
    },

    toGoodAbsence(item) {
      const { year, month } = this.$route.params;
      this.companyGroupId = item.companyGroupId;
      this.year = year;
      this.month = month;
      var ablityId = item.ablityId;
      var url =
        "/" +
        this.year +
        "/" +
        this.month +
        "/Trainee/Staff/" +
        this.companyGroupId +
        "/viewGoodAbsence";

      this.$router.push({
        path: url,
        query: {
          ablityId: ablityId,
          year: this.year,
          month: this.month,
          companyGroupId: this.companyGroupId,
        },
      });
    },
    backPage() {
      this.$router.push({
        path:
          `/` +
          this.year +
          `/` +
          this.month +
          `/trainee/evaluation/meetingResult`,
      });
    },
    initPage() {
      this.resizeTable();
      const { year, month } = this.$route.params;
      this.year = year;
      this.month = month;
      const { isview } = this.$route.query;
      this.isview = isview;
      this.mySort = "1";
      this._findCompanyStudentList("1");
    },
  },
  watch: {
    $route: function () {
      this.initPage();
    },
  },
  mounted() {
    $(window).resize(this.resizeTable);
    this.initPage();
  },
};
</script>

<style scoped>
/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}
.list-year-data {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
</style>
