import Vue from "vue";
import Router from "vue-router";

const loadComponent = (fileName) => {
  return () => import("@/components/" + fileName);
};
const loadViews = (fileName) => {
  return () => import("@/views/" + fileName);
};

Vue.use(Router);

export default new Router({
  routes: [
    {
      path: "/",
      name: "界面容器",
      component: loadViews("Container"),
      redirect: "/yearmenu",
      children: [
        {
          path: "/yearmenu",
          name: "年度菜单",
          component: loadViews("YearMenu"),
          hidden: true,
        },
        {
          path: "/:year/:month/menu/:mIndex",
          name: "系统菜单",
          component: loadViews("FuncMenu"),
          hidden: true,
        },
        {
          path: "/:year/:month/:companyGroupId/setting",
          name: "管理人员综合能力级别、职级及比例配置",
          meta: {
            type: "menu",
          },
          component: loadViews("ComprehensiveCapacityRankGrade/Main"),
          children: [
            {
              path: "/:year/:month/:companyGroupId/setting/grade",
              name: "管理人员综合能力【级别】配置表",
              meta: {
                type: "url",
              },
              component: loadViews(
                "ComprehensiveCapacityRankGrade/GradeSetting"
              ),
            },
            {
              path: "/:year/:month/:companyGroupId/setting/rank",
              name: "管理人员综合能力【职级】配置表",
              meta: {
                type: "url",
              },
              component: loadViews(
                "ComprehensiveCapacityRankGrade/RankSetting"
              ),
            },
            {
              path: "/:year/:month/:companyGroupId/setting/ratio",
              name: "管理人员综合能力【级别、职级及比例标准】表",
              meta: {
                type: "url",
              },
              component: loadViews("ComprehensiveCapacityRankGrade/RatioScale"),
            },
          ],
        },

        {
          path: "/:year/12/:companyGroupId/deputydirectorbelow",
          name: "入职满三年本科副主任以下人员能力评定",
          meta: {
            fullYear: true,
            type: "url",
            month: 12,
          },
          component: loadViews("DeputyDirectorBelow"),
        },
        {
          path: "/:year/6/:companyGroupId/allevaluationresult",
          name: "半年度各职等管理人员综合能力【评定结果审批】表",
          // hidden:true,
          meta: {
            halfYear: true,
            type: "url",
            month: 6,
          },
          component: loadViews("HalfYearAllGradeEvaluationApproveResult"),
        },
        {
          path: "/:year/:month/:companyGroupId/:positionGradeId/evaluation",
          name: "各职等人员综合能力评定",
          meta: {
            type: "menu",
            gradeMenu: true,
            companyHasProd: ["1", "2", "6"],
            personNoPermission: {
              company2: {
                wuqj: [
                  "0006",
                  "0007",
                  "0009",
                  "0010",
                  "0026",
                  "0032",
                  "0033",
                  "0034",
                ],
              },
              company1: {
                chenwx: ["0006", "0007", "0032", "0033"],
                qinl: ["0009", "0010", "0011", "0012", "0013", "0015", "0019"],
                luyh: ["0009", "0010", "0011", "0012", "0013", "0015", "0019"],
              },
              company3:["0006", "0007", "0032", "0033"]
            },
            hasNoProGrade: [
              /*不区分生产一线、生产辅助、生产支持的职等*/
              "0006",
              "0007",
              "0009",
              "0010",
              "0023",
              "0027",
              "0026",
              "0032",
              "0033",
              "0034",
            ],
          },
          component: loadViews("AssessmentComprehensiveAbility/Main"),
          children: [
            {
              path: "/:year/:month/:companyGroupId/:positionGradeId/evaluation/objectdetail",
              name: "管理人员【评定对象】明细表",
              meta: {
                type: "url",
                isOuterMenu: true,
              },
              component: loadViews(
                "AssessmentComprehensiveAbility/EvaluationObject"
              ),
            },
            {
              path: "/:year/6/:companyGroupId/:positionGradeId/evaluation/gradeassessor",
              name: "管理人员【评定人确认】表",
              meta: {
                type: "url",
                halfYear: true,
                month: 6,
                gradeRank: "grade",
              },
              component: loadViews(
                "AssessmentComprehensiveAbility/RankAssessor"
              ),
            },
            {
              path: "/:year/12/:companyGroupId/:positionGradeId/evaluation/rankassessor",
              name: "管理人员【评定人确认】表",
              meta: {
                fullYear: true,
                type: "url",
                month: 12,
                gradeRank: "rank",
              },
              component: loadViews(
                "AssessmentComprehensiveAbility/RankAssessor"
              ),
            },
            {
              path: "/:year/12/:companyGroupId/:positionGradeId/evaluation/benchmarking",
              name: "管理人员【优级及标杆人员】评定表",
              meta: {
                type: "url",
                fullYear: true,
                month: 12,
              },
              component: loadViews(
                "AssessmentComprehensiveAbility/BenchmarkingPersonnel"
              ),
            },
            {
              path: "/:year/12/:companyGroupId/:positionGradeId/evaluation/GradeStandard_list",
              name: "管理人员【级别评定】表",
              meta: {
                type: "url",
                fullYear: true,
                month: 12,
              },
              component: loadViews(
                "AssessmentComprehensiveAbility/GradeStandard_list"
              ),
            },
            {
              path: "/:year/12/:companyGroupId/:positionGradeId/evaluation/ranksummary",
              name: "综合能力【级别评定汇总合议】表",
              meta: {
                type: "url",
                fullYear: true,
                month: 12,
              },
              component: loadViews(
                "AssessmentComprehensiveAbility/RankEvaluationSummary"
              ),
            },
            {
              path: "/:year/12/:companyGroupId/:positionGradeId/evaluation/ranksummary2",
              name: "综合能力【职级评定人确认】表",
              meta: {
                type: "url",
                fullYear: true,
                month: 12,
              },
              component: loadViews(
                "AssessmentComprehensiveAbility/RankEvaluationSummary2"
              ),
            },
            {
              path: "/:year/12/:companyGroupId/:positionGradeId/evaluation/RankGrade_list",
              name: "综合能力【职级评定】表",
              meta: {
                type: "url",
                fullYear: true,
                month: 12,
              },
              component: loadViews(
                // "AssessmentComprehensiveAbility/RankGrade_list"
                "AssessmentComprehensiveAbility/GradeAllChecker_list"
              ),
            },
            {
              path: "/:year/6/:companyGroupId/:positionGradeId/evaluation/GradeAllChecker_list",
              name: "综合能力【职级评定】表",
              meta: {
                type: "url",
                halfYear: true,
                month: 6,
              },
              component: loadViews(
                "AssessmentComprehensiveAbility/GradeAllChecker_list"
              ),
            },

            {
              path: "/:year/12/:companyGroupId/:positionGradeId/evaluation/result",
              name: "综合能力【级别、职级评定结果汇总】表",
              meta: {
                type: "url",
                fullYear: true,
                month: 12,
              },
              component: loadViews(
                "AssessmentComprehensiveAbility/GradeRankEvaluationResult"
              ),
            },
            {
              path: "/:year/6/:companyGroupId/:positionGradeId/evaluationGrade/result",
              name: "综合能力【职级评定结果汇总】表",
              meta: {
                type: "url",
                halfYear: true,
                month: 6,
              },
              component: loadViews(
                "AssessmentComprehensiveAbility/GradeRankEvaluationResult"
              ),
            },
            // {
            //   path: '/evaluation/grade',
            //   name: '综合能力【职级评定】表',
            //   component: loadViews('AssessmentComprehensiveAbility/GradeEvaluation')
            // },
            {
              path: "/:year/:month/:companyGroupId/:positionGradeId/evaluation/yearsresult",
              name: "综合能力【历史评定结果汇总】表",
              meta: {
                type: "url",
              },
              component: loadViews(
                "AssessmentComprehensiveAbility/YearsResultSummary"
              ),
            },
          ],
        },
        {
          path: "/:year/:month/:companyGroupId/:positionGradeId/highlight",
          name: "管理人员【工作亮点】表",
          meta: {
            type: "url",
          },
          component: loadViews("highlight"),
        },
        {
          path: "/:year/:month/:companyGroupId/:positionGradeId/summary",
          name: "工作亮点",
          hidden: true,
          meta: {
            type: "url",
          },
          component: loadViews("summary"),
        },
        {
          path: "/:year/12/:companyGroupId/allevaluationresult",
          name: "管理人员综合能力【评定结果审批】表",
          // hidden:true,
          meta: {
            fullYear: true,
            type: "url",
            month: 12,
            //谁有此菜单权限(按公司分类)
            personHasPermission: {
              // 2是companyCode 2表示赣州纸业公司
              company2: [
                "wupf",
                "liuzl",
                "zhangk",
                "qinl",
                "liujy",
                "maifc",
                "luyh",
                "migy",
              ].concat(
                process.env.NODE_ENV === "development" ? ["huangdh"] : []
              ),
              company1: ["wupf", "chenwx"].concat(
                process.env.NODE_ENV === "development" ? ["huangdh"] : []
              ),
            },
          },
          component: loadViews("YearAllGradeEvaluationApproveResult"),
        },
        {
          path: "/:year/:month/:companyGroupId/:positionGradeId/feedback/feedback",
          name: "管理人员综合能力【评定结果反馈】表",
          meta: {
            type: "url",
          },
          component: loadViews("feedback/feedback"),
        },
        // {
        //   path: "/:year/:month/:companyGroupId/:positionGradeId/feedback",
        //   name: "管理人员综合能力【评定结果反馈】",
        //   meta: {
        //     type: "menu",
        //   },
        //   component: loadViews("AssessmentComprehensiveAbility/Main"),
        //   children: [
        //     {
        //       path: "/:year/:month/:companyGroupId/:positionGradeId/feedback/feedback",
        //       name: "(高级经理职等以下)管理人员综合能力【评定结果反馈】操作表",
        //       meta: {
        //         type: "url",
        //       },
        //       component: loadViews("feedback/feedback"),
        //     },
        //   ],
        // },
        {
          path: "/:year/:month/:companyGroupId/:positionGradeId/feedback/feedbackInfo",
          name: "部门员工【评定结果确认】表",
          hidden: true,
          meta: {
            type: "url",
          },
          component: loadViews("feedback/feedbackInfo"),
        },
        {
          path: "/:year/12/:companyGroupId/0/allevaluationresult",
          name: "年度高级经理职等以上管理人员综合能力【评定结果审批】表",
          // name: "【全集团高级经理职等以上】年度管理人员综合能力【评定结果审批】表",
          // hidden:true,
          meta: {
            fullYear: true,
            type: "url",
            month: 12,
            managerType: 0,
            //谁有此菜单权限(按公司分类)
            personHasPermission: {
              // 1是companyCode 1表示集团总部
              // qinl", "luyh", "wupf
              company1: [""].concat(
                process.env.NODE_ENV === "development" ? ["huangdh"] : []
              ),
            },
          },
          component: loadViews("YearAllGradeEvaluationApproveResult"),
        },
        {
          path: "/:year/6/:companyGroupId/0/allevaluationresult2",
          name: "半年度高级经理职等以上管理人员综合能力【评定结果审批】表",
          // name: "【全集团高级经理职等以上】年度管理人员综合能力【评定结果审批】表",
          // hidden:true,
          meta: {
            fullYear: true,
            type: "url",
            month: 6,
            managerType: 0,
            //谁有此菜单权限(按公司分类)
            personHasPermission: {
              // 1是companyCode 1表示集团总部
              // qinl", "luyh", "wupf
              company1: [""].concat(
                process.env.NODE_ENV === "development" ? ["huangdh"] : []
              ),
            },
          },
          component: loadViews("YearAllGradeEvaluationApproveResult"),
        },
        {
          path: "/:year/:month/:companyGroupId/:positionGradeId/history",
          name: "管理人员综合能力评定（历年审批结果）【汇总查询】表",
          meta: {
            type: "url",
          },
          component: loadViews("history"),
        },
      ],
    },

    {
      path: "/:year/:month/rules",
      name: "评定规则",
      meta: {
        type: "menu",
      },
      component: loadViews("Container"),
      redirect: "/rules/description",
      children: [
        {
          path: "/:year/:month/1/rules/description",
          name: "管理人员评定说明",
          meta: {
            type: "url",
          },
          component: loadViews("EvaluationRules/EvaluationDescription"),
        },
        {
          path: "/:year/:month/2/trainee/studentDescr",
          name: "管培生评定说明",
          meta: {
            type: "url",
          },
          component: loadViews("Trainee/Assessor/StudentDescription"),
        },
      ],
    },
    // {
    //   path: "/",
    //   name: "全集团高级经理职等以上",
    //   meta: {
    //     type: "menu"
    //   },
    //   component: loadViews("Container"),
    //   redirect: "/yearmenu",
    //   children: [
    //     {
    //       path: "/:year/:month/:companyGroupId/setting",
    //       name: "管理人员综合能力级别、职级及比例配置",
    //       meta: {
    //         type: "menu",
    //         fullYear: true,
    //         traineeMenu: true
    //       },
    //       component: loadViews("ComprehensiveCapacityRankGrade/Main"),
    //       children: [
    //         {
    //           path: "/:year/:month/:companyGroupId/setting/grade",
    //           name: "管理人员综合能力级别配置表",
    //           meta: {
    //             type: "url"
    //           },
    //           component: loadViews(
    //             "ComprehensiveCapacityRankGrade/GradeSetting"
    //           )
    //         },
    //         {
    //           path: "/:year/:month/:companyGroupId/setting/rank",
    //           name: "管理人员综合能力职级配置表",
    //           meta: {
    //             type: "url"
    //           },
    //           component: loadViews("ComprehensiveCapacityRankGrade/RankSetting")
    //         },
    //         {
    //           path: "/:year/:month/:companyGroupId/setting/ratio",
    //           name: "管理人员综合能力级别、职级及比例标准表",
    //           meta: {
    //             type: "url"
    //           },
    //           component: loadViews("ComprehensiveCapacityRankGrade/RatioScale")
    //         }
    //       ]
    //     },
    //   ],
    // },
    // {
    //   path: '/flow',
    //   name: '流程说明',
    //   meta:{
    //     type:'menu',
    //   },
    //   component:loadViews('Container'),
    //   children:[
    //   ]
    // },
    {
      path: "/:year/:month/trainee",
      name: "各公司管培生",
      meta: {
        type: "menu",
        halfYear: true,
        traineeMenu: true,
        companys: [
          {
            name: "集团总部",
            code: "1",
          },
          {
            name: "销售公司",
            code: "5",
          },
          {
            name: "赣州纸业纸品",
            code: "2",
          },
          {
            name: "广西竹林",
            code: "3",
          },
          {
            name: "崇左纸业",
            code: "6",
          },
          // {
          //   name: "【社招】",
          //   code: "shezhao"
          // }
        ],
      },
      component: loadViews("Container"),
      children: [
        {
          path: "/:year/:month/Trainee/Staff",
          name: "管培生明细",
          meta: {
            type: "menu",
            hasCompanys: true,
          },
          component: loadViews("Trainee/Staff/Main"),
          children: [
            {
              path: "/:year/:month/Trainee/Staff/:companyGroupId/detail",
              name: "管培生明细表",
              meta: {
                type: "url",
                //month:6
              },
              component: loadViews("Trainee/Staff/PeopleDetail"),
            },
          ],
        },
        {
          path: "/:year/:month/Trainee/Staff/shezhao",
          name: "管培生明细表",
          hasCompanys: false,
          hidden: true,
          meta: {
            type: "url",
            //month:6
          },
          component: loadViews("Trainee/Staff/shezhao"),
        },
        {
          path: "/:year/:month/trainee/assessor",
          name: "管培生综合能力评定人",
          meta: {
            type: "menu",
            hasCompanys: true,
            appendMenu: [
              {
                path: "/:year/:month/trainee/assessor/years",
                name: "管培生【历年评定人员明细】查询表",
                meta: {
                  type: "url",
                },
              },
            ],
          },
          component: loadViews("Trainee/Assessor/Main"),
          children: [
            {
              path: "/:year/:month/trainee/assessor/:companyGroupId/detail",
              name: "管培生评定人明细表",
              meta: {
                type: "url",
              },
              component: loadViews("Trainee/Assessor/AssessorDetail"),
            },
            {
              path: "/:year/:month/trainee/assessor/years",
              name: "管培生【历年评定人员明细】查询表",
              hidden: true,
              meta: {
                type: "url",
              },
              component: loadViews("Trainee/Assessor/YearsAssessorDetail"),
            },
          ],
        },
        {
          path: "/:year/:month/trainee/assessor2",
          name: "管培生个人年度工作总结",
          meta: {
            type: "menu",
            hasCompanys: true,
          },
          component: loadViews("Container"),
          children: [
            {
              path: "/:year/:month/trainee/assessor2/:companyGroupId/detail2",
              name: "管培生【工作总结】表",
              meta: {
                type: "url",
              },
              component: loadViews("Trainee/Assessor/AssessorDetail2"),
            },
            {
              path: "/:year/:month/trainee/assessor2/:companyGroupId/summary",
              name: "年度工作总结",
              hidden: true,
              meta: {
                type: "url",
              },
              component: loadViews("Trainee/Assessor/summary"),
            },
          ],
        },
        {
          path: "/:year/:month/trainee/assessor3",
          name: "管培生年度述职演讲PPT",
          meta: {
            type: "menu",
            hasCompanys: true,
          },
          component: loadViews("Container"),
          children: [
            {
              path: "/:year/:month/trainee/assessor3/:companyGroupId/detail3",
              name: "管培生年度述职演讲PPT",
              meta: {
                type: "url",
              },
              component: loadViews("Trainee/Assessor/AssessorDetail3"),
            },
            // {
            //   path: "/:year/:month/trainee/assessor2/:companyGroupId/summary",
            //   name: "年度工作总结",
            //   hidden: true,
            //   meta: {
            //     type: "url",
            //   },
            //   component: loadViews("Trainee/Assessor/summary"),
            // },
          ],
        },
        {
          path: "/:year/:month/trainee/FlowStart_list",
          name: "管培生综合能力评定及跟踪",
          meta: {
            type: "menu",
            hasCompanys: true,
          },
          component: loadViews("Trainee/Staff/Main"),
          children: [
            {
              path: "/:year/:month/Trainee/Staff/:companyGroupId/FlowStart_list",
              name: "管培生【综合能力评定及跟踪】表",
              meta: {
                type: "url",
              },
              component: loadViews("Trainee/Staff/FlowStart_list"),
            },
          ],
        },

        {
          path: "/:year/:month/trainee/evaluation/score",
          name: "管培生综合能力评定得分明细",
          meta: {
            type: "menu",
            hasCompanys: true,
          },
          component: loadViews("Trainee/Evaluation/Score/Main"),
          children: [
            {
              path: "/:year/:month/trainee/evaluation/score/:companyGroupId/detail",
              name: "管培生综合能力评定得分明细表",
              meta: {
                type: "url",
                isview: "0",
              },
              component: loadViews("Trainee/Evaluation/Score/ScoreDetail"),
            },
          ],
        },
        {
          path: "/:year/:month/trainee/evaluation/:companyGroupId/joint",
          name: "管培生综合能力评定结果合议",
          meta: {
            type: "menu",
            hasCompanys: true,
          },
          component: loadViews(
            "Trainee/Evaluation/EvaluationJoint/EvaluationJointResultSummary"
          ),
          children: [
            {
              path: "/:year/:month/trainee/evaluation/:companyGroupId/allJoint",
              name: "管培生综合能力评定结果合议",
              meta: {
                noSchoolLevel: true,
                type: "url",
              },
              component: loadViews(
                "Trainee/Evaluation/EvaluationJoint/EvaluationJointResultSummary"
              ),
            },
          ],
        },

        {
          path: "/:year/:month/trainee/evaluation/meetingResult",
          name: "管培生综合能力评定【合议结果】审批表",
          meta: {
            type: "url",
          },
          component: loadViews(
            "Trainee/Evaluation/EvaluationJoint/meetingResult"
          ),
        },

        {
          path: "/:year/:month/Trainee/Evaluation/:companyGroupId/EvaluationJoint",
          name: "管培生综合能力评定【历年审批结果】查询",
          meta: {
            type: "menu",
            // hasCompanys: true
          },
          component: loadViews("AssessmentComprehensiveAbility/Main"),
          children: [
            {
              path: "/:year/:month/trainee/evaluation/EvaluationJoint/history",
              name: "管培生综合能力评定（历年审批结果）【汇总查询】表",
              meta: {
                type: "url",
              },
              component: loadViews(
                "Trainee/Evaluation/EvaluationJoint/history"
              ),
            },
            {
              path: "/:year/:month/trainee/evaluation/EvaluationJoint/ResultQuery",
              name: "管培生综合能力评定（历年审批结果）【明细查询】表",
              meta: {
                type: "url",
              },
              component: loadViews(
                "Trainee/Evaluation/EvaluationJoint/ResultQuery"
              ),
            },
          ],
        },

        {
          path: "/:year/:month/trainee/evaluation/studentApprove",
          name: "管培生审批提示",
          hidden: true,
          meta: {
            form: "edit",
          },
          component: loadViews(
            "Trainee/Evaluation/EvaluationJoint/StudentApprove"
          ),
        },

        {
          path: "/:year/:month/trainee/Staff/:companyGroupId/talk",
          name: "管培生综合能力【评定结果面谈】",
          meta: {
            type: "menu",
            hasCompanys: true,
          },
          component: loadViews("Trainee/Staff/AblityTalk_list"),
          children: [
            {
              path: "/:year/:month/trainee/Staff/:companyGroupId/talklist",
              name: "管培生综合能力【评定结果面谈】",
              meta: {
                noSchoolLevel: true,
                type: "url",
              },
              component: loadViews("Trainee/Staff/AblityTalk_list"),
            },
          ],
        },

        {
          path: "/:year/:month/Trainee/Staff/:companyGroupId/FlowStartDetails_list",
          name: "管培生【综合能力评定操作】明细表",
          hidden: true,
          meta: {
            form: "edit",
          },
          component: loadViews("Trainee/Staff/FlowStartDetails_list"),
        },
        {
          path: "/:year/:month/Trainee/Staff/:companyGroupId/FlowStartDetails_point1",
          name: "成功要素评分方式",
          hidden: true,
          meta: {
            form: "edit",
          },
          component: loadViews("Trainee/Staff/FlowStartDetails_point1"),
        },
        {
          path: "/:year/:month/Trainee/Staff/:companyGroupId/FlowStartDetails_point2",
          name: "综合分评分方式",
          hidden: true,
          meta: {
            form: "edit",
          },
          component: loadViews("Trainee/Staff/FlowStartDetails_point2"),
        },
        {
          path: "/:year/:month/Trainee/Staff/:companyGroupId/viewGoodAbsence",
          name: "综合能力评定【优缺点】查看表",
          hidden: true,
          meta: {
            form: "edit",
          },
          component: loadViews("Trainee/Staff/ViewGoodAbsence"),
        },
        {
          path: "/:year/:month/Trainee/Evaluation/:companyGroupId/addGoodsAbsence",
          name: "综合评价填写积极态度、稳定性、优点、缺点",
          hidden: true,
          meta: {
            form: "edit",
          },
          component: loadViews(
            "Trainee/Evaluation/EvaluationJoint/meetingGoodsAbsence"
          ),
        },
        {
          path: "/:year/:month/Trainee/Evaluation/ScoreDetail_all",
          name: "所有公司管培生得分明细合并显示",
          hidden: true,
          meta: {
            form: "edit",
          },
          component: loadViews("Trainee/Evaluation/Score/ScoreDetail_all"),
        },
        {
          path: "/:year/:month/Trainee/Staff/:companyGroupId/talkRecord",
          name: "年度综合能力评定结果谈话记录查询表",
          hidden: true,
          meta: {
            form: "edit",
          },
          component: loadViews("Trainee/Staff/AblityTalkRecord"),
        },
        {
          path: "/:year/:month/:companyGroupId/trainee/baseLevelList",
          name: "XX公司评定级别、职级标准表",
          hidden: true,
          meta: {
            form: "edit",
          },
          component: loadViews("BaseLevel_list.vue"),
        },
        {
          path: "/:year/:month/trainee/evaluation/meetingLastResult",
          name: "管培生综合能力评定【合议结果】上期审批结果",
          hidden: true,
          meta: {
            type: "url",
          },
          component: loadViews(
            "Trainee/Evaluation/EvaluationJoint/meetingLastResult"
          ),
        },
      ],
    },
    {
      path: "/404",
      component: loadViews("NotFoundPage"),
      name: "404未找到",
      meta: {
        title: "404未找到",
      },
    },
    {
      path: "*",
      component: loadViews("NotFoundPage"),
      name: "404",
      meta: {
        title: "404未找到",
      },
    },
  ],
});
