import router from '@/router'
import NProgress from 'nprogress'
import { get<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,set<PERSON><PERSON><PERSON>,remove<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON> } from '@/utils/Cookie'
import { getAuthGroup } from '@/api'

const checkAuthApp = ()=>{
    /**
    const apps = JSON.parse(getCookie(AppsKey)||'[]');
    let result = apps.find(app=>{
        return app.fdKey == process.env.PROJECT_CODE && app.fdIsAdmin;
    })
    */
   return new Promise((resolve,reject)=>{
    getAuthGroup().then(res=>{
        let result = false;
        let loginUser = JSON.parse(getCookie(UserKey) || "{}");
        /*if(loginUser&&loginUser.loginName){
            if(res.managerGroup.indexOf(loginUser.loginName)!== -1||res.traineeGroup.indexOf(loginUser.loginName)!== -1){
                result = true;
            }
        }
        if(!result){
            layer.alert('对不起，当前登录账号没有权限登录本系统！',{icon: 2},function(index){
                window.location.href=process.env.LOGIN_PATH+window.location.href;
                layer.close(index);
            })
        }
        resolve(result)*/
        resolve(true);  /*去掉用户名校验 */
    })
   })
}
router.beforeEach(async (to, from, next) => {
    NProgress.start()
    if (!getCookie(TokenKey)) { 
        window.location.href=process.env.LOGIN_PATH+window.location.href;
    }
    let auth_result = await checkAuthApp();
    if(auth_result){
        if(['界面容器','系统菜单','年度菜单','404未找到','404',null].indexOf(from.name) === -1||(from.name == '系统菜单' && ['界面容器','年度菜单'].indexOf(to.name)!==-1)){
            setCookie(FromUrlKey,window.location.href);
        }
        $("html").scrollTop(0);
        next();
    }
    
});

router.afterEach((to,from)=>{
    if(['404未找到','404'].indexOf(from.name) !==-1){
        removeCookie(FromUrlKey);
    }
})