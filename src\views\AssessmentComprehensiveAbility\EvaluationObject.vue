<template>
  <div class="evaluation-object-container">
    <div class="row">
      <div class="col-xs-12 text-left">
        <h4 class="col-xs-6">
          {{ this.$route.params.year }}{{ this.month == `6` ? `半` : `` }}年度{{
            getCompanyName()
          }}（{{ getGradeName() }}）{{ $route.name }}
        </h4>
        <div class="col-xs-6 text-right" style="line-height: 40px">
          <button
            type="button"
            class="btn btn-primary btn-xs"
            @click="showDescrDialog"
          >
            使用说明
          </button>
          <button
            type="button"
            class="btn btn-primary btn-xs"
            :disabled="disabledButton"
            @click="showMergeDialog"
            v-if="
              $store.state.doubleCol.arrAuths
                .point_ablity_updateMergePositionGrade
            "
          >
            合并评定单位
          </button>
          <button
            type="button"
            class="btn btn-primary btn-xs"
            :disabled="disabledButton"
            @click="_updatePositionGradeLong()"
            v-if="
              $store.state.doubleCol.arrAuths
                .point_ablity_updateMergePositionGrade
            "
          >
            更新职等及工龄
          </button>
          <button
            class="btn btn-primary btn-xs"
            @click="
              $router.push(
                `/${$route.params.year}/${$route.params.month}/menu/1`
              )
            "
          >
            返回目录
          </button>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-xs-12">
        <div class="list-table">
          <div class="scroll-table" style="height: 100%; overflow-x: hidden">
            <table class="scroll-table-header table-bordered">
              <colgroup>
                <col style="width: 40px" />
                <col style="width: 100px" />
                <col style="width: 70px" />
                <col style="width: 40px" />
                <col style="width: 40px" />
                <col style="width: 50px" />
                <col style="width: 130px" />
                <col style="width: 100px" />
                <col style="width: 80px" />
                <col style="width: 80px" />
                <col style="width: 200px" />
              </colgroup>
              <thead>
                <tr>
                  <th>序号</th>
                  <th>部门</th>
                  <th>姓名</th>
                  <th>性别</th>
                  <th>年龄</th>
                  <th>学历</th>
                  <th>岗位</th>
                  <th>职等</th>
                  <th>华劲工龄</th>
                  <th>职等工龄</th>
                  <th>备注</th>
                </tr>
              </thead>
            </table>
            <div
              class="scroll-table-body"
              :style="{
                overflow: 'auto',
                maxHeight: body_height - 130 + 'px',
                'overflow-y': 'scroll',
                'overflow-x': 'hidden',
              }"
            >
              <table
                style="
                  margin: 0px 10px 0 0;
                  position: relative;
                  font-size: 13px;
                  float: left;
                  border: none;
                "
                class="table table-bordered table-hover table-striped"
              >
                <colgroup>
                  <col style="width: 40px" />
                  <col style="width: 100px" />
                  <col style="width: 70px" />
                  <col style="width: 40px" />
                  <col style="width: 40px" />
                  <col style="width: 50px" />
                  <col style="width: 130px" />
                  <col style="width: 100px" />
                  <col style="width: 80px" />
                  <col style="width: 80px" />
                  <col style="width: 200px" />
                </colgroup>
                <tbody>
                  <tr
                    v-for="(data, $index) in tableData"
                    :key="$index"
                    @dblclick="
                      data.confirmLeaderId ||
                      (data.hwagainLong < 12 && data.status == 0)
                        ? null
                        : showMergeOneDialog(data)
                    "
                  >
                    <td>{{ $index + 1 }}</td>
                    <td>{{ data.deptName }}</td>
                    <td>{{ data.userName }}</td>
                    <td>{{ data.gender }}</td>
                    <td>{{ data.age }}</td>
                    <td>{{ data.degree }}</td>
                    <td>{{ data.jobName }}</td>
                    <td>{{ data.gradeName }}</td>
                    <td>
                      {{
                        Math.floor(data.hwagainLong / 12) <= 0
                          ? ""
                          : Math.floor(data.hwagainLong / 12) + "年"
                      }}{{
                        data.hwagainLong % 12 === 0
                          ? ""
                          : (data.hwagainLong % 12) + "个月"
                      }}
                    </td>
                    <td>
                      {{
                        Math.floor(data.workLong / 12) <= 0
                          ? ""
                          : Math.floor(data.workLong / 12) + "年"
                      }}{{
                        data.workLong % 12 === 0
                          ? ""
                          : (data.workLong % 12) + "个月"
                      }}
                    </td>
                    <td style="text-align: left">
                      {{
                        data.status == 0
                          ? data.remark == "不进行评定"
                            ? "不进行评定：" + (data.bakRemark || "")
                            : data.remark
                          : data.mergeRemark
                      }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  findDeptMemberDetailListByCompanyGroupId,
  findPositionGradeByCompanyGroupId,
  updateMergePositionGrade,
  updatePositionGradeLong,
} from "@/api";

const deptAreaSelectHtml = `<select id="deptArea" class="form-control input-sm" style="width:120px;display:inline-block;float: left;margin-left: 10px;">
  <option value=''>请选择</option>
  <option value='A'>生产一线</option>
  <option value='B'>生产辅助</option>
  <option value='C'>生产支持</option>
  </select>`;
export default {
  data() {
    return {
      isLoading: true,
      isAuths: false,
      body_height: 0,
      tableData: [],
      dropdownList: [],
      companyGroupId: "",
      year: "",
      month: "",
      positionGradeId: "",
      deptArea: Utils.getQueryParams("deptArea"),
    };
  },

  computed: {
    disabledButton() {
      if (this.isLoading || this.tableData.length == 0) {
        return true;
      } else {
        let flag = true;
        this.tableData.forEach((item) => {
          if (!item.confirmLeaderId && item.status != 0) {
            flag = false;
          }
        });
        return flag;
      }
    },
  },
  methods: {
    getCompanyName() {
      let companyName = Utils.getCompanyNameByGroupId(this.companyGroupId);
      if (
        this.positionGradeId == "0006" ||
        this.positionGradeId == "0007" ||
        this.positionGradeId == "0032" ||
        this.positionGradeId == "0033"
      ) {
        companyName = "全集团";
      }
      return companyName;
    },
    getGradeName() {
      let name = Utils.getGradeLevlByGradeId(this.positionGradeId);
      if (this.deptArea == "A") {
        name = name + "-生产一线";
      } else if (this.deptArea == "B") {
        name = name + "-生产辅助";
      } else if (this.deptArea == "C") {
        name = name + "-生产支持";
      }
      return name;
    },

    _findDeptMemberDetailListByCompanyGroupId() {
      /*先判断有权限才可以查询(modify by huangdh@2019-05-24)*/
      if (
        this.$store.state.doubleCol.arrAuths
          .point_ablity_findListByCompanyGroupId
      ) {
        findDeptMemberDetailListByCompanyGroupId({
          companyGroupId: this.companyGroupId,
          year: this.year,
          month: this.month,
          positionGradeId: this.positionGradeId,
          deptArea: this.deptArea,
        })
          .then((res) => {
            this.isLoading = false;
            if (!res.success) return;
            this.tableData = res.data ? res.data || [] : [];
          })
          .catch((err) => {
            layer.msg(`${err}`, { icon: 2, shade: 0.3, shadeClose: true });
          });
      } else {
        layer.msg(`对不起，您没有权限查询本界面.`, {
          icon: 2,
          shade: 0.3,
          shadeClose: true,
        });
      }
    },
    _findPositionGradeByCompanyGroupId() {
      findPositionGradeByCompanyGroupId({ companyGroupId: this.companyGroupId })
        .then((res) => {
          this.dropdownList = res.data || [];
        })
        .catch((err) => {
          layer.msg(`${err}`, { icon: 2, shade: 0.3, shadeClose: true });
        });
    },

    /*合并评定单位*/
    _updateMergePositionGrade(itemList, layerindex) {
      if (this.disabledButton) {
        return;
      }
      this.isLoading = true;
      updateMergePositionGrade(itemList)
        .then((res) => {
          let result = res.success;
          layer.msg(`评定${result ? "成功" : "失败"}`, {
            icon: result ? 1 : 2,
            shade: 0.3,
            shadeClose: true,
          });
          if (result) {
            this._findDeptMemberDetailListByCompanyGroupId();
            layer.close(layerindex);
          }
        })
        .catch((err) => {
          layer.msg(`${err.msg}`, { icon: 2, shade: 0.3, shadeClose: true });
          layer.close(layerindex);
        });
    },

    showMergeOneDialog(data) {
      /*一个人*/
      let that = this;
      Utils.layerBox.layerDialogOpen({
        area: ["600px", "250px"],
        content: `<div style="width:100%;height:100%;text-align:center;line-height:32px;box-sizing: border-box;padding: 12px 0">
          <div class="row" style="margin:0" id="mergeType">
            <label class="col-xs-5 text-right">是否评定：</label>
            <div style="text-align:left;">
              <span style="margin-right:20px;"><input type="radio" name="mergeType" checked value="1"/>进行评定</span>
              <span style="margin-right:20px;"><input type="radio" name="mergeType" value="0"/>不进行评定</span>
            </div>
          </div>
          <div class="row" style="margin:0" id="mergeSelect1">
            <label class="col-xs-5 text-right">与之合并的评定公司：</label>
            <select id="mergeCompany" class="form-control input-sm" style="width:120px;display:inline-block;float: left;">
              <option value=''>请选择</option>
              <option value='10002'>集团总部</option>
              <option value='11000'>销售公司</option>
              <option value='16000'>赣州纸业纸品</option>
              <option value='15000'>广西竹林</option>
              <option value='14000'>赣州竹林</option>
              <option value='22747'>崇左纸业</option>
            </select>
          </div>
          <div class="row" style="margin:0" id="mergeSelect2">
            <label class="col-xs-5 text-right">与之合并的评定职等：</label>
            <select id="mergeGradeType" class="form-control input-sm" style="width:120px;display:inline-block;float: left;">
              ${that.dropdownList
                .map(
                  (item) =>
                    `<option value="${item.fd_id}">${item.grade_name}</option>`
                )
                .join("\n")}
            </select>
            ${that.deptArea ? deptAreaSelectHtml : ""}
          </div>
          <div class="row" style="margin:0;text-align:left;display:none;" id="mergeReason">
            <label class="col-xs-5 text-right">不进行评定的理由：</label>
            <span> <textarea id="txt_bakRemark" style="width:300px;height:60px;" >${
              data.bakRemark == null ? "" : data.bakRemark
            }</textarea> </span>
          </div>
        </div>`,
        success: function (layero) {
          $("#mergeType span>input[type='radio']").on("change", function (e) {
            if ($(this).val() == 1) {
              $("#mergeSelect1").show();
              $("#mergeSelect2").show();
              $("#mergeReason").hide();
            } else {
              $("#mergeSelect1").hide();
              $("#mergeSelect2").hide();
              $("#mergeReason").show();
            }
          });
        },
        btn1: function (index) {
          let itemList = [];
          if ($("input[name='mergeType']:checked").val() == 1) {
            if (`${$("#mergeCompany").val()}` == "") {
              layer.msg(`请选择与之合并的评定公司.`, {
                icon: 2,
                shade: 0.3,
                shadeClose: true,
              });
              return;
            }

            itemList.push({
              fdId: data.fdId,
              companyId: `${$("#mergeCompany").val()}`,
              mergePositionGradeId: `${$("#mergeGradeType").val() || ""}`,
              mergeDeptArea: `${
                that.deptArea ? `${$("#deptArea").val()}` : ""
              }`,
              mergeRemark: `更换评定单位`,
            });
          } else {
            let reason = $("#txt_bakRemark").val();
            if (reason == null || reason == "") {
              layer.msg(`选择【不进行评定】必须录入理由.`, {
                icon: 2,
                shade: 0.3,
                shadeClose: true,
              });
              return;
            }
            itemList.push({
              fdId: data.fdId,
              remark: `不进行评定`,
              bakRemark: reason,
            });
          }
          that._updateMergePositionGrade(itemList, index);
        },
      });
    },
    showMergeDialog() {
      /*一个职等所有人*/
      let that = this;
      Utils.layerBox.layerDialogOpen({
        area: ["500px", "160px"],
        content: `<div style="width:100%;height:100%;text-align:center;line-height:32px;box-sizing: border-box;padding: 12px 0">
          <div class="row" style="margin:0">
            <label class="col-xs-5 text-right">与之合并的评定单位：</label>
            <select id="mergeGradeType" class="form-control input-sm" style="width:120px;display:inline-block;">
              ${that.dropdownList
                .map(
                  (item) =>
                    `<option value="${item.fd_id}">${item.grade_name}</option>`
                )
                .join("\n")}
            </select>
            ${that.deptArea ? deptAreaSelectHtml : ""}
          </div>
        </div>`,
        btn1: function (index) {
          let itemList = [];
          that.tableData.forEach(({ fdId, companyId }) => {
            itemList.push({
              fdId: fdId,
              companyId: companyId,
              mergePositionGradeId: `${$("#mergeGradeType").val() || ""}`,
              mergeDeptArea: `${
                that.deptArea ? `${$("#deptArea").val()}` : ""
              }`,
              mergeRemark: `与${Utils.getGradeGradeNameByGradeId(
                $("#mergeGradeType").val()
              )}合并评定`,
            });
          });
          that._updateMergePositionGrade(itemList, index);
        },
      });
    },

    _updatePositionGradeLong() {
      if (this.disabledButton) {
        return;
      }
      layer.confirm(
        "确认要更新职等及工龄吗？",
        {
          title: "提示",
          btn: ["确认", "取消"], //按钮
        },
        (layerindex) => {
          this.isLoading = true;
          updatePositionGradeLong({
            fdYear: this.year,
            fdMonth: this.month,
            companyGroupId: this.companyGroupId,
          })
            .then((res) => {
              let result = res.success;
              layer.msg(`更新${result ? "成功" : "失败"}`, {
                icon: result ? 1 : 2,
                shade: 0.3,
                shadeClose: true,
              });
              if (result) {
                this._findDeptMemberDetailListByCompanyGroupId();
                layer.close(layerindex);
              }
            })
            .catch((err) => {
              layer.msg(`${err.msg}`, {
                icon: 2,
                shade: 0.3,
                shadeClose: true,
              });
              layer.close(layerindex);
            });
        }
      );
    },
    showDescrDialog() {
      Utils.layerBox.layerDialogOpen({
        title: "使用说明",
        area: ["670px", "465px"],
        btn: ["关闭"],
        content: `<div style="padding:10px 20px;margin:0 auto;">
          <p style="line-height:24px;">
          1、本表作用：<br/>
            &nbsp;&nbsp;①用于查询该职等需评定的人员明细；<br/>
            &nbsp;&nbsp;②用于将该职等的所有人员与其他职等进行合并评定的操作；<br/>
            &nbsp;&nbsp;③用于个别特殊人员不进行评定或调整到其它职等进行评定的操作；<br/>
          2、将该职等所有人员与其它职等进行合并评定：点击“合并评定单位”键，选择确认合并的职等名称，操作成功后评定人员信息会自动转移到<br/>
            合并的职等人员明细表单，并在其备注中显示“与××职等合并评定”。<br/>
          3、个别特殊人员不评定或调整到其他职等进行评定：鼠标左键双击该人员所在行，弹框显示选择“进行评定”和“不进行评定”，若勾选“进
            行评定”，选择确定调整后的职等即可，操作成功后会自动显示在调整后的职等评定对象明细表中。若勾选“不进行评定”，确定后在备注栏中
            自动显示“不进行评定”的字样。<br/>
          4、不进行评定人员：<br/>
            &nbsp;&nbsp;（1）入职未满12个月的原则上不列入到当次的评定。<br/>
            &nbsp;&nbsp;（2）信息部的软件开发人员：在每年在12月份进行评定，不与管理人员一起评定。<br/>
            &nbsp;&nbsp;（3）管培生：指的是执行管培生薪酬体系的人员单独进行评定，不与管理人员一起评定。<br/>
          </p></div>`,
      });
    },

    initPage() {
      this.pageResize();
      const { companyGroupId, year, month, positionGradeId } =
        this.$route.params;
      this.companyGroupId = companyGroupId;
      this.year = year;
      this.month = month;
      this.positionGradeId = positionGradeId;
      this._findDeptMemberDetailListByCompanyGroupId();
      this._findPositionGradeByCompanyGroupId();
    },
    pageResize() {
      this.body_height = $(window).height();
    },
  },
  watch: {
    $route: function () {
      this.initPage();
    },
  },
  mounted() {
    this.initPage();
    $(window).resize(this.pageResize);
    console.log(this.$store.state.doubleCol.arrAuths);
  },
};
</script>

<style scoped>
/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}
.list-year-data {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
.scroll-table-body {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
</style>
