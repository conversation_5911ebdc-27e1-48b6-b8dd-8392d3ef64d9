<template>
  <div class="deputy-director-container">
    <div class="row">
        <div class="col-xs-12 text-left">
          <h4 class="col-xs-6">【{{getCompanyName()}}】入职满三年本科学历副主任级以下人员能力评定</h4>
          <div class="col-xs-6 text-right" style="line-height:40px;">
            <button class="btn btn-primary btn-xs" @click="showDescription()">评定说明</button>
            <button type="button" class="btn btn-primary btn-xs" :disabled="disabledButton" @click="_updateRisePosition(0)"
                v-if="$store.state.doubleCol.arrAuths.point_ablity_updateRisePosition" >保存</button>
            <button type="button" class="btn btn-primary btn-xs" :disabled="disabledButton" @click="_updateRisePosition(1)"
                v-if="$store.state.doubleCol.arrAuths.point_ablity_updateRisePosition" >评定完成</button>
            <button class="btn btn-primary btn-xs" @click="$router.push(`/${$route.params.year}/12/menu/1`)">返回目录</button>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-xs-12">
          <div class="list-table">
            <div class="scroll-table" style="height:100%;overflow-x:hidden;">
                <table class="scroll-table-header table-bordered">
                  <colgroup>
                    <col style="width:40px"/>
                    <col style="width:80px"/>
                    <col style="width:60px"/>
                    <col style="width:40px"/>
                    <col style="width:110px"/>
                    <col style="width:110px"/>
                    <col style="width:80px"/>
                    <col style="width:70px"/>
                    <col style="width:70px"/>
                    <col style="width:80px"/>
                    <col style="width:250px"/>
                  </colgroup>
                  <thead>
                    <tr>
                      <th>序号</th>
                      <th>厂/部门</th>
                      <th>姓名</th>
                      <th>性别</th>
                      <th>岗位</th>
                      <th>职务</th>
                      <th>职等</th>
                      <th>华劲工龄</th>
                      <th>任职时长</th>
                      <th>能否升副主任</th>
                      <th>备注</th>
                    </tr>
                  </thead>
                </table>
                <div class="scroll-table-body" :style="{overflow:'auto',maxHeight:body_height - 130 + 'px','overflow-y':'scroll','overflow-x':'hidden'}">
                    <table style="margin:0px 10px 0 0;position: relative;font-size:13px;float:left;border:none;" class="table table-bordered table-hover table-striped">
                        <colgroup>
                          <col style="width:40px"/>
                          <col style="width:80px"/>
                          <col style="width:60px"/>
                          <col style="width:40px"/>
                          <col style="width:110px"/>
                          <col style="width:110px"/>
                          <col style="width:80px"/>
                          <col style="width:70px"/>
                          <col style="width:70px"/>
                          <col style="width:80px"/>
                          <col style="width:250px"/>
                        </colgroup>
                        <tbody>
                          <tr v-for="(data,$index) in tableData" :key="$index">
                            <td>{{$index+1}}</td>
                            <td>{{data.deptName||''}}</td>
                            <td>{{data.userName||''}}</td>
                            <td>{{data.gender||''}}</td>
                            <td>{{data.jobName||''}}</td>
                            <td>{{data.positionName||''}}</td>
                            <td>{{data.gradeName||''}}</td>
                            <td>{{Math.floor(data.hwagainLong/12) <= 0 ? '' : (Math.floor(data.hwagainLong/12)+'年')}}{{data.hwagainLong%12===0?'':(data.hwagainLong%12+'个月')}}</td>
                            <td>{{Math.floor(data.workLong/12) <= 0 ? '' : (Math.floor(data.workLong/12)+'年')}}{{data.workLong%12===0?'':(data.workLong%12+'个月')}}</td>
                            <td>
                              <select class="form-control input-sm" v-model="data.isRisePosition" style="width:90%;">
                                <option :value="null">请选择</option>
                                <option :value="1">是</option>
                                <option :value="2">否</option>
                              </select>
                            </td>
                            <td> <!-- {{data.mergeRemark||''}} -->
                              <!-- <input type="text" v-model="data.isRiseRemark" class="form-control input-sm" style="height:40px;"/> -->
                              <textarea v-model="data.isRiseRemark" style="height:50px;width:100%;"></textarea>
                            </td>
                          </tr>
                      </tbody>
                    </table>
                </div>
            </div>
          </div>
        </div>
      </div>
  </div>
</template>

<script>
import { findUpWorkThreeYear,updateRisePosition } from '@/api'
export default {
  data(){
    return {
      isLoading:true,
      body_height:0,
      tableData:[],
      companyGroupId:'',
      year:'',
      month:'12',
    }
  },
  computed:{
    disabledButton(){
      if(this.isLoading||(!this.tableData||this.tableData.length==0)){
        return true;
      }
      else if(this.tableData&&this.tableData.length!=0){
        // return this.tableData[0].isRisePosition ? true : false;
        return this.tableData[0].isRiseConfirm==1 ? true : false;
      }
      return false
    }
  },
  methods:{
    getCompanyName(){
      return Utils.getCompanyNameByGroupId(this.companyGroupId);
    },
    _updateRisePosition(status){
      if(this.disabledButton){
        return;
      }
      this.isLoading = true;
      let params = [];
      this.tableData.forEach(item=>{
        params.push(item.fdId+"^"+item.isRisePosition+"^"+item.isRiseRemark);
      })
      updateRisePosition({itemList:params.join("|"),isRiseConfirm:status}).then(res=>{
        let result = res.success;
        layer.msg(`评定${result ? '成功' : '失败'}`, {icon: result ? 1 : 2,shade:0.3,shadeClose:true});
        this._findUpWorkThreeYear();
      })
    },
    _findUpWorkThreeYear(){
      /*先判断有权限才可以查询(modify by huangdh@2019-05-24)*/
      if (this.$store.state.doubleCol.arrAuths.point_ablity_findUpWorkThreeYear)
      {
          findUpWorkThreeYear({CompanyGroupId:this.companyGroupId,Year:this.year,Month:this.month}).then(res=>{
            this.isLoading = false;
            if(!res.success) return;
            this.tableData = res.data ? res.data : [];
          })
      }
      else {
          layer.msg(`对不起，您没有权限查询本界面.`, {icon: 2,shade:0.3,shadeClose:true});
      }
    },
    showDescription(){
      Utils.layerBox.layerDialogOpen({
          title:'评定人资格说明',
          area:['480px','320px'],
          btn:['关闭'],
          content:`<div style="padding:10px 20px;margin:0 auto;">
          <p style="line-height:24px;">
            1、原则：入职3年以上的本科学历人员需达到副主任等以上能力，否则需作离职处理。<br/>
            2、评定目的：确定该部分人员是否达到副主任等人员能力，达到的需与现副主任等人员一起进行综合能力级别与职级评定。<br/>
            3、评定组织：人力资源部。<br/>
            4、评定时间：在综合能力级别评定前组织评定。<br/>
            5、评定办法：现场合议评定。<br/>
            6、评定人员：评定对象的部门主管及上级领导。<br/>
          </p></div>`});
    },
    initPage(){
      this.pageResize();
      const {companyGroupId,year} = this.$route.params;
      this.companyGroupId = companyGroupId;
      this.year = year;
      this._findUpWorkThreeYear();
    },
    pageResize(){
      this.body_height = $(window).height();
    }
  },
  watch:{
    "$route":function(){
      this.initPage();
    }
  },
  mounted(){
    this.initPage();
    $(window).resize(this.pageResize);
  }
}
</script>

<style scoped>
/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}
.list-year-data {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
.scroll-table-body {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
</style>
