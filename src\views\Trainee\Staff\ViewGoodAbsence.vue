<template>
    <div class="grade-evaluation-dialog-container">
        <div class="row">
            <div style="font-size:20px;font-weight:bold;">
                {{ this.userName }}综合能力评定【积极性、稳定性及优点与不足】评价明细表</div>
            <div class="col-xs-12">
                <div style="text-align:left;float:left;margin-top:10px;">
                    <span style="margin-left:30px;font-weight:bold;">姓名：</span>{{ this.userName }}
                    <span style="margin-left:60px;font-weight:bold;">部门：</span>{{ this.deptName }}
                    <span style="margin-left:60px;font-weight:bold;">职务：</span>{{ this.jobName }}
                </div>
                <div style="float:right;margin-right:50px;margin-top:10px;margin-bottom:5px;">
                    <button type="button" class="btn btn-primary btn-xs" @click="$router.go(-1)"> 返回上级</button>
                </div>

                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th rowspan="2" style="width:50px;">序号</th>
                            <th rowspan="2" style="width:80px;">评定人</th>
                            <th colspan="2">积极性</th>
                            <th colspan="2">稳定性</th>
                            <th colspan="2">优点与不足</th>
                        </tr>
                        <tr>
                            <th style="width:80px;">评定结论</th>
                            <th style="width:250px;">判定理由</th>
                            <th style="width:80px;">评定结论</th>
                            <th style="width:250px;">判定理由</th>
                            <th style="width:300px;">优点</th>
                            <th style="width:200px;">不足</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(data, $index) in tableData" :key="$index">
                            <td style="text-align:centre;">{{ $index + 1 }}</td>
                            <td style="text-align:centre;">{{ data.raterName }}</td>
                            <td style="text-align:centre;">
                                {{ data.energyPoint == null ? "" : (data.energyPoint >= 70 ? "积极" : "不积极") }}</td>
                            <td style="text-align:left;">{{ data.energyReason }}</td>
                            <td style="text-align:centre;">{{ data.stablity }}</td>
                            <td style="text-align:left;">{{ data.stablityReason }}</td>
                            <td style="text-align:left;" v-html="showText(data, 1)"></td>
                            <td style="text-align:left;" v-html="showText(data, 2)"></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div style="margin-left:20px;text-align:left;">
                <strong>说明：</strong><br />
                1、优点：汇总评定人评定为优、A选项的案例说明<br />
                2、不足：汇总评定人评定D选项的案例说明<br />
            </div>

        </div>
    </div>
</template>


<script>
import { findOne, findListByAblityId } from "@/api";
export default {
    data() {
        return {
            body_height: 0,
            tableData: [],
            userName: "",
            deptName: "",
            jobName: ""
        };
    },


    methods: {
        findList() {
            findListByAblityId({ ablityId: this.$route.query.ablityId }).then(res => {
                if (!res.success) return;
                this.tableData = res.data ? res.data : [];
                if (this.tableData.length > 0) {
                    this.userName = this.tableData[0].userName;
                    this.deptName = this.tableData[0].deptName;
                    this.jobName = this.tableData[0].jobName;
                }
            }).catch(err => {
                layer.msg(`${err.msg}`, { icon: 2, shade: 0.3, shadeClose: true });
            });
        },
        converText(key, textValue) {
            this.$refs[key].innerHTML = textValue;
        },
        showText(data, type) {
            if (data.status < 30) {
                return "";
            }
            let index = 1;
            let infoText = "";
            if (data.judgeType == "综合分") {
                if (type == 1 && data.totalPoint >= 75) {
                    infoText = data.textTotal;
                }
                else if (type == 2 && data.totalPoint < 75) {
                    infoText = data.textTotal;
                }
                return infoText;
            }
            //<pre></pre>
            else if (type == 1) {
                if (data.mindAction >= 75) {
                    infoText = index + "、果断的思维与行动：" + (data.textAction || '') + "<br/>";
                    index++;
                }
                if (data.sharpEye >= 75) {
                    infoText = infoText + index + "、洞察力：" + (data.textEye || '') + "<br/>";
                    index++;
                }
                if (data.mindSkill >= 75) {
                    infoText = infoText + index + "、巧用专长：" + (data.textSkill || '') + "<br/>";
                    index++;
                }
                if (data.groupImprove >= 75) {
                    infoText = infoText + index + "、提高能力：" + (data.textImprove || '') + "<br/>";
                    index++;
                }
                if (data.sharpDiscipline >= 75) {
                    infoText = infoText + index + "、纪律性：" + (data.textDiscipline || '') + "<br/>";
                    index++;
                }
                if (data.groupLeader >= 75) {
                    infoText = infoText + index + "、领导力：" + (data.textLeader || '') + "<br/>";
                    index++;
                }
                if (data.mindNew >= 75) {
                    infoText = infoText + index + "、创新与借鉴：" + (data.textNew || '') + "<br/>";
                    index++;
                }
                if (data.sharpEmbrace >= 75) {
                    infoText = infoText + index + "、拥抱变化：" + (data.textEmbrace || '') + "<br/>";
                    index++;
                }
                if (data.groupCooperate >= 75) {
                    infoText = infoText + index + "、营造多样化：" + (data.textCooperate || '') + "<br/>";
                    index++;
                }
            }
            else if (type == 2) {
                if (data.mindAction < 75) {
                    infoText = index + "、果断的思维与行动：" + (data.textAction || '') + "<br/>";
                    index++;
                }
                if (data.sharpEye < 75) {
                    infoText = infoText + index + "、洞察力：" + (data.textEye || '') + "<br/>";
                    index++;
                }
                if (data.mindSkill < 75) {
                    infoText = infoText + index + "、巧用专长：" + (data.textSkill || '') + "<br/>";
                    index++;
                }
                if (data.groupImprove < 75) {
                    infoText = infoText + index + "、提高能力：" + (data.textImprove || '') + "<br/>";
                    index++;
                }
                if (data.sharpDiscipline < 75) {
                    infoText = infoText + index + "、纪律性：" + (data.textDiscipline || '') + "<br/>";
                    index++;
                }
                if (data.groupLeader < 75) {
                    infoText = infoText + index + "、领导力：" + (data.textLeader || '') + "<br/>";
                    index++;
                }
                if (data.mindNew < 75) {
                    infoText = infoText + index + "、创新与借鉴：" + (data.textNew || '') + "<br/>";
                    index++;
                }
                if (data.sharpEmbrace < 75) {
                    infoText = infoText + index + "、拥抱变化：" + (data.textEmbrace || '') + "<br/>";
                    index++;
                }
                if (data.groupCooperate < 75) {
                    infoText = infoText + index + "、营造多样化：" + (data.textCooperate || '') + "<br/>";
                    index++;
                }
            }
            return infoText;
        }
    },


    initPage() {
        this.pageResize();
    },
    pageResize() {
        this.body_height = $(window).height();
    },
    created() {
        this.findList();
    }
};
</script>
