import Vue from "vue";

export const layerBox = {
  layerDialogOpen(options) {
    return layer.open({
      type: 1,
      title: options.title || "提示",
      shadeClose: options.shadeClose || false,
      shade: options.shade || true,
      resize: options.resize || false,
      shade: 0.4,
      btn: options.btn || ["确定", "取消"],
      maxmin: options.maxmin || false, //开启最大化最小化按钮
      area: options.area || ["693px", "500px"],
      btn1: options.btn1 || null,
      btn2: options.btn2 || null,
      content: options.content || "",
      success: options.success || null,
    });
  },
  /**
   *
   * @param {*} options
   * options = {
   *  layer:layer弹框实例,
   *  component:Vue组件,
   *  props:props，数组类型,
   *  methods:'一些方法，可在component组件里面调用，对象类型'，
   * }
   */
  layerLoadVueComponent(options) {
    $("#layui-layer" + options.layer + " .layui-layer-content").append(
      "<div></div>"
    );
    let components = {};
    components["component" + options.layer] = options.component;
    const component = new Vue({
      el: "#layui-layer" + options.layer + " .layui-layer-content>div",
      data: options.props || {},
      methods: options.methods,
      components,
      template: `<component${options.layer} @submit-click='doConfirm' @cancel-click='doClose' :params='$data'></component${options.layer}>`,
      // render: h => h(options.component)
    });
    return component;
  },
};
export const getCompanyNameByGroupId = function (groupId) {
  let result = "";
  switch (groupId) {
    case "1":
      result = "集团总部";
      break;
    case "2":
      result = "赣州纸业纸品";
      break;
    case "3":
      result = "广西竹林";
      break;
    case "4":
      result = "赣州竹林";
      break;
    case "5":
      result = "销售公司";
      break;
    case "6":
      result = "崇左纸业";
      break;
      case "shezhao":
      result = "社招";
      break;
  }
  
  return result;
};
export const getGradeLevlByGradeId = function (gradeId) {
  var grade = "";
  switch (gradeId) {
    case "0006":
      grade = "总经理职等";
      break;
    case "0007":
      grade = "副总经理职等";
      break;
    case "0009":
      grade = "经理职等";
      break;
    case "0010":
      grade = "副经理职等";
      break;
    case "0011":
      grade = "主任职等";
      break;
    case "0012":
      grade = "副主任职等";
      break;
    case "0013":
      grade = "工程师职等";
      break;
    case "0014":
      grade = "班长职等";
      break;
    case "0015":
      grade = "科员职等";
      break;
    case "0019":
      grade = "预备工程师职等";
      break;
    case "0023":
      grade = "场长职等";
      break;
    case "0024":
      grade = "副场长职等";
      break;
    case "0025":
      grade = "片区主任职等";
      break;
    case "0026":
      grade = "厂长职等";
      break;
    case "0034":
      grade = "副厂长职等";
      break;
    case "0027":
      grade = "值班长职等";
      break;
    case "0032":
      grade = "高级经理级职等";
      break;
    case "0033":
      grade = "总经理助理级职等";
      break;
  }
  return grade;
};

export const getGradeGradeNameByGradeId = function (gradeId) {
  var grade = "";
  switch (gradeId) {
    case "0006":
      grade = "总经理";
      break;
    case "0007":
      grade = "副总经理";
      break;
    case "0009":
      grade = "经理";
      break;
    case "0010":
      grade = "副经理";
      break;
    case "0011":
      grade = "主任";
      break;
    case "0012":
      grade = "副主任";
      break;
    case "0013":
      grade = "工程师";
      break;
    case "0014":
      grade = "班长";
      break;
    case "0015":
      grade = "科员";
      break;
    case "0019":
      grade = "预备工程师";
      break;
    case "0023":
      grade = "场长";
      break;
    case "0024":
      grade = "副场长";
      break;
    case "0025":
      grade = "片区主任";
      break;
    case "0026":
      grade = "厂长";
      break;
    case "0034":
      grade = "副厂长";
      break;
    case "0027":
      grade = "值班长";
      break;
    case "0032":
      grade = "高级经理";
      break;
    case "0033":
      grade = "总经理助理";
      break;
  }
  return grade;
};

export const getQueryParams = function (key) {
  let queryStrArr = window.location.href.split("?");
  var url_params = queryStrArr ? queryStrArr[1] || "" : "";
  var params_arr = url_params.split("&");
  var obj = {};
  $.each(params_arr, function (index, item) {
    var obj_arr = item.split("=");
    obj[obj_arr[0]] = obj_arr[1];
  });
  return obj ? obj[key] || "" : "";
};

export const number2ChNum = function (num) {
  if (isNaN(num)) {
    return;
  }
  let str = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
  return str[num];
};
