<template>
<div class="benchmarking-personnel-container">
    <div class="row">
        <div class="col-xs-12 text-left">
            <h4 class="col-xs-6">{{getCompanyName()}}管培生【综合能力评定及跟踪】表</h4>
            <div class="col-xs-6 text-right" style="line-height:40px;">
                <button type="button" class="btn btn-primary btn-xs" @click="showDescrDialog">说明</button>
                <button type="button" class="btn btn-primary btn-xs" :disabled="disabledButton || onff" @click="_updateStandardStudentToOA()"
                        v-if="$store.state.doubleCol.arrAuths.point_ablity_updateConfirmLeaderByStudent">评定开始发起</button>
                <button class="btn btn-primary btn-xs" @click="$router.push(`/${$route.params.year}/${$route.params.month}/menu/2`)">返回目录</button>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12">
            <div class="list-table">
                <div class="scroll-table" style="height:100%;overflow-x:hidden;">
                    <table class="scroll-table-header table-bordered">
                        <colgroup>
                            <col style="width:5%" />
                            <col style="width:10%" />
                            <col style="width:20%" />
                            <col style="width:20%" />
                            <col style="width:20%" />
                            <col style="width:10%" />
                            <col style="width:15%" />
                        </colgroup>
                        <thead>
                            <tr>
                                <th rowspan="2">序号</th>
                                <th rowspan="2">评定人</th>
                                <th colspan="4">评定完成情况</th>
                                <th rowspan="2">评定表查阅</th>
                            </tr>
                            <tr>
                                <th>开始评定时间</th>
                                <th>要求完成时间</th>
                                <th>实际完成时间</th>
                                <th>超期天数</th>
                            </tr>
                        </thead>
                    </table>
                    <div class="scroll-table-body" :style="{overflow:'auto',maxHeight:body_height - 160 + 'px','overflow-y':'scroll','overflow-x':'hidden'}">
                        <table style="margin:0px 10px 0 0;position: relative;font-size:13px;float:left;border:none;" class="table table-bordered table-hover table-striped">
                            <colgroup>
                                <col style="width:5%" />
                                <col style="width:10%" />
                                <col style="width:20%" />
                                <col style="width:20%" />
                                <col style="width:20%" />
                                <col style="width:10%" />
                                <col style="width:15%" />
                            </colgroup>
                            <tbody>
                                <tr v-for="(data,$index) in tableData" :key="$index">
                                    <td>{{$index+1}}</td>
                                    <td>{{data.userName}}</td>
                                    <td>{{data.sendDate==null?'':data.sendDate.substring(0,10)}}</td>
                                    <td>{{data.requireFinishDate==null?'':data.requireFinishDate.substring(0,10)}}</td>
                                    <td>{{data.checkDate==null?'':data.checkDate.substring(0,10)}}</td>
                                    <td>{{data.delayDay>0?data.delayDay:null}}</td>
                                    <td><a  @click="showDetailsList(data)">查看</a></td>
                                </tr>
                            </tbody>
                        </table>
                        <span v-if="tableDataLength <= 0">
                          {{ tableDataLength == -1 ? "加载中..." : "暂无数据" }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</template>

<script>
import {
    findStudentConfirmLeaderList,
    findMergeListByCompanyGroupId,
    updateStandardEmployee,
    updateStandardStudentToOA
} from '@/api'
import FlowStartDetails_list from './FlowStartDetails_list'

export default {
    data() {
        return {
            isLoading: true,
            body_height: 0,
            tableData: [],
            assessorList: [],
            year: "",
            month: "",
            companyGroupId: "",
            positionGradeId: "",
            deptArea: Utils.getQueryParams('deptArea'),
            onff: false,
            tableDataLength: -1
        }
    },
    computed: {
        disabledButton() {
            if (this.isLoading || this.tableData.length==0) {
                return true
            } else if (this.tableData && this.tableData.length != 0) {
                return !(this.tableData[0].confirmLeaderId != null && (this.tableData[0].status == null || this.tableData[0].status == 10) )
            }
            return false;
        }
    },
    methods: {
        getCompanyName() {
            return Utils.getCompanyNameByGroupId(this.companyGroupId);
        },
        getGradeName() {
            return Utils.getGradeLevlByGradeId(this.positionGradeId);
        },
        checkStandardSize() {
            let count = 0;
            for (let i = 0; i < this.tableData.length; i++) {
                let data = this.tableData[i];
                if (data.standardPoint) {
                    count++;
                }
                //先不限制评价对象人数
                // if(count>=2){
                //   return false;
                // }
            }
            return true;
        },
        showDescrDialog() {
            Utils.layerBox.layerDialogOpen({
                title: '说明',
                area: ['600px', '320px'],
                btn: ['关闭'],
                content: `<div style="padding:10px 20px;margin:0 auto;">
          <p style="line-height:24px;">
            1、本表生成时间：《评定人确定表》审批后自动生成本表。<br/>
            2、数据来源<br/>
            （1）评定人：读取已审批的《评定人确定表》。<br/>
            （2）开始评定时间、要求完成时间：点击“评定开始发起”键，弹出要求完成时间录入框，开始评定时间默认为当前时间，要求完成时间人工录入，确定后生成个评定人的评定表，并推送到OA。<br/>
            （3）实际完成时间：读取评定人OA评定表的完成提交时间。<br/>
            （4）超期天数=实际完成时间-要求完成时间，若不超期，留空。
          </p></div>`
            });
        },
        showStandardName(data) {
            // data.standardLevel = data.standardPoint ? data.standardPoint == 90 ? '优' : '标杆' : '';
            data.standardPoint = data.standardLevel == "优" ? 90 : data.standardPoint;
        },
        _updateStandardStudentToOA() {
            this.onff = true;
            if (this.disabledButton) {
                return;
            }
            var date = new Date();
            var seperator1 = "-";
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var strDate = date.getDate();
            if (month >= 1 && month <= 9) {
                month = "0" + month;
            }
            if (strDate >= 0 && strDate <= 9) {
                strDate = "0" + strDate;
            }
            var currentdate = year + seperator1 + month + seperator1 + strDate;

            Utils.layerBox.layerDialogOpen({
                title: '说明',
                area: ['420px', '200px'],
                btn: ['确定', '关闭'],
                content: `<div style="padding:10px 20px;margin:0 auto;">
                    <div style="padding-bottom:10px;">评定开始时间：`+currentdate+`</div>
                    <div>要求完成时间：<input type="text" id="validDate"></div></div>`,
                success: () => {
                    laydate.render({
                        elem: '#validDate' //指定元素
                    });
                },
                btn1: (index, layero) => {
                    if ($(validDate).val()=="")
                    {
                        layer.msg(`请选择要求完成时间.`, {
                            icon: 2,
                            shade: 0.3,
                            shadeClose: true
                        });
                        return;
                    }
                    let selectedDate=$(validDate).val();
                    let confirmLeaderId=this.tableData[0].confirmLeaderId;

                    layer.confirm("评定开始发起将触发OA流程，请确认？", {
                        title: '提示',
                        btn: ['确定', '取消'] //按钮
                    }, (index) => {
                        this.isLoading = true;
                        updateStandardStudentToOA({
                            confirmLeaderId: confirmLeaderId,
                            requireFinishDate: selectedDate,
                            companyGroupId: this.companyGroupId
                        }).then(res => {
                            let result = res.success;
                            layer.msg(`流程发起${result ? '成功' : '失败'}`, {
                                icon: result ? 1 : 2,
                                shade: 0.3,
                                shadeClose: true
                            });
                            this._findStudentConfirmLeaderList();
                            layer.close(index);
                            layer.close(index-1);
                            this.onff = false;
                        }).catch(err => {
                            layer.msg(`${err.msg}`, {
                                icon: 2,
                                shade: 0.3,
                                shadeClose: true
                            });
                            layer.close(index)
                            this.onff = false;
                        })
                    });
                },
                btn2: (index, layero) => {
                    layer.close(index);
                    this.onff = false;
                }
            });
        },
        _findStudentConfirmLeaderList() {
            /*先判断有权限才可以查询(modify by huangdh@2019-12-23)*/
            if (this.$store.state.doubleCol.arrAuths.point_ablity_findStudentDetailsListBy)
            {
                findStudentConfirmLeaderList({
                    companyGroupId: this.companyGroupId,
                    year: this.year,
                    month: this.month
                }).then(res => {
                    this.tableData = res.data || [];
                    this.isLoading = false;
                    this.tableDataLength = this.tableData.length
                });
            }
            else {
                layer.msg(`对不起，您没有权限查询本界面.`, {icon: 2,shade:0.3,shadeClose:true});
            }

        },
        showDialog(item) {
            let layerDialog = Utils.layerBox.layerDialogOpen({
                title: "【级别评定】表",
                btn: [],
                maxmin: false, //开启最大化最小化按钮
                area: ["800px", "420px"],
                btn1: (index, layero) => {},
                btn2: (index, layero) => {}
            });
            this.dialogComponent = Utils.layerBox.layerLoadVueComponent({
                layer: layerDialog,
                component: FlowStartDetails_list,
                methods: {
                    doConfirm: (params) => {
                        layer.close(layerDialog);
                    },
                    doClose: () => {
                        layer.close(layerDialog); //关闭对话框
                    }
                },
                props: {
                    confirmLeaderId: item.confirmLeaderId,
                    leaderName: item.userName,
                    sendDate: (item.sendDate==null?'':item.sendDate.substring(0, 10)),
                    requireFinishDate: (item.requireFinishDate==null?'':item.requireFinishDate.substring(0, 10)),
                }
            });
        },
        showDetailsList(item){
            this.$router.push({
                path: '/'+this.year+'/'+this.month+'/Trainee/Staff/'+this.companyGroupId+'/FlowStartDetails_list',
                query: {
                    confirmLeaderId:item.confirmLeaderId,
                    leaderName: item.userName,
                    sendDate: (item.sendDate==null?'':item.sendDate.substring(0, 10)),
                    requireFinishDate: (item.requireFinishDate==null?'':item.requireFinishDate.substring(0, 10)),
                }
            });
        },
        initPage() {
            this.pageResize();
            const {
                companyGroupId,
                year,
                month,
                positionGradeId
            } = this.$route.params;
            this.companyGroupId = companyGroupId;
            this.year = year;
            this.month = month;
            this.positionGradeId = positionGradeId;
            this._findStudentConfirmLeaderList();
            //this._findMergeListByCompanyGroupId();
        },
        pageResize() {
            this.body_height = $(window).height();
        }
    },
    watch: {
        "$route": function () {
            this.initPage();
        }
    },
    created() {
        this.initPage();
        $(window).resize(this.pageResize);
    }
}
</script>
<style scoped>
/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}
.scroll-table-body {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
</style>
