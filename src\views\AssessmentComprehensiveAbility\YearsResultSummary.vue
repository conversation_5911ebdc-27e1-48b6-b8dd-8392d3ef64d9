<template>
  <div class="years-result-summary-container">
    <div class="row">
      <div class="col-xs-12 text-left">
        <h4 class="col-xs-7">
          {{ this.$route.params.year }}{{ this.month == `6` ? `半` : `` }}年度{{ getCompanyName() }}（{{ getGradeName() }}）{{ $route.name }}
        </h4>
        <div class="col-xs-5 text-right" style="padding: 0;line-height: 38px;">
          <button class="btn btn-primary btn-xs"
            @click="$router.push(`/${$route.params.year}/${$route.params.month}/menu/1`)">返回目录</button>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-xs-12">
        <div class="list-table">
          <div class="scroll-table" style="height:100%;overflow-x:hidden;">
            <table class="scroll-table-header table-bordered">
              <colgroup>
                <col style="width:40px" />
                <col style="width:110px" />
                <col style="width:70px" />
                <col style="width:40px" />
                <col style="width:40px" />
                <col style="width:50px" />
                <col style="width:130px" />
                <col style="width:100px" />

                <col v-for="(second, index) in secondRowHeader" :key="index" style="width:60px" />

                <col style="width:90px" />
              </colgroup>
              <thead>
                <tr>
                  <th rowspan="2">序号</th>
                  <th rowspan="2">厂部</th>
                  <th rowspan="2">姓名</th>
                  <th rowspan="2">性别</th>
                  <th rowspan="2">年龄</th>
                  <th rowspan="2">学历</th>
                  <th rowspan="2">岗位</th>
                  <th rowspan="2">职等</th>
                  <th v-for="(first, index) in firstRowHeader" :key="index" colspan="2">{{ first }}</th>
                  <th rowspan="2">备注</th>
                </tr>
                <tr>
                  <th v-for="(second, index) in secondRowHeader" :key="index">{{ second.value }}</th>
                </tr>
              </thead>
            </table>
            <div class="scroll-table-body"
              :style="{ overflow: 'auto', maxHeight: body_height - 160 + 'px', 'overflow-y': 'scroll', 'overflow-x': 'hidden' }">
              <table style="margin:0px 10px 0 0;position: relative;font-size:13px;float:left;border:none;"
                class="table table-bordered table-hover table-striped">
                <colgroup>
                  <col style="width:40px" />
                  <col style="width:110px" />
                  <col style="width:70px" />
                  <col style="width:40px" />
                  <col style="width:40px" />
                  <col style="width:50px" />
                  <col style="width:130px" />
                  <col style="width:100px" />

                  <col v-for="(second, index) in secondRowHeader" :key="index" style="width:60px" />

                  <col style="width:90px" />
                </colgroup>
                <tbody>
                  <tr v-for="(data, $index) in tableData" :key="$index">
                    <td>{{ $index + 1 }}</td>
                    <td>{{ data.deptName }}</td>
                    <td>{{ data.userName }}</td>
                    <td>{{ data.gender }}</td>
                    <td>{{ data.age }}</td>
                    <td>{{ data.degree }}</td>
                    <td>{{ data.jobName }}</td>
                    <td>{{ data.gradeName }}</td>
                    <td v-for="(second, index) in secondRowHeader" :key="index">{{ getMonthScore(second, data) }}</td>
                    <td>{{ data.remark }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { findYearReport } from '@/api'
export default {
  data() {
    return {
      tableData: [],
      body_height: 0,
      firstRowHeader: [],
      secondRowHeader: [],
      yearData: [],
      year: "",
      month: "",
      companyGroupId: "",
      positionGradeId: "",
      deptArea: Utils.getQueryParams('deptArea')
    }
  },
  methods: {
    getCompanyName() {
      let companyName = Utils.getCompanyNameByGroupId(this.companyGroupId);
      if (this.positionGradeId == "0006" || this.positionGradeId == "0007" || this.positionGradeId == "0032" || this.positionGradeId == "0033") {
        companyName = "全集团";
      }
      return companyName;
    },
    getGradeName() {
      let name = Utils.getGradeLevlByGradeId(this.positionGradeId);
      if (this.deptArea == "A") {
        name = name + "-生产一线"
      }
      else if (this.deptArea == "B") {
        name = name + "-生产辅助"
      }
      else if (this.deptArea == "C") {
        name = name + "-生产支持"
      }
      return name;
    },
    getMonthScore(data, userData) {
      let result = this.yearData.find(item => {
        if (item.fd_month == data.month && item.fd_year == data.year && item.user_id == userData.userId) {
          return item;
        }
      });
      return (result && ((result.fd_year + '-' + result.fd_month) !== this.year + '-' + this.fd_month) || userData.status == 30) ? (result ? result.last_result : '') : '';
    },
    _findYearReport() {
      /*先判断有权限才可以查询(modify by huangdh@2019-05-24)*/
      if (this.$store.state.doubleCol.arrAuths.point_ablity_findYearReport) {
        findYearReport({ CompanyGroupId: this.companyGroupId, Year: this.year, Month: this.month, PositionGradeId: this.positionGradeId, DeptArea: this.deptArea }).then(res => {
          if (!res.success) return;
          this.tableData = res.data ? res.data[0] ? res.data[0].AblityList : [] : [];
          this.yearData = res.data ? res.data[0] ? res.data[0].LastResultList : [] : [];
          let headers = res.data ? res.data[0] ? res.data[0].YearHeaderList : [] : [];
          let firstRow = new Set();
          let sencodRow = [];
          headers.forEach(header => {
            for (let k of Object.keys(header)) {
              firstRow.add(k.substring(1));
              let value = header[k];
              sencodRow.push({ value: value, year: k.substring(1), month: value === '上半年' ? 6 : 12 });
            }
          })
          for (let i of firstRow) {
            this.firstRowHeader.push(i)
          }
          this.secondRowHeader = sencodRow;
        }).catch(err => {
          layer.msg(`${err.msg}`, { icon: 2, shade: 0.3, shadeClose: true });
        })
      }
      else {
        layer.msg(`对不起，您没有权限查询本界面.`, { icon: 2, shade: 0.3, shadeClose: true });
      }
    },
    initPage() {
      this.pageResize();
      const { companyGroupId, year, month, positionGradeId } = this.$route.params;
      this.companyGroupId = companyGroupId;
      this.year = year;
      this.month = month;
      this.positionGradeId = positionGradeId;
      // this.deptArea = "";
      this._findYearReport();
    },
    pageResize() {
      this.body_height = $(window).height();
    }
  },
  watch: {
    "$route": function () {
      this.initPage();
    }
  },
  mounted() {
    this.initPage();
    $(window).resize(this.pageResize);
  }
}
</script>

<style scoped>
/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}
.list-year-data {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
.scroll-table-body {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
</style>

