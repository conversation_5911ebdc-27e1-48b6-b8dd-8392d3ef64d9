<template>
  <div class="planning-setting-container">
      <div class="row">
        <div class="col-xs-12 text-left">
          <h4 class="col-xs-6">{{$route.name}}</h4>
          <div class="col-xs-6 text-right" style="line-height:40px;">
            <button class="btn btn-primary btn-xs" :disabled="tableData.length == 0" @click="saveSettings"
                v-if="$store.state.doubleCol.arrAuths.base_basePositionPlan_updateBasePositionPlan">保存录入</button>
            <button class="btn btn-primary btn-xs" v-if="isEdit" @click="$router.push(`/${$route.params.year}/${$route.params.month}/trainee/planning/${$route.params.graduateYear}/${$route.params.schoolLevel}/detail?type=${type}`)">返回</button>
            <button class="btn btn-primary btn-xs" @click="$router.push(`/${$route.params.year}/${$route.params.month}/menu/1`)">返回目录</button>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-xs-9">
            <div class="list-year-head-table" style="width:900px;background:#b7d8dc;">
              <table class="list-year-head table table-bordered" style="margin-bottom:0;width:884px;margin:0 auto 0 0;">
                <colgroup>
                    <col width='50'/>
                    <col width='100'/>
                    <col width='70'/>
                    <col width='100'/>
                    <col width='70'/>
                    <col width='100'/>
                    <col width='70'/>
                    <col width='100'/>
                </colgroup>
                <thead>
                  <tr v-if="!isEdit">
                    <th></th>
                    <th colspan="6" style="text-align:left;">
                      <div style="margin-right:20px;display:inline-block;">
                        <label>届别：</label>
                        <select style="width:100px;display:inline-block;height:24px;line-height:26px;" v-model="graduateYear">
                          <option value="">请选择</option>
                          <option v-for="(item,index) in graduateYearList" :value="item.graduate_year" :key="index">{{item.graduate_year}}</option>
                        </select>
                      </div>
                      <div style="margin-right:20px;display:inline-block;">
                        <label>岗位等级：</label>
                        <select class="form-control input-sm" style="width:100px;display:inline-block;" v-model="schoolLevel">
                          <option value="">请选择</option>
                          <option v-for="(item,index) in jobCategoryList" :value="item.value" :key="index">{{item.name}}</option>
                        </select>
                      </div>
                      <div style="margin-right:20px;display:inline-block;" v-if="isLevelOne">
                        <label>岗位类型：</label>
                        <select class="form-control input-sm" style="width:100px;display:inline-block;" v-model="jobCategory">
                          <option value="">请选择</option>
                          <option value="市场销售类">市场销售类</option>
                          <option value="行政人资类">行政人资类</option>
                        </select>
                      </div>
                    </th>
                    <th></th>
                  </tr>
                  <tr>
                      <th rowspan="3">序号</th>
                      <th rowspan="3">职务等级</th>
                      <th rowspan="3">年份</th>
                      <th colspan="4">时间</th>
                      <th rowspan="3">操作</th>
                  </tr>
                  <tr>
                      <th colspan="2">起</th>
                      <th colspan="2">止</th>
                  </tr>
                  <tr>
                      <th>年</th>
                      <th>月</th>
                      <th>年</th>
                      <th>月</th>
                  </tr>
                </thead>
            </table>
          </div>
          <div class="list-year-data" style="width:900px;overflow-x:hidden;overflow-y:scroll;border-bottom:1px solid #ccc;">
              <table style="margin-bottom:0;" class="table table-bordered">
                <colgroup>
                    <col width='50'/>
                    <col width='100'/>
                    <col width='70'/>
                    <col width='100'/>
                    <col width='70'/>
                    <col width='100'/>
                    <col width='70'/>
                    <col width='100'/>
                </colgroup>
                <tbody>
                    <tr v-for="(data,$index) in tableData" :key="$index">
                        <td>{{$index+1}}</td>
                        <td>
                          <select class="form-control input-sm" v-model="data.positionGradeName">
                            <option value="">请选择</option>
                            <option v-for="(item,index) in gradeList" :value="item.item_name" :key="index">{{item.item_name}}</option>
                          </select>
                        </td>
                        <td><input type="text" class="form-control input-sm" placeholder="请输入" v-model="data.yearName"/></td>
                        <td>
                          <select class="form-control input-sm" :disabled="!graduateYear" v-model="data.startYear">
                            <option value="">请选择</option>
                            <option v-for="year in yearsList" :key="year" :value="year">{{year}}年</option>
                          </select>
                        </td>
                        <td>
                          <select class="form-control input-sm" :disabled="!graduateYear" v-model="data.startMonth">
                            <option value="">请选择</option>
                            <option v-for="month in 12" :key="month" :value="month">{{month}}月</option>
                          </select>
                        </td>
                        <td>
                          <select class="form-control input-sm" :disabled="!graduateYear" v-model="data.endYear">
                            <option value="">请选择</option>
                            <option v-for="year in yearsList" :key="year" :value="year">{{year}}年</option>
                          </select>
                        </td>
                        <td>
                          <select class="form-control input-sm" :disabled="!graduateYear" v-model="data.endMonth">
                            <option value="">请选择</option>
                            <option v-for="month in 12" :key="month" :value="month">{{month}}月</option>
                          </select>
                        </td>
                        <td>
                          <button v-if="tableData.length - 1 == $index" class="btn btn-primary btn-xs" @click="addRow">新增</button>
                          <button v-if="tableData.length >1" class="btn btn-danger btn-xs" @click="delRow($index)">删除</button>
                        </td>
                    </tr>
                </tbody>
            </table>
          </div>
        </div>
      </div>
  </div>
</template>

<script>
import {
  findListByYearAndCategory,
  findStudentGrade,
  findGraduateYear,
  updateBasePositionPlan
} from "@/api";

export default {
  data() {
    let planningObjTemp = {
      graduateYear:'',
      jobCategory:'',
      schoolLevel:'',
      positionGradeName:'',
      startMonth:'',
      startYear:'',
      yearIndex:'',
      yearName:'',
      endYear:'',
      endMonth:'',
    }
    return {
      isLoading:true,
      planningObjTemp:JSON.stringify(planningObjTemp),
      tableData: [],
      gradeList:'',
      jobCategoryList: [{
          name:'一级岗位',
          value:'1'
      },{
          name:'二级岗位',
          value:'2'
      },{
          name:'三级岗位',
          value:'3'
      }],
      graduateYearList:[],
      yearsList:[],
      graduateYear: "",
      schoolLevel: "",
      jobCategory: "",
      type:''
    };
  },
  computed:{
      isLevelOne(){
          if(this.schoolLevel == 1) return true;
          else this.jobCategory = ""; return false
      },
      isEdit(){
        return this.$route.meta ? this.$route.meta.form == 'edit' : false;
      }
  },
  methods: {
    getJobCategory(){
      let result = "";
      switch(this.type){
        case 'scxs':
          result = '市场销售类';break;
        case 'xzrz':
          result = '行政人资类';break;
        default:
          result = '';
      }
      return result;
    },
    saveSettings(){
      if(this.isLoading){
        return;
      }
      this.isLoading = true;
      this.tableData.forEach((item,index)=>{
        item.yearIndex = index+1;
        item.graduateYear = this.graduateYear;
        item.jobCategory = this.jobCategory;
        item.schoolLevel = this.schoolLevel;
      })
      updateBasePositionPlan(this.tableData).then(res=>{
        let result = res.success;
        layer.msg(`保存配置${result ? '成功' : '失败'}`, {icon: result ? 1 : 2,shade:0.3,shadeClose:true});
        this.initPage();
      })
    },
    addRow(){
      this.tableData.push(JSON.parse(this.planningObjTemp));
    },
    delRow(index){
      this.tableData = this.tableData.filter((data,i)=> index!=i);
    },
    _findListByYearAndCategory(){
        /*先判断有权限才可以查询(modify by huangdh@2019-05-24)*/
        if (this.$store.state.doubleCol.arrAuths.base_basePositionPlan_findListByYearAndCategory)
        {
            findListByYearAndCategory({graduateYear:this.$route.params.graduateYear,schoolLevel:this.$route.params.schoolLevel,jobCategory:this.$route.params.jobCategory}).then(res=>{
                if(res.data&&res.data.length){
                  this.tableData = res.data
                }else{
                  this.tableData = [];
                  this.tableData.push(JSON.parse(this.planningObjTemp))
                }
            })
        }
        else {
            layer.msg(`对不起，您没有权限查询本界面.`, {icon: 2,shade:0.3,shadeClose:true});
        }
    },
    _findGraduateYear() {
      findGraduateYear().then(res => {
        this.graduateYearList = res.data || [];
      });
    },
    _findStudentGrade() {
      findStudentGrade().then(res => {
        this.gradeList = res.data || [];
      });
    },
    initPage() {
      this.isLoading = false;
      this._findStudentGrade();
      this._findGraduateYear();
      this.type = Utils.getQueryParams('type')||'';
      if(this.isEdit){
        this._findListByYearAndCategory();
        this.graduateYear = this.$route.params.graduateYear,
        this.schoolLevel = this.$route.params.schoolLevel,
        this.jobCategory = this.getJobCategory(this.type)
      }else{
        this.tableData = [];
        this.tableData.push(JSON.parse(this.planningObjTemp));
      }
    }
  },
  watch: {
    $route: function() {
      this.initPage();
    },
    graduateYear(val){
      let start = parseInt(val);
      let end = start + 8;
      let arr = [];
      if(start){
        for(let i=0;i<end - start;i++){
          arr.push(start+i);
        }
        this.yearsList = arr;
      }
    }
  },
  created() {
    this.initPage();
  }
};
</script>

<style>
.planning-setting-container table thead tr th {
  height: 26px !important;
}
</style>
