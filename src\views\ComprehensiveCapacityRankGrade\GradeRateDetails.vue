<template>
  <div class="grade-evaluation-dialog-container">
    <div class="row">
      <div class="col-xs-12">
        <table class="table table-bordered">
          <thead>
            <tr>
              <th rowspan="2" style="width: 60px">职等总人数</th>
              <th colspan="2">优级</th>
              <th rowspan="2" style="width: 60px">ABC级人数</th>
              <th colspan="5">【级别】占比及人数</th>
              <th colspan="5">【职级】占比及人数</th>
            </tr>
            <tr>
              <th style="width: 50px">人数</th>
              <th style="width: 50px">占比</th>
              <th style="width: 50px">级别</th>
              <th style="width: 70px">标准占比</th>
              <th style="width: 70px">实际占比</th>
              <th style="width: 70px">标准人数</th>
              <th style="width: 70px">实际人数</th>
              <th style="width: 50px">职级</th>
              <th style="width: 70px">标准占比</th>
              <th style="width: 70px">实际占比</th>
              <th style="width: 70px">标准人数</th>
              <th style="width: 70px">实际人数</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(data, $index) in tableData" :key="$index">
              <td v-if="$index === 0" :rowspan="tableData.length">
                {{ data.total_count }}
              </td>
              <td v-if="$index === 0" :rowspan="tableData.length">
                {{ data.good_count }}
              </td>
              <td v-if="$index === 0" :rowspan="tableData.length">
                {{ data.good_rate == null ? "" : data.good_rate + "%"  }}
              </td>
              <td v-if="$index === 0" :rowspan="tableData.length">
                {{ data.abc_count }}
              </td>
              <td v-if="data.rowspan !== 0" :rowspan="data.rowspan">{{ data.level }}</td>
              <td v-if="data.rowspan !== 0" :rowspan="data.rowspan">
                {{ data.standard_rate == null ? "" : data.standard_rate + "%" }}
              </td>
              <td v-if="data.rowspan !== 0" :rowspan="data.rowspan">{{ data.fact_rate == null ? "" : data.fact_rate + "%" }}</td>
              <td v-if="data.rowspan !== 0" :rowspan="data.rowspan">{{ data.standard_count }}</td>
              <td v-if="data.rowspan !== 0" :rowspan="data.rowspan">{{ data.adjust_level_count }}</td>
              <td>{{ data.level2 }}</td>
              <td>
                {{
                  data.standard_rate2 == null ? "" : data.standard_rate2 + "%"
                }}
              </td>
              <td>
                {{ data.fact_rate2 == null ? "" : data.fact_rate2 + "%" }}
              </td>
              <td>{{ data.standard_count2 }}</td>
              <td>{{ data.adjust_result_count }}</td>
            </tr>
          </tbody>
        </table>
        <div>
          <strong>说明：</strong>优级人数及占比没有固定指标，若是有人员符合条件的，在同一评定单位原则上不超过15%，特殊情况超过15%的要特别说明，若是没有人员符合条件的则显示为0，因此ABC级人数是剔除优级之后的。
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { findLevelGradeRate } from "@/api";
export default {
  props: {
    params: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      body_height: 0,
      fdYear: "",
      fdMonth: "",
      tableData: "",
      // mergerArr: ""
    };
  },
  methods: {
    _findLevelGradeRate() {
      findLevelGradeRate({
        fdYear: this.params.fdYear,
        fdMonth: this.params.fdMonth,
        companyGroupId: this.params.companyGroupId,
        positionGradeId: this.params.positionGradeId,
      })
        .then((res) => {
          if (!res.success) return;
          let data = JSON.parse(JSON.stringify(res.data || []));
          if (data.length > 0) {
            let mergerArr = [];
            let idx = 1;
            data.forEach((item, index) => {
              if (mergerArr.length === 0) {
                mergerArr.push({ title: item.level, num: idx });
              } else {
                if (data[index - 1].level !== item.level) {
                  idx = 1;
                  mergerArr.push({ title: item.level, num: idx });
                } else {
                  idx++;
                  mergerArr[mergerArr.length - 1].num = idx;
                }
              }
            });
            mergerArr.forEach((item, index) => {
              data.forEach((val, k) => {
                if (k == 0 && index === 0) {
                  val.rowspan = item.num;
                } else {
                  if (item.title === val.level) {
                    if (val.level !== data[k - 1].level) {
                      val.rowspan = item.num;
                    } else {
                      val.rowspan = 0;
                    }
                  }
                }
              });
            });
            this.tableData = data
          } else {
            this.tableData = [];
          }
          // this.tableData = res.data ? res.data : [];
        })
        .catch((err) => {
          layer.msg(`${err.msg}`, { icon: 2, shade: 0.3, shadeClose: true });
        });
    },
  },
  created() {
    this._findLevelGradeRate();
  },
};
</script>

<style lang="scss">
.grade-evaluation-dialog-container {
  padding: 20px 0;
  .row {
    margin: 0;
    padding-left: 0;
    padding-right: 0;
    box-sizing: border-box;
  }
  .input-sm {
    height: 28px;
    padding: 5px 10px 5px 5px;
  }
  div.scroll-table {
    width: 100%;
    height: 320px;
    display: inline-block;
    float: left;
    background: #f4fafb;
    table.scroll-table-header {
      width: 100%;
      font-size: 13px;
      // display:block;
      // padding-right:17px;
    }
    table.scroll-table-body {
    }
  }
  table thead {
    tr {
      th {
        text-align: center;
        border-bottom: none;
        padding: 2px 5px;
        vertical-align: middle;
        background: #b7d8dc;
        height: 32px;
        font-size: 12px;
        color: #333;
        & > * {
          background: #b7d8dc !important;
          outline: none;
        }
      }
    }
    &.hide-thead-th > tr > th {
      height: 0;
      padding: 0;
      background: transparent;
      border: none;
    }
  }
  .table tbody tr {
    td {
      padding: 2px 5px;
      vertical-align: middle;
      height: 27px;
      font-size: 12px;
      text-align: center;
      .btn {
        padding-top: 0;
        padding-bottom: 0;
      }
    }
    &:nth-child(odd) {
      background: #fff;
    }
    &:nth-child(even) {
      background: #f9f9f9;
    }
  }
}
</style>
