<template>
    <div class="container" style="width:300px;padding-top:20px;">
        <form>
            <div class="form-group row">
                <label class="col-xs-4 text-right">姓名</label>
                <span>{{params.userName}}</span>
            </div>
            <div class="form-group row">
                <label class="col-xs-4 text-right">届别</label>
                <select class="form-control" v-model="formData.graduateYear" style="width:120px;display:inline-block;">
                    <option value="">请选择</option>
                    <option v-for="(item,index) in graduateYearList" :value="item.graduate_year" :key="index">{{item.graduate_year}}</option>
                </select>
            </div>            
            <div class="form-group row">
                <label class="col-xs-4 text-right">公司</label>
                <select class="form-control" v-model="formData.companyGroupId" @change="resetSchoolLevel" style="width:120px;display:inline-block;">
                    <option value="">请选择</option>
                    <option v-for="(item,index) in companyGroups" :value="item.code" :key="index">{{item.name}}</option>
                </select>
            </div>
            <div class="form-group row">
                <label class="col-xs-4 text-right">岗位级别</label>
                <select class="form-control" v-model="formData.schoolLevel" style="width:120px;display:inline-block;">
                    <option value="">请选择</option>
                    <!-- v-if="formData.companyGroupId ==5 ? item.value==1 ?true :false : true" -->
                    <option v-for="(item,index) in schoolLevels" :value="item.value" :key="index">{{item.name}}</option>
                </select>
            </div>
            <div class="form-group row">
                <label class="col-xs-4 text-right">是否参加本次评定</label>
                <select class="form-control" v-model="formData.status" style="width:120px;display:inline-block;">
                    <option value="">请选择</option>
                    <option v-for="(item,index) in statusList" :value="item.value" :key="index">{{item.name}}</option>
                </select>
            </div>
            <div class="form-group text-center">
                <button type="button" class="btn btn-xs btn-primary" @click="_adjustGraduateYearSave">保存录入</button>
            </div>
        </form>
    </div>
</template>

<script>
import { adjustGraduateYearSave, findGraduateYear } from "@/api";

export default {
  props: {
    params: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      graduateYearList: [],
      schoolLevels: [
        {
          name: "一级岗位",
          value: "1"
        },
        {
          name: "二级岗位",
          value: "2"
        },
        {
          name: "三级岗位",
          value: "3"
        }
      ],
      companyGroups: [
        {
          name: "集团总部",
          code: "1"
        },
        {
          name: "销售公司",
          code: "5"
        },
        {
          name: "赣州纸业纸品",
          code: "2"
        },
        {
          name: "广西竹林",
          code: "3"
        },
        {
          name: "崇左纸业",
          code: "6"
        }
      ],
      statusList: [
        {
          name: "参加",
          value: "10"
        },
        {
          name: "不参加",
          value: "0"
        }
      ],
      formData: {
        ablityId:"",
        userId: "",
        schoolLevel: "",
        graduateYear: "",
        companyGroupId: "",
        status:""
      }
    };
  },
  methods: {
    resetSchoolLevel() {
      this.formData.schoolLevel = "";
    },
    _findGraduateYear() {
      findGraduateYear().then(res => {
        this.graduateYearList = res.data || [];
      });
    },
    _adjustGraduateYearSave() {
      layer.confirm(
        `确认调整保存吗?`,
        { icon: 3, title: `提示` },
        index => {
          adjustGraduateYearSave(this.formData).then(res => {
              let result = res.success;
              layer.msg(`保存录入${result ? '成功' : '失败'}`, {icon: result ? 1 : 2,shade:0.3,shadeClose:true});
              layer.close(index)
              this.params.closeComponent&&this.params.closeComponent();
          });
        }
      );
    }
  },
  mounted() {
    this.formData = {
      userId: this.params.userId || "",
      schoolLevel: this.params.schoolLevel || "",
      graduateYear: this.params.graduateYear || "",
      companyGroupId: this.params.companyGroupId || "",
      ablityId: this.params.ablityId || "",
      status: this.params.status || ""
    };
    console.log(this.formData);
    this._findGraduateYear();
  }
};
</script>

<style>
</style>
