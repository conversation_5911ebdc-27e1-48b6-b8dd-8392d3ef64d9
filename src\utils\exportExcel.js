import FileSaver from 'file-saver'
import * as XlSX from 'xlsx'
export default {
  // 导出excel
  exportExcel (name, tableName) {
    var sel = XlSX.utils.table_to_book(document.querySelector(tableName))
    var selIn = XlSX.write(sel, { bookType: 'xlsx', bookSST: true, type: 'array' })
    try {
      FileSaver.saveAs(new Blob([selIn], { type: 'application/octet-stream' }), name)
    } catch (e) {
      console.log('err>>>>', e)
    }
    return selIn
  }
}
