<template>
  <div class="grade-evaluation-dialog-container">
      <div class="row">
        <div class="col-xs-12">
          <table class="table table-bordered">
            <thead>
              <colgroup>
                <col style="width:5%"/>
                <col style="width:14%"/>
                <col style="width:10%"/>
                <col style="width:5%"/>
                <col style="width:5%"/>
                <col style="width:7%"/>
                <col style="width:18%"/>
                <col style="width:12%"/>                
                <col style="width:12%"/>
                <col style="width:12%"/>
              </colgroup>
              <tr>
                <th >评定人:</th>
                <th >{{this.params.leaderName}}</th>
                <th colspan="2" >评定开始时间：</th>
                <th colspan="2" >{{this.params.sendDate}}</th>
                <th colspan="2" >要求完成时间：</th>
                <th colspan="2" >{{this.params.requireFinishDate}}</th>
              </tr>
              <tr>
                <th>序号</th>
                <th>厂部</th>
                <th>姓名</th>
                <th>性别</th>
                <th>年龄</th>
                <th>学历</th>
                <th>岗位</th>                
                <th>职等</th>
                <th>评定分值</th>
                <th>评定事例依据</th>
              </tr>
            </thead>
            <tbody>
              <colgroup>
                <col style="width:5%"/>
                <col style="width:14%"/>
                <col style="width:10%"/>
                <col style="width:5%"/>
                <col style="width:5%"/>
                <col style="width:7%"/>
                <col style="width:18%"/>
                <col style="width:12%"/>                
                <col style="width:12%"/>
                <col style="width:12%"/>
              </colgroup>
              <tr v-for="(data,$index) in tableData" :key="$index">
                <td>{{$index+1}}</td>
                <td>{{data.dept_name}}</td>
                <td>{{data.user_name}}</td>
                <td>{{data.gender}}</td>
                <td>{{data.age}}</td>
                <td>{{data.degree}}</td>
                <td>{{data.job_name}}</td>                
                <td>{{data.grade_name}}</td>
                <td>{{data.point+((data.level||'')==''?'':('('+data.level+')'))}}</td>
                <td>
                  <a @click="showRemark(data)" ><img src="../../assets/images/action_dtl.png" alt=""/></a>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
  </div>
</template>

<script>
import { findAblityPointListByConfirmLeaderId } from "@/api";
export default {
  props: {
      params:{
          type:Object,
          required:true
      }
  },
  data() {
    return {
      tableData: []
    };
  },
  methods: {
    _findAblityPointListByConfirmLeaderId() {      
      findAblityPointListByConfirmLeaderId({
        confirmLeaderId:this.params.confirmLeaderId,
        leaderName: this.params.leaderName
      }).then(res => {
        if (!res.success) return;
        this.tableData = res.data ? res.data : [];
      }).catch(err=>{
        layer.msg(`${err.msg}`, {icon: 2,shade:0.3,shadeClose:true});
      });
    },
    showRemark(item){
      var remark=item.bak_remark||"";
      if (remark==""){
        remark="无评定.";
      }
      Utils.layerBox.layerDialogOpen({
          title:'评定事例依据',
          area:['600px','300px'],
          btn:['关闭'],
          content: `<div style="padding:10px 20px;margin:0 auto;">`+remark+`</div>`
          });
    }
  },
  
  created() {
    this._findAblityPointListByConfirmLeaderId();
  }
};
</script>

<style lang="scss">
.grade-evaluation-dialog-container {
  padding: 20px 0;
  .row {
    margin: 0;
    padding-left: 0;
    padding-right: 0;
    box-sizing: border-box;
  }
  .input-sm {
    height: 28px;
    padding: 5px 10px 5px 5px;
  }
  div.scroll-table {
    width: 100%;
    height: 320px;
    display: inline-block;
    float: left;
    background: #f4fafb;
    table.scroll-table-header {
      width: 100%;
      font-size: 13px;
      // display:block;
      // padding-right:17px;
    }
    table.scroll-table-body {
    }
  }
  table thead {
    tr {
      th {
        text-align: center;
        border-bottom: none;
        padding: 2px 5px;
        vertical-align: middle;
        background: #b7d8dc;
        height: 32px;
        font-size: 12px;
        color: #333;
        & > * {
          background: #b7d8dc !important;
          outline: none;
        }
      }
    }
    &.hide-thead-th > tr > th {
      height: 0;
      padding: 0;
      background: transparent;
      border: none;
    }
  }
  .table tbody tr {
    td {
      padding: 2px 5px;
      vertical-align: middle;
      height: 27px;
      font-size: 12px;
      text-align: center;
      .btn {
        padding-top: 0;
        padding-bottom: 0;
      }
    }
    &:nth-child(odd) {
      background: #fff;
    }
    &:nth-child(even) {
      background: #f9f9f9;
    }
  }
}
</style>
