<template>
  <div class="result-detail-container">
    <div class="row">
      <div
        class="col-xs-12"
        style="font-size:20px;font-weight:bold;text-center"
      >
        {{ $route.name }}
      </div>
      <div class="col-xs-12 text-left;">
        <div class="col-xs-5 text-left" style="line-height: 24px">
          <span style="font-weight: bold">评定年度:</span>
          <span>{{ checkYear }}</span>
          <span style="font-weight: bold; margin-left: 30px">审批状态:</span>
          <span>{{ approveStatus }}</span
          ><br />
          <span style="color: red"
            >说明：审批表由集团人资统一提交，各公司只需操作到"评定结果合议表"即可。</span
          >
        </div>
        <div class="col-xs-7 text-right" style="line-height: 40px">
          <button class="btn btn-primary btn-xs" @click="viewStudentApprove()">
            审批提示
          </button>
          <button class="btn btn-primary btn-xs" @click="viewLastYear()">
            上期审批结果查看
          </button>
          <button class="btn btn-primary btn-xs" @click="showDescrDialog()">
            评定说明
          </button>
          <button type="button" class="btn btn-primary btn-xs" @click="sheetIt">
            导出Excel
          </button>
          <!-- <button
            class="btn btn-primary btn-xs"
            @click="_findListByFdYear('1')"
          >
            {{ buttonQueryText }}
          </button> -->
          <button
            class="btn btn-primary btn-xs"
            @click="_commitStudentToOA"
            :disabled="disabledButton"
            :style="{ display: isAuthCommit }"
            v-if="
              $store.state.doubleCol.arrAuths.point_ablity_commitStudentToOA
            "
          >
            提交审批
          </button>
          <button
            class="btn btn-primary btn-xs"
            @click="
              $router.push(
                `/${$route.params.year}/${$route.params.month}/menu/2`
              )
            "
          >
            返回目录
          </button>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-xs-12">
        <div id="table" class="list-table">
          <div class="table-container">
            <div class="table-header-wrapper">
              <table class="scroll-table-header table-bordered">
                <colgroup>
                  <col width="80" />
                  <col width="30" />
                  <col width="55" />
                  <col width="45" />
                  <col width="40" />
                  <col width="55" />
                  <col width="80" />
                  <col width="70" />
                  <col width="90" />
                  <col width="70" />
                  <col width="50" />
                  <col width="40" />
                  <col width="40" />

                  <col width="40" />
                  <col width="40" />
                  <col width="40" />
                  <col width="40" />
                  <col width="40" />
                  <col width="40" />
                  <col width="40" />
                  <col width="40" />
                  <col width="40" />
                  <col width="100" />
                </colgroup>
                <thead>
                  <tr>
                    <th rowspan="2">公司</th>
                    <th rowspan="2">序号</th>
                    <th colspan="9">评定对象</th>
                    <th rowspan="2">积极态度</th>
                    <th rowspan="2">稳定性</th>
                    <th colspan="2">本期评定</th>
                    <th colspan="2">本期合议</th>
                    <th colspan="2">本期审批</th>
                    <th rowspan="2">综合评价</th>
                    <th rowspan="2">工作总结</th>
                    <th rowspan="2">得分明细查看</th>
                    <th rowspan="2">备注</th>
                  </tr>
                  <tr>
                    <th>姓名</th>
                    <th>届别</th>
                    <th>岗位级别</th>
                    <th>学历</th>
                    <th>学校</th>
                    <th>部门</th>
                    <th>职务</th>
                    <th>职等</th>
                    <th>年份</th>
                    <th>得分</th>
                    <th>能力等级</th>
                    <th>得分</th>
                    <th>能力等级</th>
                    <th>能力等级</th>
                    <th>留任结论</th>
                  </tr>
                </thead>
              </table>
            </div>

            <div
              class="table-body-wrapper"
              :style="{
                maxHeight: body_height - 200 + 'px',
              }"
            >
              <table class="table table-bordered table-hover table-striped">
                <colgroup>
                  <col width="80" />
                  <col width="30" />
                  <col width="55" />
                  <col width="45" />
                  <col width="40" />
                  <col width="55" />
                  <col width="80" />
                  <col width="70" />
                  <col width="90" />
                  <col width="70" />
                  <col width="50" />
                  <col width="40" />
                  <col width="40" />

                  <col width="40" />
                  <col width="40" />
                  <col width="40" />
                  <col width="40" />
                  <col width="40" />
                  <col width="40" />
                  <col width="40" />
                  <col width="40" />
                  <col width="40" />
                  <col width="100" />
                </colgroup>
                <tbody>
                  <template v-for="(item, index) in tableData">
                    <template v-if="isPositionLevel(item.companyName)">
                      <template
                        v-for="(company, cIndex) in getUniqueCompanies(
                          item.list
                        )"
                      >
                        <tr :key="`level-${index}-${cIndex}`">
                          <template
                            v-if="
                              isFirstPositionLevelForCompany(company, index)
                            "
                          >
                            <td :rowspan="getCompanyTotalRows(company)">
                              {{ company }}
                            </td>
                          </template>

                          <td
                            style="
                              text-align: left;
                              font-size: 14px;
                              font-weight: bold;
                            "
                            colspan="22"
                          >
                            {{ item.companyName }}
                          </td>
                        </tr>

                        <template
                          v-for="(student, sIndex) in getStudentsByCompany(
                            item.list,
                            company
                          )"
                        >
                          <tr :key="`student-${index}-${cIndex}-${sIndex}`">
                            <td>{{ student.sortIndex }}</td>
                            <td>{{ student.userName }}</td>
                            <td>
                              {{
                                student.graduateYear == null
                                  ? ""
                                  : student.graduateYear
                              }}
                            </td>
                            <td>
                              {{
                                student.schoolLevel == 1
                                  ? "一级"
                                  : student.schoolLevel == 2
                                  ? "二级"
                                  : student.schoolLevel == 3
                                  ? "三级"
                                  : ""
                              }}
                            </td>
                            <td>{{ student.degree || "" }}</td>
                            <td>{{ student.school || "" }}</td>
                            <td>{{ student.deptName || "" }}</td>
                            <td>{{ student.jobName || "" }}</td>
                            <td>{{ student.gradeName || "" }}</td>
                            <td>
                              {{
                                student.useDay1 == null
                                  ? ""
                                  : (student.fdMonth == 6 ? "满" : "第") +
                                    student.useDay1 +
                                    "年"
                              }}
                            </td>

                            <td>{{ toFixed1(student.meetingEnergyPoint) }}</td>
                            <td>{{ student.meetingStablity || "" }}</td>
                            <td
                              :style="{
                                color:
                                  toFixed1(student.avgPoint) ==
                                  toFixed1(student.meetingPoint)
                                    ? ''
                                    : 'red',
                              }"
                            >
                              {{ toFixed1(student.avgPoint) }}
                            </td>

                            <td
                              :style="{
                                color:
                                  student.ablityLevel == student.meetingLevel
                                    ? ''
                                    : 'red',
                              }"
                            >
                              {{ student.ablityLevel || "" }}
                            </td>
                            <td
                              :style="{
                                color:
                                  toFixed1(student.avgPoint) ==
                                  toFixed1(student.meetingPoint)
                                    ? ''
                                    : 'red',
                              }"
                            >
                              {{ toFixed1(student.meetingPoint) }}
                            </td>

                            <td
                              :style="{
                                color:
                                  student.ablityLevel == student.meetingLevel
                                    ? ''
                                    : 'red',
                              }"
                            >
                              {{ student.meetingLevel || "" }}
                            </td>
                            <td>
                              {{
                                ((student.adjustLevel || "") == ""
                                  ? student.meetingLevel
                                  : student.adjustLevel) || ""
                              }}
                            </td>
                            <td>{{ student.adjustResult }}</td>

                            <td>
                              <a @click="viewGoodsAbsence(student.fdId)">
                                <img
                                  src="../../../../assets/images/action_dtl.png"
                                  alt=""
                              /></a>
                            </td>
                            <td>
                              <span
                                @click="
                                  viewGoodsAbsence2(
                                    student.fdId,
                                    student.userName
                                  )
                                "
                              >
                                <img
                                  src="../../../../assets/images/action_dtl.png"
                              /></span>
                            </td>
                            <td>
                              <a @click="viewPointDetails(student)">
                                <img
                                  src="../../../../assets/images/action_dtl.png"
                                  alt=""
                              /></a>
                            </td>
                            <td style="text-align: left">
                              <template v-if="student.studentStatus >= 11">
                                {{ student.bakRemark || "" }}
                              </template>
                              <template v-else>
                                <template v-if="student.bakRemark">
                                  <a @click="showRemarkDialog(student)">{{
                                    student.bakRemark
                                  }}</a>
                                </template>
                                <template v-else>
                                  <a
                                    @click="showRemarkDialog(student)"
                                    style="
                                      color: blue;
                                      text-decoration: underline;
                                      cursor: pointer;
                                    "
                                    >录入</a
                                  >
                                </template>
                              </template>
                            </td>
                          </tr>
                        </template>
                      </template>
                    </template>
                  </template>
                </tbody>
              </table>
              <span v-if="tableDataLength <= 0">
                {{ tableDataLength == -1 ? "加载中..." : "暂无数据" }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  findListByFdYear,
  findGraduateYear,
  commitStudentToOA,
  findUserAuth,
  updateMeetingRemark,
} from "@/api";
import { getCookie, UserKey } from "@/utils/Cookie";
export default {
  data() {
    return {
      tableData: [],
      companyGroupId: "1",
      schoolLevel: "",
      graduate_year: "",
      year: "",
      month: "",
      checkYear: "",
      approveStatus: "",
      body_height: 0,
      mySort: "",
      buttonQueryText: "按届别显示",
      authCompanyName: "",
      authCommit: "",
      loginUser: {},
      currentRemarkData: null,
      remarkDialogVisible: false,
      remarkContent: "",
      companyMergeMap: new Map(),
      tableDataLength: -1,
    };
  },
  computed: {
    disabledButton() {
      if (this.isLoading) {
        return true;
      } else if (
        this.tableData == null ||
        this.authCompanyName === "您无权限查阅"
      ) {
        return true;
      } else if (
        this.tableData &&
        this.tableData[0] &&
        this.tableData.length != 0
      ) {
        return this.tableData[0].list[0].studentStatus >= 11 ? true : false;
      }
      return false;
    },
    isAuthCommit() {
      if (this.authCommit == "有权限") {
        return "";
      }
      return "none";
    },
    processedTableData() {
      if (!this.tableData || this.tableData.length === 0) {
        return [];
      }

      // 完全重写数据处理逻辑，更简单、更可靠
      const companies = [];
      const companyMap = {};

      // 第一步：遍历并收集所有公司信息
      this.tableData.forEach((item) => {
        if (this.isPositionLevel(item.companyName)) {
          // 处理职级标题行
          const levelTitle = item.companyName;

          // 按公司分组
          const companyGroups = {};
          item.list.forEach((student) => {
            const companyName = student.companyName;
            if (!companyGroups[companyName]) {
              companyGroups[companyName] = [];
            }
            companyGroups[companyName].push(student);
          });

          // 为每个公司添加此职级
          Object.keys(companyGroups).forEach((companyName) => {
            if (!companyMap[companyName]) {
              const company = {
                name: companyName,
                levels: [],
                totalRows: 0,
              };
              companyMap[companyName] = company;
              companies.push(company);
            }

            // 添加职级和员工
            companyMap[companyName].levels.push({
              title: levelTitle,
              employees: companyGroups[companyName],
            });

            // 更新行数：1行职级标题 + 员工数
            companyMap[companyName].totalRows +=
              1 + companyGroups[companyName].length;
          });
        } else {
          // 处理普通公司
          if (!companyMap[item.companyName]) {
            const company = {
              name: item.companyName,
              levels: [],
              totalRows: 0,
            };
            companyMap[item.companyName] = company;
            companies.push(company);
          }

          // 添加员工
          if (item.list && item.list.length > 0) {
            companyMap[item.companyName].levels.push({
              title: item.title || "",
              employees: item.list,
            });
            companyMap[item.companyName].totalRows += 1 + item.list.length;
          }
        }
      });

      return companies;
    },
  },
  methods: {
    _findListByFdYear(queryType) {
      if (this.$store.state.doubleCol.arrAuths.point_ablity_findListByFdYear) {
        findListByFdYear({
          fdYear: this.$route.params.year,
          fdMonth: this.$route.params.month,
          sortType: this.mySort,
        }).then((res) => {
          this.tableData = res.data || [];
          this.tableDataLength = this.tableData.length;
          if (this.tableData && this.tableData.length > 0) {
            this.tableData.forEach((item) => {
              if (item.list && item.list.length > 0) {
                item.list.forEach((data, index) => {
                  data.sortIndex2 = index + 1;
                });
              }
            });
          }

          let studentStatus = this.tableData[0].list[0].studentStatus;
          this.authCompanyName = this.tableData[0].companyName;
          if (studentStatus == 30) {
            this.approveStatus = "已审批";
          } else if (studentStatus == 20) {
            this.approveStatus = "审批中";
          } else if (studentStatus == 11 || studentStatus == 12) {
            this.approveStatus = "已提交";
          } else {
            this.approveStatus = "待提交";
          }

          this.$nextTick(() => {
            this.preprocessTableData();
          });
        });
      } else {
        layer.msg(`对不起，您没有权限查询本界面.`, {
          icon: 2,
          shade: 0.3,
          shadeClose: true,
        });
      }
    },
    _findUserAuth() {
      findUserAuth().then((res) => {
        this.authCommit = res.data || "";
        console.log("res.data-" + res.data);
      });
      console.log("authCommit-" + this.authCommit);
    },
    _commitStudentToOA() {
      console.log(this.month);
      if (this.disabledButton) {
        return;
      }
      layer.confirm(`确认要提交OA审批吗?`, { icon: 3 }, (index) => {
        this.isLoading = true;
        let arr = [];
        this.tableData.forEach((item) => {
          item.list.forEach(({ fdId, fdYear, companyGroupId }) => {
            arr.push({
              fdId: fdId,
              fdYear: fdYear,
              fdMonth: this.month,
              companyGroupId: companyGroupId,
            });
          });
        });

        commitStudentToOA(arr)
          .then((res) => {
            let result = res.success;
            layer.msg(`提交OA${result ? "成功" : "失败"}！${res.msg || ""}`, {
              icon: result ? 1 : 2,
              shade: 0.3,
              shadeClose: true,
            });
            this._findListByFdYear(this.mySort);
          })
          .catch((err) => {
            layer.msg("提交OA失败！" + (err.msg || ""), {
              icon: 2,
              shade: 0.3,
              shadeClose: true,
            });
          });
      });
    },
    viewPointDetails(data) {
      this.$router.push({
        path:
          "/" +
          this.year +
          "/" +
          this.month +
          "/Trainee/Evaluation/ScoreDetail_all",
      });
    },
    viewGoodsAbsence(fdId) {
      var url =
        "/" +
        this.year +
        "/" +
        this.month +
        "/Trainee/Evaluation/" +
        this.companyGroupId +
        "/addGoodsAbsence";
      this.$router.push({
        path: url,
        query: {
          ablityId: fdId,
          isadd: 0,
          year: this.year,
          month: this.month,
          companyGroupId: this.companyGroupId,
        },
      });
    },
    viewGoodsAbsence2(fdId, name) {
      var url =
        "/" +
        this.year +
        "/" +
        this.month +
        "/trainee/assessor2/" +
        this.companyGroupId +
        "/summary";
      this.$router.push({
        path: url,
        query: {
          fdId: fdId,
          name: name,
          year: this.year,
          month: this.month,
          companyGroupId: this.companyGroupId,
        },
      });
    },
    viewLastYear() {
      let lastYear = null;
      let lastMonth = 6;
      var url = "";
      if (this.month == 6) {
        lastYear = parseInt(this.year) - 1;
        url =
          "/" +
          lastYear +
          "/" +
          lastMonth +
          "/trainee/evaluation/meetingLastResult";
      } else {
        lastYear = parseInt(this.year);
        url =
          "/" +
          lastYear +
          "/" +
          lastMonth +
          "/trainee/evaluation/meetingLastResult";
      }
      this.$router.push({
        path: url,
        query: {
          year: lastYear,
          month: lastMonth,
        },
      });
    },
    viewStudentApprove() {
      var url =
        "/" +
        this.year +
        "/" +
        this.month +
        "/trainee/evaluation/studentApprove";
      let flag = 0;
      if (this.tableData && this.tableData[0] && this.tableData.length != 0) {
        if (this.tableData[0].list[0].studentStatus >= 11) {
          flag = 1;
        }
      }
      this.$router.push({
        path: url,
        query: {
          year: this.year,
          month: this.month,
          flag: flag,
        },
      });
    },
    showDescrDialog() {
      Utils.layerBox.layerDialogOpen({
        title: "<b>评定说明：</b>",
        area: ["900px", "500px"],
        btn: [],
        content: `<div style="padding:10px 20px 0 20px;margin:0 auto;">
          <p style="line-height:24px;">
          一、评定方式<br/>
          1、成功要素：指的是运用成功要素（三大特质九大要素）对评定对象进行逐项评定。<br/>
          2、综合分：指的是根据评定对象的整体能力表现进行一个综合评定。<br/>
          二、评定人<br/>
          （一）评定人资格<br/>
          1、成功要素评定人资格：原则上需是熟悉成功要素标准（三大特质九大要素）且对评定对象的能力表现有足够了解的评定人。<br/>
          2、综合分评定人资格<br/>
          ①评定人属于首次接触成功要素标准，尚不能完全掌握其核心要点的，采用综合分方式评定。<br/>
          ②评定人熟悉成功要素标准但因对评定对象的能力表现只有整体的认知和链接，无法逐一对应成功要素进行评定的，采用综合分方式评定。<br/>
          （二）评定人人员及人数<br/>
          1、评定人员：从评定对象的业务关联领导、直接领导和间接领导中找到最熟悉其工作能力表现的人员。<br/>
          2、评定人数：2-5人。<br/>
          三、评定时间：每年6月份、12月份各评定1次。<br/>
          四、评定得分<br/>
          （一）评定选项及对应得分：（1）优：85-100分；（2）A：80-84分；（3）B：75-79分；（4）C：70-74分；（5）D：0-69分<br/>
          （二）积极性及稳定性得分：（1）积极性评分在70以上，则为积极；（2）稳定性评分在70分以上，则为稳定。<br/>
          （三）权重及得分<br/>
          1、评定人数为5人的，间接领导权重为30%，直接领导权重为20%，人资领导权重为20%，业务关联领导均为15%；<br>
          2、评定人数为4人的，间接领导权重为40%，直接领导权重为30%，2个业务关联领导均为15%；<br/>
          3、评定人数为3人的<br/>
            （1）有间接领导的：间接领导权重为50%，直接领导权重为25%，业务关联领导为25%；<br>
            （2）没有间接领导的：直接领导权重为50%，2个关联业务领导各占25%；<br/>
          4、评定人数为2人的<br/>
          （1）没有间接领导的：直接领导权重为60%，业务关联领导为40%；<br/>
          （2）有间接领导的：间接领导权重为60%，业务关联领导为40%。<br/>
          五、评定流程简要说明<br/>
          （一）评定人及评定方式确定：根据以上原则由人资部初步选定，选定后提交OA呈报各公司负责的领导审批。<br/>
          （二）能力评定<br/>
          1、评定流程：在评定人及评定方式审批后，系统自动触发OA评定表到各评定人的评定工作台进行评定。<br/>
          2、评定标准<br/>
          （1）以评定对象职务职等规划的当前阶段应该达到的能力水平作为"优秀"标准，比如二级管培生×××入职已经第四年，职务等级为三级经理，需以三级经理应该达到的能力水平作为优秀标准来进行评定。<br/>
          （2）评定人应对同一届别同一岗位级别的管培生进行横向的对比，原则上应该有能力的高低差异区分，人数较少的例外。<br/>
          （三）评定合议<br/>
          1、合议人：评定完成后，由集团统一组织各公司体系领导进行合议。<br/>
          2、合议单位：以同一公司、同一届别、同一岗位级别的人员为基本单位进行合议，理由主要是不同届别和岗位级别的管培生能力存在一定差别，而且培养的目标职务和年限也有所差别，放在一起合议不具备横向的可对比性。<br/>
          3、合议确定内容：评定对象的综合能力评定等级（优、A、B、C、D级）、积极态度以及稳定性。<br/>
          （1）能力等级为优、A、B、C级视为合格，D级视为不合格<br/>
          ①优级：提资时间按照A级（6个月提资一次）执行，同时可考虑提前晋升；<br/>
          ②A级：6个月提资一次；<br/>
          ③B级：9个月提资一次；<br/>
          ④C级：12个月提资一次；<br/>
          ⑤D级：原则上劝退；<br/>
          （2）积极态度和稳定性：具有一票否决权。<br/>
          （四）确定报批名单：默认所有管培生评定结果均需报批，即"是否提交审批"选项默认为"是"，该项为"是"的管培生，才可提交OA报批，若部分管培生经合议后，认为已不需呈报董事长审批的，则将选项调整为"否"，即不需提交OA审批。 <br/>
          （五）评定合议结果报批：在各公司合议完成后，以全集团为单位进行汇总报批。<br/>
          </p></div>`,
      });
    },
    toGoodAbsence(item) {
      const { companyGroupId, year, month } = this.$route.params;
      this.companyGroupId = companyGroupId;
      this.year = year;
      this.month = month;
      var ablityId = item.fdId;
      var url =
        "/" +
        this.year +
        "/" +
        this.month +
        "/Trainee/Staff/" +
        this.companyGroupId +
        "/viewGoodAbsence";

      this.$router.push({
        path: url,
        query: {
          ablityId: ablityId,
          year: this.year,
          month: this.month,
          companyGroupId: this.companyGroupId,
        },
      });
    },
    toFixed2(num) {
      if (num) {
        let num_str = num.toString().split(".");
        if (num_str.length == 1) {
          return num_str[0] + ".0";
        } else {
          return num_str[0] + "." + num_str[1].substring(0, 1);
        }
      } else {
        return "";
      }
    },
    toFixed1(num) {
      if (num) {
        let num_str = num.toString().split(".");
        if (num_str.length == 1) {
          return num_str[0] + ".0";
        } else {
          return num_str[0] + "." + num_str[1].substring(0, 1);
        }
      } else {
        return "";
      }
    },
    initPage() {
      this.body_height = $(window).height();
      const { year, month } = this.$route.params;
      this.mySort = "1";
      this._findListByFdYear("1");
      this._findUserAuth();
      this.year = year;
      this.month = month;
      if (month == 6) {
        this.checkYear = parseInt(year) - 1 + "-" + year + "年度";
      } else {
        this.checkYear = year + "-" + (parseInt(year) + 1) + "半年度";
      }
    },
    sheetIt() {
      this.exportExcelOne.exportExcel(
        `管培生综合能力评定审批结果.xlsx`,
        "#table"
      );
    },
    isPositionLevel(name) {
      if (!name) return false;

      return (
        name.includes("级") || name.includes("人") || name.includes("经理级")
      );
    },
    getCompanyGroups(list) {
      if (!list || !list.length) return [];

      const companyMap = new Map();

      list.forEach((item, index) => {
        const companyName = item.companyName || "未知公司";

        if (!companyMap.has(companyName)) {
          companyMap.set(companyName, {
            name: companyName,
            data: [],
          });
        }

        item.sortIndex2 = item.sortIndex2 || index + 1;

        companyMap.get(companyName).data.push(item);
      });

      return Array.from(companyMap.values());
    },
    adjustTableWidth() {
      setTimeout(() => {
        const headerWrapper = document.querySelector(".table-header-wrapper");
        const bodyWrapper = document.querySelector(".table-body-wrapper");
        const headerTable = document.querySelector(".scroll-table-header");
        const bodyTable = document.querySelector(".table-body-wrapper > table");

        if (!headerWrapper || !bodyWrapper || !headerTable || !bodyTable)
          return;

        // 获取滚动条是否可见
        const scrollbarVisible =
          bodyWrapper.scrollHeight > bodyWrapper.clientHeight;

        // 测量滚动条宽度
        const scrollbarWidth = scrollbarVisible
          ? bodyWrapper.offsetWidth - bodyWrapper.clientWidth
          : 0;

        // 方案1：如果隐藏了滚动条，则不调整表头宽度
        // headerWrapper.style.width = '100%';

        // 方案2：如果保留滚动条，动态调整表头宽度
        headerWrapper.style.width = scrollbarWidth
          ? `calc(100% - ${scrollbarWidth}px)`
          : "100%";

        // 确保表列宽一致
        // ... 其余代码不变
      }, 50);
    },
    showRemarkDialog(data) {
      if (data.studentStatus >= 11) return;

      this.currentRemarkData = data;
      this.remarkContent = data.bakRemark || "";

      layer.open({
        type: 1,
        title: "编辑备注",
        area: ["500px", "300px"],
        content: `<div style="padding: 20px;">
          <textarea id="remarkTextarea" class="form-control" style="width: 100%; height: 150px; resize: none;">${this.remarkContent}</textarea>
          <div style="margin-top: 20px; text-align: center;">
            <button id="saveRemarkBtn" class="btn btn-primary">保存</button>
            <button id="cancelRemarkBtn" class="btn btn-default" style="margin-left: 10px;">取消</button>
          </div>
        </div>`,
        success: (layero, index) => {
          document.getElementById("saveRemarkBtn").onclick = () => {
            const remarkValue = document.getElementById("remarkTextarea").value;
            this.saveRemark(remarkValue, index);
          };

          document.getElementById("cancelRemarkBtn").onclick = () => {
            layer.close(index);
          };
        },
      });
    },
    saveRemark(remarkValue, layerIndex) {
      if (!this.currentRemarkData) return;

      const params = {
        fdId: this.currentRemarkData.fdId,
        remark: remarkValue,
      };

      updateMeetingRemark(params).then((res) => {
        let result = res.success;
        layer.msg(`保存${result ? "成功" : "失败"}！${res.msg || ""}`, {
          icon: result ? 1 : 2,
          shade: 0.3,
          shadeClose: true,
        });

        if (result) {
          this.currentRemarkData.bakRemark = remarkValue;
          layer.close(layerIndex);
        }
      });
    },
    preprocessTableData() {
      this.companyMergeMap = new Map();

      let visibleRowCount = 0;
      let currentCompany = null;
      let companyStartRow = 0;
      let companyRowspan = 0;

      this.tableData.forEach((item, index) => {
        if (this.isPositionLevel(item.companyName)) {
          visibleRowCount++;

          item.list.forEach((student) => {
            const companyName = student.companyName;

            if (companyName !== currentCompany) {
              if (currentCompany && companyRowspan > 0) {
                this.companyMergeMap.set(companyStartRow, {
                  company: currentCompany,
                  rowspan: companyRowspan,
                });
              }

              currentCompany = companyName;
              companyStartRow = visibleRowCount;
              companyRowspan = 1;
            } else {
              companyRowspan++;
            }

            visibleRowCount++;
          });
        } else {
          if (item.companyName !== currentCompany) {
            if (currentCompany && companyRowspan > 0) {
              this.companyMergeMap.set(companyStartRow, {
                company: currentCompany,
                rowspan: companyRowspan,
              });
            }

            currentCompany = item.companyName;
            companyStartRow = visibleRowCount;
            companyRowspan = item.list.length;
          } else {
            companyRowspan += item.list.length;
          }

          visibleRowCount += item.list.length;
        }
      });

      if (currentCompany && companyRowspan > 0) {
        this.companyMergeMap.set(companyStartRow, {
          company: currentCompany,
          rowspan: companyRowspan,
        });
      }
    },
    processTableData() {
      // 公司分组结果
      const companyGroups = [];
      let currentCompany = null;
      let currentCompanyData = null;

      // 遍历原始表格数据
      this.tableData.forEach((item) => {
        // 处理职级标题行
        if (this.isPositionLevel(item.companyName)) {
          const levelName = item.companyName;
          const companies = this.getCompanyGroups(item.list);

          companies.forEach((company) => {
            // 如果是新公司或与当前处理的公司不同
            if (!currentCompany || currentCompany !== company.name) {
              // 保存前一个公司的数据
              if (currentCompanyData) {
                companyGroups.push(currentCompanyData);
              }

              // 创建新公司数据
              currentCompany = company.name;
              currentCompanyData = {
                companyName: company.name,
                levelGroups: [],
                totalRows: 0,
              };
            }

            // 确保员工数据存在
            if (company.data && company.data.length > 0) {
              // 添加职级组
              currentCompanyData.levelGroups.push({
                levelName: levelName,
                employees: company.data,
              });

              // 更新总行数：职级标题行 + 员工行数
              currentCompanyData.totalRows += 1 + company.data.length;
            }
          });
        } else {
          // 处理普通公司行
          if (!currentCompany || currentCompany !== item.companyName) {
            if (currentCompanyData) {
              companyGroups.push(currentCompanyData);
            }

            currentCompany = item.companyName;
            currentCompanyData = {
              companyName: item.companyName,
              levelGroups: [],
              totalRows: 0,
            };
          }

          // 只有当列表非空时才添加
          if (item.list && item.list.length > 0) {
            currentCompanyData.levelGroups.push({
              levelName: item.title || "",
              employees: item.list,
            });

            currentCompanyData.totalRows += 1 + item.list.length; // 标题行 + 员工行
          }
        }
      });

      // 添加最后一个公司
      if (currentCompanyData) {
        companyGroups.push(currentCompanyData);
      }

      return companyGroups;
    },
    getUniqueCompanies(students) {
      if (!students || !students.length) return [];
      const companies = new Set();
      students.forEach((student) => {
        if (student.companyName) companies.add(student.companyName);
      });
      return Array.from(companies);
    },
    getStudentsByCompany(students, companyName) {
      if (!students || !students.length) return [];
      return students.filter((student) => student.companyName === companyName);
    },
    isFirstPositionLevelForCompany(companyName, currentIndex) {
      for (let i = 0; i < currentIndex; i++) {
        const item = this.tableData[i];
        if (this.isPositionLevel(item.companyName)) {
          const companies = this.getUniqueCompanies(item.list);
          if (companies.includes(companyName)) {
            return false;
          }
        } else if (item.companyName === companyName) {
          return false;
        }
      }
      return true;
    },
    getCompanyTotalRows(companyName) {
      let totalRows = 0;

      this.tableData.forEach((item) => {
        if (this.isPositionLevel(item.companyName)) {
          const students = this.getStudentsByCompany(item.list, companyName);
          if (students.length > 0) {
            totalRows += 1 + students.length;
          }
        } else if (item.companyName === companyName) {
          totalRows += item.list ? item.list.length : 0;
        }
      });

      return totalRows;
    },
  },
  watch: {
    $route: function () {
      this.initPage();
    },
    tableData: {
      handler() {
        this.$nextTick(() => {
          this.adjustTableWidth();
        });
      },
      deep: true,
    },
  },
  mounted() {
    this.initPage();
    this.loginUser = JSON.parse(getCookie(UserKey) || "{}");
    $(window).resize(() => {
      this.body_height = $(window).height();
    });
    this.$nextTick(() => {
      this.adjustTableWidth();

      setTimeout(() => this.adjustTableWidth(), 100);
      setTimeout(() => this.adjustTableWidth(), 500);
      setTimeout(() => this.adjustTableWidth(), 1000);
    });
    window.addEventListener("resize", this.adjustTableWidth);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.adjustTableWidth);
  },
};
</script>

<style>
/* 恢复表体的滚动行为 */
.table-body-wrapper {
  width: 100%;
  overflow-y: auto; /* 重要：启用垂直滚动 */
  overflow-x: hidden;
  /* 保持隐藏滚动条的样式 */
  scrollbar-width: none;
  -ms-overflow-style: none;
}

/* 确保表格容器自身不溢出 */
.result-detail-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止容器自身溢出 */
}

/* 让表格部分占用剩余空间并可滚动 */
.row {
  flex-shrink: 0; /* 防止行被压缩 */
}

/* 表格行应该占用剩余空间 */
.list-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止表格容器溢出 */
}

.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止表格容器溢出 */
}

/* 方案1：隐藏滚动条但保留滚动功能 */
.table-body-wrapper::-webkit-scrollbar {
  width: 0; /* Chrome, Safari, Edge */
  display: none;
}

.table-body-wrapper {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* 恢复表头宽度 */
.table-header-wrapper {
  width: 100%; /* 现在不需要减去滚动条宽度 */
  overflow: hidden;
}

/* 或者方案2：保留滚动条但自动调整表头宽度 */
.scroll-table {
  width: 100%;
  position: relative;
}

.scroll-table-header {
  position: relative;
  z-index: 2;
}

.scroll-table-body {
  position: relative;
  z-index: 1;
}

.table-bordered th,
.table-bordered td {
  box-sizing: border-box;
}

.scroll-table-body::-webkit-scrollbar {
  width: 17px;
}

.scroll-table-header,
.table-body-wrapper > table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
}

.scroll-table-header th,
.table-body-wrapper td {
  box-sizing: border-box;
  white-space: normal;
  word-wrap: break-word;
  word-break: normal;
}

.scroll-table-header {
  position: relative;
  z-index: 2;
  background-color: #fff;
}

.table-body-wrapper::-webkit-scrollbar {
  width: 17px;
}

.table-body-wrapper {
  scrollbar-width: 17px;
}

.form-control.input-sm {
  border-radius: 3px;
  transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
}

.form-control.input-sm:focus {
  border-color: #66afe9;
  outline: 0;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075),
    0 0 8px rgba(102, 175, 233, 0.6);
}

.form-control.input-sm[disabled] {
  background-color: #eee;
  opacity: 1;
  cursor: not-allowed;
}

img[alt="编辑备注"] {
  transition: transform 0.2s;
}

img[alt="编辑备注"]:hover {
  transform: scale(1.2);
}
</style>
