<template>
  <div class="grade-evaluation-dialog-container">
      <div class="row">
        <div style="font-size:20px;font-weight:bold;">{{this.month==6?(this.year-1):this.year}}-{{this.month==6?this.year:(this.year+1)}}{{this.month==12?"半":""}}年度{{this.companyGroupName}}管培生综合能力评定操作表</div>
        <div class="col-xs-12">
            <div style="text-align:left;float:left;margin-top:10px;">
                <span style="margin-left:30px;font-weight:bold;">评定人：</span>{{this.$route.query.leaderName}}
                <span style="margin-left:30px;font-weight:bold;">评定开始时间：</span>{{this.$route.query.sendDate}}
                <span style="margin-left:30px;font-weight:bold;">要求完成时间：</span>{{this.$route.query.requireFinishDate}}
            </div>
           <div style="float:right;margin-right:30px;margin-top:10px;">
               <button type="button" class="btn btn-primary btn-xs" 
               @click="$router.push(`/${$route.params.year}/${$route.params.month}/Trainee/Staff/${$route.params.companyGroupId}/FlowStart_list`)">
               返回上级</button>
           </div>
          <table class="table table-bordered">
            <thead>
              <colgroup>
                <col style="width:3%"/>
                <col style="width:6%"/>
                <col style="width:6%"/>
                <col style="width:5%"/>
                <col style="width:7%"/>
                <col style="width:7%"/>
                <col style="width:7%"/>

                <col style="width:4%"/>
                <col style="width:3%"/>
                <col style="width:4%"/>
                <col style="width:3%"/>
                <col style="width:3%"/>
                <col style="width:3%"/>
                <col style="width:4%"/>
                <col style="width:3%"/>
                <col style="width:4%"/>

                <col style="width:4%"/>
                <col style="width:4%"/>
                <col style="width:4%"/>
                <col style="width:4%"/>
                <col style="width:4%"/>
                <col style="width:4%"/>
                <col style="width:5%"/>
                <col style="width:6%"/>
              </colgroup>
              <tr>
                <th rowspan="2">序号</th>
                <th colspan="5" >评定对象</th>
                <th rowspan="2">评定方式</th>
                <th colspan="9" >成功要素评定得分明细</th>
                <th rowspan="2">评定<br>得分</th>
                <th rowspan="2">能力<br>等级</th>
                <th colspan="2">积极态度</th>
                <th colspan="2">稳定性</th>
                <th rowspan="2">考核结论</th>
                <th rowspan="2">评定操作</th>
              </tr>
              <tr>
                <th>姓名</th>
                <th>届别</th>
                <th>部门</th>
                <th>职务</th>
                <th>职等</th>

                <th>思维与<br>行动</th>
                <th>洞察力</th>
                <th>专长与<br>应用</th>
                <th>提高能力</th>
                <th>纪律性</th>
                <th>领导力</th>
                <th>创新与<br>借签</th>
                <th>拥抱变化</th>
                <th>营造多样<br>化合作</th>
                <th>得分</th>
                <th>结论</th>
                <th>得分</th>
                <th>结论</th>
              </tr>
            </thead>
            <tbody>
              <colgroup>
                <col style="width:3%"/>
                <col style="width:6%"/>
                <col style="width:6%"/>
                <col style="width:5%"/>
                <col style="width:7%"/>
                <col style="width:7%"/>
                <col style="width:7%"/>

                <col style="width:4%"/>
                <col style="width:3%"/>
                <col style="width:4%"/>
                <col style="width:3%"/>
                <col style="width:3%"/>
                <col style="width:3%"/>
                <col style="width:4%"/>
                <col style="width:3%"/>
                <col style="width:4%"/>

                <col style="width:4%"/>
                <col style="width:4%"/>
                <col style="width:4%"/>
                <col style="width:4%"/>
                <col style="width:4%"/>
                <col style="width:4%"/>
                <col style="width:5%"/>
                <col style="width:6%"/>
              </colgroup>
              <tr v-for="(data,$index) in tableData" :key="$index">
                <td>{{$index+1}}</td>                
                <td>{{data.userName}}</td>
                <td>{{data.graduateYear}}</td>
                <td>{{data.deptName}}</td>
                <td>{{data.jobName}}</td>                
                <td>{{data.gradeName}}</td>
                <td>{{data.judgeType}}</td>                

                <td>{{data.mindAction}}</td>
                <td>{{data.sharpEye}}</td>
                <td>{{data.mindSkill}}</td>
                <td>{{data.groupImprove}}</td>
                <td>{{data.sharpDiscipline}}</td>
                <td>{{data.groupLeader}}</td>
                <td>{{data.mindNew}}</td>
                <td>{{data.sharpEmbrace}}</td>
                <td>{{data.groupCooperate}}</td>

                <td>{{data.avgPoint == null?'':data.avgPoint.toFixed(1)}}</td>
                <td>{{data.ablityLevel}}</td>
                <td>{{data.energyPoint}}</td>
                <td>{{data.energyPoint==null?"":(data.energyPoint>=70?"积极":"不积极")}}</td>
                <td>{{data.stablityPoint}}</td>
                <td>{{data.stablity}}</td>
                <td>{{data.stayStatus}}</td>
                <td>
                    <a @click="toChildPage(data.ablityId,data.judgeType)">{{data.avgPoint == null?'开始评定':'修改评定'}}</a>
                </td>
              </tr>
            </tbody>
          </table>
          <div style="text-align:left;">
            一、管培生综合能力评定流程<br/>
              1、评定人独立评定→评定结果合议确定能力等级（优、A、B、C、D）→评定结果审批。<br/>
              二、评定方式：<br/>
              1、成功要素：指的是运用成功要素（九大要素）对评定对象进行逐项评定，评定人需熟悉成功要素。 <br/>
              2、综合分：指的是根据评定对象的整体能力表现进行一个综合评定，评定人不熟悉成功要素。

          </div>
        </div>
      </div>
  </div>
</template>

<script>
import { findStudentPointListByConfirmLeaderId } from "@/api";

export default {
  data() {
    return {
      body_height: 0,
      year: "",
      month: "", 
      companyGroupId: "",
      companyGroupName:"",
      tableData: [],
    };
  }, 

  methods: {
    findList() {
        findStudentPointListByConfirmLeaderId({
            confirmLeaderId:this.$route.query.confirmLeaderId,
            leaderName: this.$route.query.leaderName
            }).then(res => {
                if (!res.success) return;
                this.tableData = res.data ? res.data : [];
                if (this.tableData.length>0){
                  this.year = this.tableData[0].fdYear;
                  this.month = this.tableData[0].fdMonth;
                  this.companyGroupName = this.tableData[0].companyGroupName;     
                }                     
            }).catch(err=>{
                layer.msg(`${err.msg}`, {icon: 2,shade:0.3,shadeClose:true});
            });
            console.log("test!");
        },
        toChildPage(ablityId,judgeType){      
          const {
              companyGroupId,
              year,
              month,
              positionGradeId
          } = this.$route.params;
          this.companyGroupId = companyGroupId;
          this.year = year;
          this.month = month;

          var url=""; 
          if (judgeType=="成功要素"){
            var url='/'+this.year+'/'+this.month+'/Trainee/Staff/'+this.companyGroupId+'/FlowStartDetails_point1';
          }
          else {
            url='/'+this.year+'/'+this.month+'/Trainee/Staff/'+this.companyGroupId+'/FlowStartDetails_point2';           
          }
          
          this.$router.push({path:url,
            query:{ablityId:ablityId,
              judgeType:judgeType,
              confirmLeaderId:this.$route.query.confirmLeaderId,
              leaderName:this.$route.query.leaderName,
              sendDate:this.$route.query.sendDate,              
              requireFinishDate:this.$route.query.requireFinishDate
            }
          });
      },
    },

    
    initPage() {
        this.pageResize();
        
  
    },
    pageResize() {
        this.body_height = $(window).height();
    },
    created() {
        this.findList();
    }
};

</script>
