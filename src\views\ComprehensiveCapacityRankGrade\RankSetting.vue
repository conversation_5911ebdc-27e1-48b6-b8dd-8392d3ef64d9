<template>
  <div class="rank-setting-container">
    <div class="row" style="line-height:40px;max-width:825px">
        <div class="col-xs-12 text-left">
          <h4 class="col-xs-7" style="padding: 0;">【{{getCompanyName()}}】管理人员综合能力【职级】配置表</h4>
          <div class="col-xs-5 text-right" style="padding: 0;">
            <button class="btn btn-primary btn-xs" @click="dialogShow(true)"
            v-if="$store.state.doubleCol.arrAuths.base_baseLevelDetails_updateBatch" >新增职级</button>
            <!-- $router.push(`/${$route.params.year}/${$route.params.month}/menu/1`) -->
            <button class="btn btn-primary btn-xs" @click="$router.back()">返回目录</button>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-xs-12">
          <div class="list-table" style="width:600px;">
            <div class="scroll-table" style="width:100%;height:100%;overflow-x:hidden;">
                <table class="scroll-table-header table-bordered">
                  <colgroup>
                    <col style="width:8%">
                    <col style="width:23%">
                    <col style="width:23%">
                    <col style="width:23%">
                    <col style="width:23%">
                  </colgroup>
                  <thead>
                    <tr>
                      <th>序号</th>
                      <th>职等</th>
                      <th>级别</th>
                      <th>职级</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                </table>
                <div class="scroll-table-body" :style="{overflow:'auto',maxHeight:body_height - 135 + 'px','overflow-y':'scroll','overflow-x':'hidden'}">
                    <table style="margin:0px 10px 0 0;position: relative;font-size:13px;float:left;border:none;" class="table table-bordered table-hover table-striped">
                        <colgroup>
                          <col style="width:8%">
                          <col style="width:23%">
                          <col style="width:23%">
                          <col style="width:23%">
                          <col style="width:23%">
                        </colgroup>
                        <tbody>
                          <tr v-for="(data,$index) in tableData" :key="$index">
                            <td v-if="data.start" :rowspan="data.rowspan">{{data.order}}</td>
                            <td v-if="data.start" :rowspan="data.rowspan">{{data.positionGradeName}}</td>
                            <td v-if="data.start_level" :rowspan="data.rowspan_level">{{data.level}}</td>
                            <td>{{data.levelGrade}}</td>
                            <td v-if="data.start_level" :rowspan="data.rowspan_level">
                              <a @click="editHandle(data)" style="padding:0 15px;"
                              v-if="$store.state.doubleCol.arrAuths.base_baseLevelDetails_updateBatch">编辑</a>
                              <a @click="deleteHandle(data.baseLevelId)" style="padding:0 15px;"
                              v-if="$store.state.doubleCol.arrAuths.base_baseLevelDetails_deleteByBaseLevelId">删除</a>
                            </td>
                          </tr>
                      </tbody>
                    </table>
                </div>
            </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { findDetailsListByCompanyGroupId,findListByPositionGradeId,deleteByBaseLevelId,updateBatch
,findDetailsByBaseLevelId,findPositionGradeByCompanyGroupId,findGradeSettingList } from '@/api'
export default {
  data(){
    return {
      isLoading:true,
      body_height: 0,
      tableData:[],
      gradeList:[],
      positionLevelData:[],
      year:"",
      month:"",
      companyGroupId:"",
    }
  },
  methods:{
    getCompanyName(){
      return Utils.getCompanyNameByGroupId(this.companyGroupId);
    },
    _updateBatch(list=[],isAdd){
      if(this.isLoading){
        return;
      }
      this.isLoading = true;
      updateBatch(list).then(res=>{
        let result = res.success;
        let msg = this.isAdd ? '新增' : '编辑';
        layer.msg(`${msg}${result ? '成功' : '失败'}`, {icon: result ? 1 : 2,shade:0.3,shadeClose:true});
        this.findDetailsListByCompanyGroupId();
      })
    },
    _findListByPositionGradeId(positionGradeId){
      return new Promise((resolve,reject)=>{
        findListByPositionGradeId({PositionGradeId:positionGradeId,CompanyGroupId:this.companyGroupId,Year:this.year,Month:this.month}).then(res=>{
            resolve(res.data||[]);
        }).catch(err=>{resolve([])})
      })
    },
    async dialogShow(isAdd,positionGradeName,level,levels=[],baseLevelId){
      let that = this;
      if(isAdd){
        this.positionLevelData = await this._findListByPositionGradeId(this.gradeList[0] ? this.gradeList[0].fdId : '');
      }
      Utils.layerBox.layerDialogOpen({
          area:['400px','250px'],
          content:`<div style="width:100%;height:100%;text-align:center;line-height:32px;box-sizing: border-box;padding: 12px 0">
            <div class="row" style="margin:0">
              <label class="col-xs-4 text-right">职等：</label>
              ${
                isAdd
                ? `<div class="col-xs-8 text-left"><select class="input-sm" style="width:160px;" id="gradeSelect">
                  ${this.gradeList.map(item=> `<option value="${item.fd_id}">${item.grade_name}</option>` ).join("\n")}
                </select></div>`
                :
                `<span class="col-xs-8 text-left">
                  ${positionGradeName}
                  </span>`
                }
            </div>
            <div class="row" style="margin:0;line-height: 20px;margin-top:10px;">
              <label class="col-xs-4 text-right">级别选择：</label>
              <div class="col-xs-8 text-left">
                ${
                isAdd
                ? `<select class="input-sm" id="positionLevel" style="width:120px;">
                </select>`
                : `<span>
                  ${level}
                  </span><input type="hidden" id="positionLevel" value="${level}"/>`
                }
              </div>
            </div>
            <div class="row" style="margin:0;line-height: 20px;margin-top:10px;">
              <label class="col-xs-4 text-right">包含职级：</label>
              <div class="col-xs-8 text-left" id="level_checkbox_wrap">
              </div>
            </div>
          </div>`,
          btn1:function(index){
            let checkbox_arr = $("input[type='checkbox'][name='positionLevel']:checked") || [];
            let checkbox_vals = [];
            for(let i=0;i<checkbox_arr.length;i++){
              let checkbox = checkbox_arr[i];
              checkbox_vals.push($(checkbox).val())
            }
            if(isAdd){
              baseLevelId = $("#positionLevel").val();
            }
            if(checkbox_vals){
              let gradeObj_Arr = []
              checkbox_vals.forEach( val => {
                gradeObj_Arr.push({
                  baseLevelId,
                  levelGrade:val,
                })
              })
              that.isLoading ? null :that._updateBatch(gradeObj_Arr,isAdd);
            }
            layer.close(index)
          },
          success:function(layero,index){
            let appendOption = function(){
              if(isAdd) level = $("#positionLevel option[value='"+$("#positionLevel").val()+"']").text();
              let grade_arr = level === '优' ? ['优'] : level === 'A' ? ['A+','A','A-'] : level === 'B' ? ['B+','B','B-'] : level === 'C' ? ['C+','C','C-'] : [];
              $("#level_checkbox_wrap").empty();
              $("#level_checkbox_wrap").append(`${grade_arr.map(item=> `<label class="checkbox-inline">
                                        <input type="checkbox" name="positionLevel" value="${item}" ${levels.indexOf(item) !== -1 ? 'checked' : ''}>${item}
                                     </label>`).join("\n")}`);
            }
            if(isAdd){
              var appendLevelOption = function(){
                that._findListByPositionGradeId($("select[id='gradeSelect']").val()).then(res=>{
                  that.positionLevelData = res || [];
                  let select_str = (res || []).map((level,index)=> `<option value="${level.fdId}" ${index===0 ? 'selected' : ''}>${level.level}</option>`).join("\n")
                  $("select[id='positionLevel']").empty();
                  $("select[id='positionLevel']").append(select_str);
                  appendOption();
                })
              }
              $("select[id='gradeSelect']").off('change').on('change',appendLevelOption);
              appendLevelOption();
            }else{
              appendOption();
            }
            $("select[id='positionLevel']").off('change').on('change',appendOption);
          }
        });
    },
    editHandle(data={},isAdd=false){
      findDetailsByBaseLevelId({BaseLevelId:data.baseLevelId}).then(res=>{
        let level = "";
        let levelGrade = (res.data||[]).map((item,index)=> { if(index==0){level = data.level;};return item.levelGrade});
        this.dialogShow(false,data.positionGradeName,level,levelGrade,data.baseLevelId);
      })
    },
    deleteHandle(baseLevelId){
      layer.confirm('确认删除此条数据吗？', {
          title:'提示',
          btn: ['确定','取消'] //按钮
        }, ()=>{
          deleteByBaseLevelId({BaseLevelId:baseLevelId}).then(res=>{
            if(res.success){
              layer.msg('删除成功', {icon: 1});
              this.findDetailsListByCompanyGroupId();
            }
            else
              layer.msg('删除失败', {icon: 2});
          })
        });
    },
    findDetailsListByCompanyGroupId(){
      /*先判断有权限才可以查询(modify by huangdh@2019-05-24)*/
      if (this.$store.state.doubleCol.arrAuths.base_baseLevelDetails_findDetailsListByCompanyGroupId)
      {
          findDetailsListByCompanyGroupId({CompanyGroupId:this.companyGroupId,Year:this.year,Month:this.month}).then(res=>{
            this.isLoading = false;
            if(!res.success) return;
            this.eachData(res.data || [])
          })
      }
      else {
          layer.msg(`对不起，您没有权限查询本界面.`, {icon: 2,shade:0.3,shadeClose:true});
      }
    },
    _findPositionGradeByCompanyGroupId(){
      if (this.companyGroupId=="1"){
        findGradeSettingList({companyGroupId:this.companyGroupId}).then(res=>{
          this.gradeList = res.data||[];
        })
      }
      else {
        findPositionGradeByCompanyGroupId({companyGroupId:this.companyGroupId}).then(res=>{
          this.gradeList = res.data||[];
        })
      }
    },
    eachData(data){
      let start = 0, end = 0, start_level = 0, end_level = 0;
      data.forEach((d,index) =>{
        if(index === 0 ){
          d.order = 1;
          d.start = true;
          start = 0;
          d.start_level = true;
          start_level = 0;

        }else{
          if(d.baseLevelId != data[start_level].baseLevelId){
              end_level = index;
              d.start_level = true;
              data[start_level].rowspan_level = end_level - start_level;
              start_level = index;
          }else {
            if(data.length === index + 1){
              data[start_level].rowspan_level = index + 1 - start_level;
            }
          }
          if(d.positionGradeId == data[start].positionGradeId){
            d.order = data[index-1].order;
            if(data.length === index + 1){
              data[start].rowspan = index + 1 - start;//因为索引从0开始，所以index+1
            }
          }else{
            d.start = true;
            d.order = data[index-1].order+1;
            end = index;
            data[start].rowspan = end - start;
            start = index;
          }
        }
      })
      this.tableData = data;
    },
    initPage(){
      const {companyGroupId,year,month} = this.$route.params;
      this.companyGroupId = companyGroupId;
      this.year = year;
      this.month = month;
      this.findDetailsListByCompanyGroupId();
      this._findPositionGradeByCompanyGroupId();
    },
    pageResize(){
      this.body_height = $(window).height();
    }
  },
  watch:{
    "$route":function(){
      this.pageResize();
      this.initPage();
    }
  },
  created(){
    this.initPage();
  },
  mounted(){
      this.pageResize();
      $(window).resize(this.pageResize);
  }
}
</script>

<style scoped>
/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}
.list-year-data {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
.scroll-table-body {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
</style>
