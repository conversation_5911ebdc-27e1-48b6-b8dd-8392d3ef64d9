<template>
    <div class="years-result-summary-container">
        <div class="row">
            <div class="col-xs-12 text-left">
                <h4 class="col-xs-7">
                    {{ title }}
                </h4>
                <div class="col-xs-12 text-right"
                    style="padding-bottom: 10px;line-height: 38px;display: flex;align-items: center;justify-content: right;">
                    <div style="display: flex;align-items: center;margin-right: 60px;">
                        <el-input size="mini" placeholder="请输入姓名" style="width:240px;margin-right:10px;"
                            v-model="input1">
                            <template slot="prepend">姓名搜索</template>
                        </el-input>
                        <button class="btn btn-primary btn-xs" style="height: 100%;" @click="_getLeaderAggregateQuery">
                            查询
                        </button>
                    </div>

                    <button class="btn btn-primary btn-xs" style="margin-right: 10px;" @click="sheetIt">
                        导出Excel
                    </button>
                    <button class="btn btn-primary btn-xs" style="margin-right: 10px;" @click="showDescrAction">
                        说明
                    </button>
                    <!-- push(`/${$route.params.year}/${$route.params.month}/menu/1`) -->
                    <button class="btn btn-primary btn-xs" @click="back">返回上级</button>
                </div>
            </div>
        </div>

        <div class="col-xs-12">
            <el-table id="table" :span-method="arraySpanMethod" :data="tableData" border
                :cell-style="{ 'text-align': 'center', 'padding': '4px 0' }" style="width:max-content"
                :max-height="body_height - 140 + 'px'">
                <el-table-column fixed prop="orderNumber" label="序号" width="55">
                </el-table-column>
                <el-table-column fixed width="733" label="评定对象">
                    <el-table-column prop="companyName" label="公司" width="68">
                    </el-table-column>
                    <el-table-column prop="userName" label="姓名" width="65">
                    </el-table-column>
                    <el-table-column prop="gender" label="性别" width="55">
                    </el-table-column>
                    <el-table-column prop="hometown" label="籍贯" width="55">
                    </el-table-column>
                    <el-table-column prop="degree" label="学历" width="55">
                    </el-table-column>
                    <el-table-column prop="graduateYear" label="届别" width="55">
                    </el-table-column>
                    <el-table-column prop="ablityGrade1" label="岗位级别" width="45">
                    </el-table-column>
                    <el-table-column prop="deptName" label="部门" width="105">
                    </el-table-column>
                    <el-table-column prop="jobName" label="职务" width="140">
                    </el-table-column>
                    <el-table-column prop="ablityGrade" label="职等" width="90">
                    </el-table-column>
                </el-table-column>
                <el-table-column fixed prop="set2017" label="2017-2018年度" width="78">
                </el-table-column>

                <el-table-column v-for="(item, index) in years" :key="index" :label="item">
                    <el-table-column prop="province" label="半年度" width="52">
                        <template slot-scope="scope">
                            {{ scope.row.scoringResults ? scope.row.scoringResults[index * 2] : '' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="province" label="年度" width="52">
                        <template slot-scope="scope">
                            {{ scope.row.scoringResults ? scope.row.scoringResults[index * 2 + 1] : '' }}
                        </template>
                    </el-table-column>
                </el-table-column>
                <el-table-column fixed="right" prop="resignationType" label="离职类型" width="65">
                </el-table-column>
                <el-table-column fixed="right" prop="resignationTime" label="离职时间" width="85">
                </el-table-column>

                <el-table-column fixed="right" prop="remark" label="备注" width="100">
                </el-table-column>
                <!-- <el-table-column fixed="right" label="操作" width="100">
                    <template slot-scope="scope">
                        <el-button @click="handleClick(scope.row)" type="text" size="small">查看</el-button>
                        <el-button type="text" size="small">编辑</el-button>
                    </template>
</el-table-column> -->
            </el-table>

        </div>

        <div style="display: none">
            <div class="showText" style="padding:10px 20px 0 20px;margin:0 auto;">
                评定年度的定义说明<br>
                ①2017-2018年度：本系统是从2017届开始使用，也就是2018年7月进行第一次（2017-2018年度）评定，因此2017-2018只有年度，没有半年度<br>
                ②半年度的定义：每年12月评定为半年度，如：2020-2021半年度，指的是2020年12月<br>
                ③年度的定义：每年6月评定为年度，如：2020-2021年度，指的是2021年6月
            </div>
        </div>
    </div>
</template>

<script>
import { getAggregateQuery, getUserInfo, updateFeedback, sentFeedbackToOA, isAllowSentOa } from '@/api'
import TableDataUtil from '@/utils/tableUtils'

export default {
    data() {
        return {
            title: '',
            tableData: [],
            body_height: 0,
            yearData: [],
            userInfo: {},
            years: [],
            year: "",
            month: "",
            companyGroupId: "",
            positionGradeId: "",
            deptArea: Utils.getQueryParams('deptArea'),
            input1: '',
            mergeField: ['companyName'],
            keyField: ['companyName']
        }
    },
    methods: {
        sheetIt() {
            this.exportExcelOne.exportExcel(
                `${this.title}.xlsx`,
                '#table'
            )
        },
        back() {
            this.input1 = ''
            this._getLeaderAggregateQuery(true)
        },
        getCompanyName() {
            let companyName = Utils.getCompanyNameByGroupId(this.companyGroupId);
            if (this.positionGradeId == "0006" || this.positionGradeId == "0007" || this.positionGradeId == "0032" || this.positionGradeId == "0033") {
                companyName = "全集团";
            }
            return companyName;
        },
        getMonthScore(data, userData) {
            let result = this.yearData.find(item => {
                if (item.fd_month == data.month && item.fd_year == data.year && item.user_id == userData.userId) {
                    return item;
                }
            });
            return (result && ((result.fd_year + '-' + result.fd_month) !== this.year + '-' + this.fd_month) || userData.status == 30) ? (result ? result.last_result : '') : '';
        },

        _getLeaderAggregateQuery(type) {
            const loading = this.$loading({
                lock: true,
                text: 'Loading',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            });
            /*先判断有权限才可以查询(modify by huangdh@2019-05-24)*/
            // if (this.$store.state.doubleCol.arrAuths.point_ablity_findYearReport) {
            getAggregateQuery({ companyGroupId: this.companyGroupId, name: this.input1 }).then(res => {
                if (type === true) {
                    setTimeout(() => {
                        this.$router.replace(`/${this.$route.params.year}/${this.$route.params.month}/menu/2`)
                    })
                    // this.$router.back()
                    return
                }
                if (!res.success) return;
                this.tableData = res.data || []
                this.tableData.map(item => { if (item.scoringResults) item.set2017 = item.scoringResults.splice(0, 1)[0] })
                this.mergeData = TableDataUtil.dataConvertToMerge(this.tableData, this.mergeField, this.keyField)
                setTimeout(() => {
                    let rows = document.getElementsByClassName('el-table__row')
                    let len = this.tableData.length
                    for (let i = 0; i < len; i++) {
                        if (!this.tableData[i].fdId) {
                            if (rows[i].cells) {
                                rows[i].cells[0].colSpan = rows[i].cells.length
                                rows[i].cells[0].style.background = '#eee'
                                rows[i].cells[0].children[0].innerText = this.tableData[i].type
                                rows[i].cells[0].children[0].style = 'color:#000;font-weight:bold;text-align:left;padding-left:15px !important'

                                rows[i + len].cells[0].colSpan = rows[i].cells.length
                                rows[i + len].cells[0].style.background = '#eee'
                                rows[i + len].cells[0].children[0].innerText = this.tableData[i].type
                                rows[i + len].cells[0].children[0].style = 'color:#000;font-weight:bold;text-align:left;padding-left:15px !important'

                                rows[i + len * 2].cells[0].colSpan = rows[i].cells.length
                                rows[i + len * 2].cells[0].style.background = '#eee'
                                rows[i + len * 2].cells[0].children[0].innerText = this.tableData[i].type
                                rows[i + len * 2].cells[0].children[0].style = 'color:#000;font-weight:bold;text-align:left;padding-left:15px !important'
                            }
                        }
                    }
                    let col = document.getElementsByClassName('col-xs-12')
                    let table = document.getElementsByClassName('el-table__body')[0]
                    col[0].style = 'max-width:' + table.scrollWidth + 'px'
                    loading.close();
                });
            }).catch(err => {
                loading.close();
                layer.msg(`${err.msg}`, { icon: 2, shade: 0.3, shadeClose: true });
            })
            // }
            // else {
            //     layer.msg(`对不起，您没有权限查询本界面.`, { icon: 2, shade: 0.3, shadeClose: true });
            // }
        },
        arraySpanMethod({ row, column, rowIndex, columnIndex }) {
            if (this.mergeField.indexOf(column.property) > -1) {
                return this.mergeData[rowIndex][column.property + 'Span']
            }
        },
        _getUserInfo(id) {
            getUserInfo({ userId: id }).then(res => {
                if (!res.success) return;
                this.userInfo = res.data || {}
            }).catch(err => {
                layer.msg(`${err.msg}`, { icon: 2, shade: 0.3, shadeClose: true });
            })
        },

        initPage() {
            this.pageResize();
            const { companyGroupId, year, month, positionGradeId } = this.$route.params;
            this.companyGroupId = companyGroupId;
            this.year = year;
            this.month = month;
            this.positionGradeId = positionGradeId;
            // this.deptArea = "";
            this._getLeaderAggregateQuery();
            let years = Number(year) - 2018 + 1
            if (years >= 0) {
                for (let i = 0; i < years; i++)
                    this.years.push(2018 + i + '-' + (2018 + i + 1))
            }
            this.title = year + (month == `6` ? `半` : ``) + '年度' + this.getCompanyName() + this.$route.name
        },
        pageResize() {
            this.body_height = $(window).height();
        },
        showDescrAction() {
            Utils.layerBox.layerDialogOpen({
                title: "说明",
                area: ["700px", "200px"],
                btn: [],
                content: `${$('.showText')[0].outerHTML}`
            });
        },
    },
    watch: {
        "$route": function () {
            this.initPage();
        }
    },
    mounted() {
        this.initPage();
        $(window).resize(this.pageResize);
    }
}
</script>

<style scoped>
.col-xs-12 {
    /* padding: 0 !important; */
    /* max-width: 100%; */
}

.scroll-table {
    /* width: 620px; */
}

.el-table /deep/.el-table--border .el-table__cell {
    border-bottom: 1px solid #EBEEF5 !important;
}

.el-table /deep/.el-table .cell {
    padding: 0 !important;
    font-size: 13px;
    line-height: 1.5;
}
</style>