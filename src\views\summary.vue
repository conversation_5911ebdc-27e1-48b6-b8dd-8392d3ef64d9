<template>
  <div class="assessor-detail-container">
    <div class="row">
      <div class="col-xs-12 text-left" style="width: 1270px">
        <h4 class="col-xs-6">{{ name + "【" + $route.name + "】" }}</h4>
        <div class="col-xs-6 text-right" style="line-height: 40px">
          <!-- <button type="button" class="btn btn-primary btn-xs" @click="sheetIt">导出Excel</button>
            <button class="btn btn-primary btn-xs" @click="_sendOa">提交OA</button> -->
          <button class="btn btn-primary btn-xs" @click="$router.go(-1)">
            返回上级
          </button>
        </div>
      </div>
    </div>

    <div class="col-xs-12">
      <el-table
        id="table"
        :data="tableData"
        border
        :cell-style="{ 'text-align': 'center', padding: '4px 0' }"
        style="width: max-content"
        :max-height="body_height - 140 + 'px'"
      >
        <el-table-column type="index" label="序号" width="45"></el-table-column>
        <el-table-column
          prop="project"
          label="项目名称"
          width="200"
        ></el-table-column>
        <el-table-column
          prop="role"
          label="个人扮演角色"
          width="135"
        ></el-table-column>
        <el-table-column
          prop="effect"
          label="个人主要成效"
          width="400"
        ></el-table-column>
        <el-table-column label="完成时间">
          <el-table-column
            prop="startDate"
            label="开始时间"
            width="100"
          ></el-table-column>
          <el-table-column
            prop="endDate"
            label="完成时间"
            width="100"
          ></el-table-column>
          <el-table-column
            prop="useDays"
            label="耗时(天)"
            width="80"
          ></el-table-column>
        </el-table-column>
        <el-table-column
          prop="remark"
          label="备注"
          width="180"
        ></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { userSummaryDetailList, updateNotCommitSave, sendOa } from "@/api";
export default {
  data() {
    return {
      isLoading: true,
      body_height: 0,
      tableData: [],
      companyGroupId: "",
      judgeTypes: ["综合分", "成功要素"],
      year: "",
      month: "",
      name: "",
    };
  },
  computed: {
    disabledButton() {
      /*提交OA*/
      if (this.isLoading) {
        return true;
      } else if (this.tableData == null || this.tableData.length == 0) {
        return true;
      } else if (this.tableData && this.tableData.length != 0) {
        //return this.tableData[0].confirmLeaderId ? true : false;
        return this.tableData[0].confirmLeaderStatus > 10 ? true : false;
      }
      return false;
    },
  },
  methods: {
    // sheetIt() {
    //     this.exportExcelOne.exportExcel(`【${this.getCompanyName()}】管培生【工作总结】表.xlsx`, '#table')
    // },
    getSchoolLevel(level) {
      return Utils.number2ChNum(level) + "级";
    },
    getCompanyName() {
      return Utils.getCompanyNameByGroupId(this.companyGroupId);
    },

    _userSummaryDetailList(data) {
      /*先判断有权限才可以查询(modify by huangdh@2019-05-24)*/
      // if (this.$store.state.doubleCol.arrAuths.point_ablity_userSummaryDetailList) {
      userSummaryDetailList(data).then((res) => {
        this.isLoading = false;
        this.tableData = res.data.highlights || [];
        this.tableData.forEach((item) => {
          if (item.startDate) {
            item.startDate = item.startDate.slice(0, 10);
          }
          if (item.endDate) {
            item.endDate = item.endDate.slice(0, 10);
          }
        });
      });
      // }
      // else {
      //   layer.msg(`对不起，您没有权限查询本界面.`, { icon: 2, shade: 0.3, shadeClose: true });
      // }
    },
    showDescrDialog() {
      Utils.layerBox.layerDialogOpen({
        title: "<b>评定人说明：</b>",
        area: ["850px", "540px"],
        btn: ["关闭"],
        content: `<div style="padding:10px 20px;margin:0 auto;">
            <p style="padding: 0 20px;">
                <strong>1、评定人资格：</strong><br>
                （1）关联业务领导：指的是与评定对象有关联业务接触的部门领导。<br>
                （2）直接领导：指的是评定对象的部门主管，直接汇报上级。<br>
                （3）间接领导：指的是评定对象的体系领导。<br>
                （4）人资领导：指的是了解人资对象的人资体系领导。评定人数少于5人时，则不选。<br>
                <strong>2、评定人人数及权重设置</strong><br>
                （1）评定人人数：2-5人，不允许1个人进行评定。<br>
                （2）评定人数为5人的，间接领导权重为30%，直接领导权重为20%，人资领导权重为20%，业务关联领导均为15%；<br>
                （3）评定人数为4人的，间接领导权重为40%，直接领导权重为30%，业务关联领导均为15%；<br>
                （4）评定人数为3人的<br>
                ①有间接领导的：间接领导权重为50%，直接领导和业务关联领导均为25%；<br>
                ②没有间接领导的：直接领导权重为50%，2个关联业务领导各占25%； <br>
                （5）评定人数为2人的 <br>
                ①有间接领导的：间接领导权重为60%，业务关联领导或直接领导为40%； <br>
                ②没有间接领导的：直接领导权重为60%，业务关联领导为40%。 <br>
                <strong>3、评定方式确定</strong><br>
                （1）成功要素：指的是运用成功要素（九大要素）对评定对象进行逐项评定，原则上熟悉成功要素的评定人采用“成功要素”进行评定。<br>
                （2）综合分：指的是根据日常工作中评定对象的工作表现，直接用综合分进行评定。评定人属于首次接触成功要素，尚不能完全掌握其核心要点，采用“综合分”进行评定。<br>
                        
          </p></div>`,
      });
    },
    initPage() {
      this.pageResize();
      const { fdId, name } = this.$route.query;
      const { companyGroupId, year, month } = this.$route.params;
      this.companyGroupId = companyGroupId;
      this.year = year;
      this.month = month;
      this.name = name;
      this._userSummaryDetailList({
        // fdId: fdId,
        // userId: this.$store.state.user.id,
        year: year,
        month: month,
        userName:name
      });
    },
    pageResize() {
      this.body_height = $(window).height();
    },
  },
  watch: {
    $route: function () {
      this.initPage();
    },
  },
  mounted() {
    this.initPage();
    $(window).resize(this.pageResize);
  },
};
</script>

<style scoped>
.el-table /deep/.el-table--border .el-table__cell {
  border-bottom: 1px solid #ebeef5 !important;
}

.el-table /deep/.el-table .cell {
  padding: 0 !important;
  font-size: 13px;
}
</style>
