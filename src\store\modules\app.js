import Cookies from 'js-cookie'

const app = {
  state: {
    sidebar: {
      rightShow: false,
      width: '360px',
      opened: !+Cookies.get('sidebarStatus'),
      withoutAnimation: false
    },
  },
  mutations: {
    // TOGGLE_SIDEBAR: state => {
    //   if (state.sidebar.opened) {
    //     Cookies.set('sidebarStatus', 1)
    //   } else {
    //     Cookies.set('sidebarStatus', 0)
    //   }
    //   state.sidebar.opened = !state.sidebar.opened
    //   state.sidebar.withoutAnimation = false
    // },
    // SHOW_SIDEBAR: state => {
    //   state.sidebar.rightShow = true
    // },
    // HIDE_SIDEBAR: state => {
    //   state.sidebar.rightShow = false
    // },
    
  },
  actions: {
    // toggleSideBar({ commit }) {
    //   commit('TOGGLE_SIDEBAR')
    // },
    // showSideBar({ commit }) {
    //   commit('SHOW_SIDEBAR')
    // },
    // hideSideBar({ commit }) {
    //   commit('HIDE_SIDEBAR')
    // },
  }
}

export default app
