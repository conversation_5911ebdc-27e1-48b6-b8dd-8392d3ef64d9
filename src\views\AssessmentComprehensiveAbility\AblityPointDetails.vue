<template>
  <div class="grade-evaluation-dialog-container">
      <div class="row">
        <div class="col-xs-12">
          <table class="table table-bordered">
            <thead>
              <tr>
                <th colspan="2" >评定对象：</th>
                <th style="text-align:left;">{{this.params.userName}}</th>
              </tr>
              <tr>
                <th style="width:50px;">序号</th>
                <th style="width:80px;">评定人</th>
                <th >评定事例依据</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(data,$index) in tableData" :key="$index">
                <td>{{$index+1}}</td>
                <td>{{data.leaderName}}</td>
                <td style="text-align:left;">{{data.bakRemark}}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
  </div>
</template>

<script>
import { findAblityPointListByAblityId } from "@/api";
export default {
  props: {
      params:{
          type:Object,
          required:true
      }
  },
  data() {
    return {
      tableData: []
    };
  },
  methods: {
    _findAblityPointListByAblityId() {
      findAblityPointListByAblityId({
        ablityId:this.params.ablityId,
      }).then(res => {
        if (!res.success) return;
        this.tableData = res.data ? res.data : [];
      }).catch(err=>{
        layer.msg(`${err.msg}`, {icon: 2,shade:0.3,shadeClose:true});
      });
    }
  },
  created() {
    this._findAblityPointListByAblityId();
  }
};
</script>

<style lang="scss">
.grade-evaluation-dialog-container {
  padding: 20px 0;
  .row {
    margin: 0;
    padding-left: 0;
    padding-right: 0;
    box-sizing: border-box;
  }
  .input-sm {
    height: 28px;
    padding: 5px 10px 5px 5px;
  }
  div.scroll-table {
    width: 100%;
    height: 320px;
    display: inline-block;
    float: left;
    background: #f4fafb;
    table.scroll-table-header {
      width: 100%;
      font-size: 13px;
      // display:block;
      // padding-right:17px;
    }
    table.scroll-table-body {
    }
  }
  table thead {
    tr {
      th {
        text-align: center;
        border-bottom: none;
        padding: 2px 5px;
        vertical-align: middle;
        background: #b7d8dc;
        height: 32px;
        font-size: 12px;
        color: #333;
        & > * {
          background: #b7d8dc !important;
          outline: none;
        }
      }
    }
    &.hide-thead-th > tr > th {
      height: 0;
      padding: 0;
      background: transparent;
      border: none;
    }
  }
  .table tbody tr {
    td {
      padding: 2px 5px;
      vertical-align: middle;
      height: 27px;
      font-size: 12px;
      text-align: center;
      .btn {
        padding-top: 0;
        padding-bottom: 0;
      }
    }
    &:nth-child(odd) {
      background: #fff;
    }
    &:nth-child(even) {
      background: #f9f9f9;
    }
  }
}
</style>
