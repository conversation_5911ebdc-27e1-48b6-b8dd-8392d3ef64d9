<template>
  <div class="benchmarking-personnel-container">
    <div
      class="row"
      style="text-align: center; font-size: 18px; font-weight: bold"
    >
      审批提示
    </div>
    <div class="row mydiv" style="margin: 5px 50px 5px 30px">
      一、各公司评定情况汇总
      <div style="float: right">
        <button class="btn btn-primary btn-xs" @click="_studentApproveSave"  :disabled="disabledButton">
          保存说明
        </button>
        <button
          class="btn btn-primary btn-xs"
          @click="
            $router.push(
              `/${$route.params.year}/${$route.params.month}/trainee/evaluation/meetingResult`
            )
          "
        >
          返回上级
        </button>
      </div>
    </div>

    <div class="row">
      <div class="col-xs-12">
        <div class="list-table">
          <div class="scroll-table" style="height: 100%; overflow-x: hidden">
            <table
              class="scroll-table-header table-bordered"
              :style="{ width: `calc(100% - ${1.4 * 12 + 'px'})` }"
            >
              <colgroup>
                <col style="width: 5%" />
                <col style="width: 17%" />
                <col style="width: 8%" />
                <col style="width: 7%" />
                <col style="width: 7%" />
                <col style="width: 7%" />
                <col style="width: 7%" />
                <col style="width: 7%" />
                <col style="width: 7%" />
                <col style="width: 7%" />
                <col style="width: 7%" />
                <col style="width: 7%" />
                <col style="width: 7%" />
              </colgroup>
              <thead>
                <tr>
                  <th rowspan="2">序号</th>
                  <th rowspan="2">公司</th>
                  <th rowspan="4">评定人数</th>
                  <th colspan="2">优<br />考虑提前晋升</th>
                  <th colspan="2">A</th>
                  <th colspan="2">B</th>
                  <th colspan="2">C</th>
                  <th colspan="2">D<br />原则上劝退</th>
                </tr>
                <tr>
                  <th>人数</th>
                  <th>占比</th>
                  <th>人数</th>
                  <th>占比</th>
                  <th>人数</th>
                  <th>占比</th>
                  <th>人数</th>
                  <th>占比</th>
                  <th>人数</th>
                  <th>占比</th>
                </tr>
              </thead>
            </table>
            <div
              class="scroll-table-body"
              :style="{
                overflow: 'auto',
                maxHeight: body_height - 160 + 'px',
                'overflow-y': 'scroll',
                'overflow-x': 'hidden',
              }"
            >
              <table
                style="
                  margin: 0px 10px 0 0;
                  position: relative;
                  width: 100%;
                  font-size: 13px;
                  float: left;
                  border: none;
                "
                class="table table-bordered table-hover table-striped"
              >
                <colgroup>
                  <col style="width: 5%" />
                  <col style="width: 17%" />
                  <col style="width: 8%" />
                  <col style="width: 7%" />
                  <col style="width: 7%" />
                  <col style="width: 7%" />
                  <col style="width: 7%" />
                  <col style="width: 7%" />
                  <col style="width: 7%" />
                  <col style="width: 7%" />
                  <col style="width: 7%" />
                  <col style="width: 7%" />
                  <col style="width: 7%" />
                </colgroup>
                <tbody>
                  <tr v-for="(data, $index) in tableData" :key="$index">
                    <td>{{ $index + 1 }}</td>
                    <td>{{ data.company_name }}</td>
                    <td>{{ data.total }}</td>
                    <td>{{ data.value_top }}</td>
                    <td>{{ data.rate_top.toFixed(0)+"%" }}</td>
                    <td>{{ data.value_A }}</td>
                    <td>{{ data.rate_A.toFixed(0)+"%" }}</td>
                    <td>{{ data.value_B }}</td>
                    <td>{{ data.rate_B.toFixed(0)+"%" }}</td>
                    <td>{{ data.value_C }}</td>
                    <td>{{ data.rate_C.toFixed(0)+"%" }}</td>
                    <td>{{ data.value_D }}</td>
                    <td>{{ data.rate_D.toFixed(0)+"%" }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="row mydiv">
      二、劝退情况说明
      <div>
        <textarea
          id="text_gohomeDescr"
          type="text"
          style="width: 97%; height: 100px;font-weight:normal;"
          v-model="formData.gohomeDescr"
        ></textarea>
      </div>
    </div>
    <div class="row mydiv">
      三、特殊情况说明
      <div>
        <textarea
          id="text_specDescr"
          type="text"
          style="width: 97%; height: 100px;font-weight:normal;"
          v-model="formData.specDescr"
        ></textarea>
      </div>
    </div>
  </div>
</template>


<script>
import { findAblityLevel, findOneByYearMonth, studentApproveSave } from "@/api";

export default {
  data() {
    return {
      isLoading: true,
      body_height: 0,
      tableData: [],
      year: "",
      month: "",
      formData: {
        fdId: "",
        fdYear: "",
        fdMonth: "",
        gohomeDescr: "",
        specDescr: ""
      }

    };
  },
  computed:{
    disabledButton(){
        if(this.$route.query.flag=="1"){
            return false;
        }
        return false;
    }
  },
  methods: {
    _findAblityLevel() {
      this.isLoading = true
      findAblityLevel({ fdYear: this.$route.params.year, fdMonth: this.$route.params.month }).then(res => {
        this.isLoading = false
        let data = res.data || {}
        this.tableData = data.list || [];
        this.formData = data.studentApprove || {}
      });
    },

    _studentApproveSave() {
      if (this.isLoading) {
        return false
      }
      layer.confirm(
        `确认调整保存吗?`,
        { icon: 3, title: `提示` },
        index => {
          this.isLoading = true
          this.formData.fdYear=this.$route.params.year;
          this.formData.fdMonth=this.$route.params.month;
          studentApproveSave(this.formData).then(res => {
            this.isLoading = false
            let result = res.success;
            layer.msg(`保存录入${result ? '成功' : '失败'}`, { icon: result ? 1 : 2, shade: 0.3, shadeClose: true });
            layer.close(index)
            this.$router.back()
            // this.params.closeComponent && this.params.closeComponent();
          });
        }
      );
    },
    initPage() {
      this.body_height = $(window).height();
      const { year, month } = this.$route.params;
      this.year = year;
      this.month = month;
      this._findAblityLevel();
    }
  },
  mounted() {
    this._findAblityLevel();
    // this._findOneByYearMonth();
    //console.log("flag111:"+this.$route.query.flag);
  }
};
</script>

<style scoped lang="scss">
.mydiv {
  text-align: left;
  margin-left: 15px !important;
  margin-right: 50px;
  margin-top: 5px;
  margin-bottom: 5px;
  font-weight: bold;
  margin-top: 10px !important;
  & > div {
    padding-top: 5px;
    textarea {
      padding: 8px;
    }
  }
}
</style>
