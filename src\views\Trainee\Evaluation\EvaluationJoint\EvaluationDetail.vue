<template>
  <div class="evaluation-detail-container">
      <div class="row">
        <div class="col-xs-12 text-left">
          <h4 class="col-xs-6">{{pageTitle}}管培生综合能力【评定合议】表</h4>
          <div class="col-xs-6 text-right" style="line-height:40px;">
            <button class="btn btn-primary btn-xs" @click="showDialog">职务规划表查询</button>
            <button class="btn btn-primary btn-xs" @click="_updateStudentRisePositionSave" :disabled="disabledButton">保存录入</button>
            <!-- <button class="btn btn-primary btn-xs" @click="_updateStudentRisePosition" :disabled="disabledButton">合议完成提交</button> -->
            <button class="btn btn-primary btn-xs" @click="$router.push(`/${$route.params.year}/${$route.params.month}/menu/2`)">返回目录</button>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-xs-12">
          <div class="list-table">
              <div class="scroll-table" style="height:100%;overflow-x:hidden;">
                  <table class="scroll-table-header table-bordered" :style="{'width':'calc(100% - '+12*1.4+'px)'}">
                      <colgroup>
                        <col width="40"/>
                        <col width="50"/>
                        <col width="75"/>
                        <col width="60"/>
                        <col width="80"/>
                        <col width="60"/>
                        <col width="55"/>
                        <col width="50"/>
                        <col width="50"/>
                        <col width="50"/>
                        <col width="50"/>
                        <col width="60"/>
                        <col width="40"/>
                        <col width="70"/>
                        <col width="60"/>
                        <col width="70"/>
                        <col width="60"/>
                      </colgroup>
                      <thead>
                        <tr>
                          <th rowspan="2">序号</th>
                          <th colspan="6">评定对象</th>
                          <th colspan="5">评定得分明细</th>
                          <th colspan="5">评定合议结果</th>
                        </tr>
                        <tr>
                          <th>姓名</th>
                          <th>学历</th>
                          <th>部门</th>
                          <th>岗位</th>
                          <th>职务等级</th>
                          <th>年份</th>
                          <th>评定人</th>
                          <th>评定方式</th>
                          <th>评定得分</th>
                          <th>权重</th>
                          <th>权重得分</th>
                          <th>排名</th>
                          <th>能力等级</th>
                          <th>本次评定类型</th>
                          <th>是否晋升</th>
                          <th>晋升职务等级</th>
                        </tr>
                      </thead>
                  </table>
                  <div class="scroll-table-body" :style="{overflow:'auto',maxHeight:body_height - 160 + 'px','overflow-y':'scroll','overflow-x':'hidden'}">
                      <table style="margin:0px 10px 0 0;position: relative;width:100%;font-size:13px;float:left;border:none;" class="table table-bordered table-hover table-striped">
                          <colgroup>
                            <col width="40"/>
                            <col width="50"/>
                            <col width="75"/>
                            <col width="60"/>
                            <col width="80"/>
                            <col width="60"/>
                            <col width="55"/>
                            <col width="50"/>
                            <col width="50"/>
                            <col width="50"/>
                            <col width="50"/>
                            <col width="60"/>
                            <col width="40"/>
                            <col width="70"/>
                            <col width="60"/>
                            <col width="70"/>
                            <col width="60"/>
                          </colgroup>
                          <tbody>
                              <tr v-for="(data,$index) in tableData" :key="$index">
                                <td v-if="!!data.user_row_count" :rowspan="data.user_row_count">{{data.row_index||''}}</td>
                                <td v-if="!!data.user_row_count" :rowspan="data.user_row_count">{{data.user_name||''}}</td>
                                <td v-if="!!data.user_row_count" :rowspan="data.user_row_count">{{data.degree||''}}</td>
                                <td v-if="!!data.user_row_count" :rowspan="data.user_row_count">{{data.dept_name||''}}</td>
                                <td v-if="!!data.user_row_count" :rowspan="data.user_row_count">{{data.job_name||''}}</td>
                                <td v-if="!!data.user_row_count" :rowspan="data.user_row_count">{{data.grade_name||''}}</td>
                                <td v-if="!!data.user_row_count" :rowspan="data.user_row_count">{{data.year_index||''}}</td>
                                
                                <td>{{data.rater_name||''}}</td>
                                <td>{{data.judge_type||''}}</td>
                                <td>{{data.point||''}}</td>
                                <td>{{data.weight_point?(data.weight_point*100+'%'):''}}</td>
                                <td v-if="!!data.user_row_count" :rowspan="data.user_row_count">{{data.avg_point||''}}</td>

                                <td v-if="!!data.user_row_count" :rowspan="data.user_row_count">{{data.row_index||''}}</td>
                                <td v-if="!!data.user_row_count&&!data.ablity_direct_leader_id" :rowspan="data.user_row_count">
                                  <select v-model="data.meeting_level" style="width:100%;height:24px;line-height:24px;">
                                      <option :value="null">请选择</option>
                                      <option value="A">A</option>
                                      <option value="B">B</option>
                                      <option value="C">C</option>
                                      <option value="D">D</option>
                                  </select>
                                </td>
                                <td v-if="!!data.user_row_count&&data.ablity_direct_leader_id" :rowspan="data.user_row_count">
                                  {{data.meeting_level||''}}
                                </td>
                                <td v-if="!!data.user_row_count" :rowspan="data.user_row_count">{{data.student_rise_type||''}}</td>
                                <td v-if="!!data.user_row_count&&!data.ablity_direct_leader_id" :rowspan="data.user_row_count">
                                  <select v-if="data.student_rise_type!='普通评定'" v-model="data.student_is_rise" style="width:100%;height:24px;line-height:24px;" @change="setGradeName(data)">
                                    <option :value="null">请选择</option>
                                    <option value="晋升">晋升</option>
                                  </select>
                                </td>
                                <td v-if="!!data.user_row_count&&data.ablity_direct_leader_id" :rowspan="data.user_row_count">
                                  {{data.student_is_rise||''}}
                                </td>
                                <td v-if="!!data.user_row_count&&!data.student_is_rise" :rowspan="data.user_row_count">{{data.student_grade_name||''}}</td>
                                <td v-if="!!data.user_row_count&&data.student_is_rise" :rowspan="data.user_row_count">{{data.rise_grade_name||''}}</td>
                            </tr>
                          </tbody>
                      </table>
                  </div>
              </div>
          </div>
        </div>
      </div>
      <!-- <div class="text-left row" style="margin-top:10px;">
        <p style="padding: 0 20px;">
          1、能力等级：分A、B、C、D四级。<br/>
          2、能力等级比例控制：A级：小于20%；B级：50%-70%；C级：30%；D级：不设比例，属于淘汰人员。<br/>
          3、A级人员必须是综合能力得分达到27分以上（9项要素平均每项要素达到3分以上）。<br/>
        </p>
      </div> -->
  </div>
</template>

<script>
import { findAblityStudentPointListBy,updateStudentRisePosition,updateStudentRisePositionSave } from '@/api'
import PlanningDetailDialog from './PlanningDetailDialog'
export default {
  data(){
    return {
      isLoading:true,
      tableData:[],
      companyGroupId:'',
      graduateYear:'',
      schoolLevel:'',
      body_height:0
    }
  },
  computed:{
    pageTitle(){
      return `${this.graduateYear}届${Utils.number2ChNum(this.schoolLevel)}级岗位`
    },
    disabledButton(){      
      if(this.isLoading){
        return true;
      }
      else if(this.tableData&&this.tableData.length!=0){
        return this.tableData[0].status !== 10 ? true : false;
      }
      return false
    }
  },
  methods:{
    getCompanyName(){
      return Utils.getCompanyNameByGroupId(this.companyGroupId);
    },
    setGradeName(data){
      if(data.student_is_rise){
        data.student_grade_name = data.rise_grade_name;
      }else{
        data.student_grade_name = '';
      }
    },
    _updateStudentRisePositionSave(){
      if(this.disabledButton){
        return;
      }
      this.isLoading = true;
      let list = [];
      this.tableData.forEach(({fd_id,meeting_level,student_rise_type,student_is_rise,student_grade_name,user_row_count})=>{
        if(!!user_row_count){
          list.push({fdId:fd_id,meetingLevel:meeting_level,studentRiseType:student_rise_type,studentIsRise:student_is_rise,studentGradeName:student_grade_name});
        }
      })
      updateStudentRisePositionSave(list).then(res=>{
        let result = res.success;
        layer.msg(`合议保存${result?'成功':'失败'}！${res.msg||''}`, {icon: result ? 1 : 2,shade:0.3,shadeClose:true});
        this._findAblityStudentPointListBy();
      }).catch(err=>{
        layer.msg('合议提交失败！'+(err.msg||''), {icon: 2,shade:0.3,shadeClose:true});
      })
    },
    // _updateStudentRisePosition(){
    //   if(this.disabledButton){
    //     return;
    //   }
    //   this.isLoading = true;
    //   let list = this.tableData.map(({fd_id,meeting_level,student_rise_type,student_is_rise,student_grade_name})=>{
    //     return {fdId:fd_id,meetingLevel:meeting_level,studentRiseType:student_rise_type,studentIsRise:student_is_rise,studentGradeName:student_grade_name}
    //   })
    //   updateStudentRisePosition(list).then(res=>{
    //     let result = res.success;
    //     layer.msg(`合议提交${result?'成功':'失败'}！${res.msg||''}`, {icon: result ? 1 : 2,shade:0.3,shadeClose:true});
    //     this._findAblityStudentPointListBy();
    //   }).catch(err=>{
    //     layer.msg('合议提交失败！'+(err.msg||''), {icon: 2,shade:0.3,shadeClose:true});
    //   })
    // },
    _findAblityStudentPointListBy(){
        findAblityStudentPointListBy({year:this.$route.params.year,month:this.$route.params.month,companyGroupId:this.companyGroupId,graduateYear:this.graduateYear,schoolLevel:this.schoolLevel}).then(res=>{
            this.isLoading = false;
            this.tableData = res.data || [];
            this.tableData = this.tableData.map((item)=>{
              item.meeting_level = item.meeting_level || null;
              item.student_is_rise = item.student_is_rise || null;
              return item;
            })
        })
    },
    showDialog() {
      let graduateYear = this.graduateYear, schoolLevel = this.schoolLevel,type='';
      let layerDialog = Utils.layerBox.layerDialogOpen({
        title: `【${graduateYear}届${Utils.number2ChNum(schoolLevel)}级岗位】管培生职务规划表`,
        btn: [],
        maxmin: false, //开启最大化最小化按钮
        area: ["650px", '330px']
      });
      this.dialogComponent = Utils.layerBox.layerLoadVueComponent({
        layer: layerDialog,
        component: PlanningDetailDialog,
        methods: {
          doConfirm: (params, action) => {},
          doClose: () => {}
        },
        //传递本组件的参数给对话框组件，对话框组件通过props属性params获取值,例如下面这个val属性取值：this.params.val
        props: {
          graduateYear, schoolLevel,type
        }
      });
    },
    initPage(){
      this.body_height = $(window).height();
      const {companyGroupId,graduateYear,schoolLevel} = this.$route.params;
      this.companyGroupId = companyGroupId;
      this.graduateYear = graduateYear;
      this.schoolLevel = schoolLevel;
      this._findAblityStudentPointListBy();
    }
  },
  watch:{
    "$route":function(){
      this.initPage();
    }
  },
  mounted(){
    $(window).resize(()=>{
      this.body_height = $(window).height();
    })
    this.initPage();
  }
}
</script>

<style>

</style>
