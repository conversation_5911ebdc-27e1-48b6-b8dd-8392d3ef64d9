<template>
  <div class="people-count-container">
      <div class="row">
        <div class="col-xs-12 text-left">
          <h4 class="col-xs-7">【{{getCompanyName()}}】各部门人数统计</h4>
          <div class="col-xs-5 text-right" style="padding: 0;line-height: 38px;">
            <button class="btn btn-primary btn-xs" @click="$router.push(`/${$route.params.year}/${$route.params.month}/menu/1`)">返回目录</button>
          </div>
        </div>
        <div class="col-xs-6"></div>
      </div>
      <div class="row">
        <div class="col-xs-12">
          <div class="list-table">
            <div class="scroll-table" style="height:100%;overflow-x:hidden;">
                <table class="scroll-table-header table-bordered" :style="{width:`calc(100% - ${1.4*12 +'px'})`}">
                  <colgroup>
                    <col style="width:45px"/>
                    <col style="width:100px"/>
                    <col style="width:100px"/>
                    <col v-for="i in tableHead.length" :key="i" style="width:70px" />
                    <col style="width:70px"/>
                    <col style="width:70px"/>
                  </colgroup>
                  <thead>
                    <tr>
                      <th rowspan="2">序号</th>
                      <th rowspan="2">部门</th>
                      <th rowspan="2">部门人数统计</th>
                      <th :colspan="tableHead.length" >职等人数分布</th>
                      <th colspan="2">男女分布</th>
                    </tr>
                    <tr>
                      <th v-for="(head,$index) in tableHead" :key="$index">{{head.display_name}}</th>
                      <th>男</th>
                      <th>女</th>
                    </tr>
                  </thead>
                </table>
                <div class="scroll-table-body" :style="{overflow:'auto',maxHeight:body_height - 190 + 'px','overflow-y':'scroll','overflow-x':'hidden'}">
                    <table style="margin:0px 10px 0 0;position: relative;width:100%;font-size:13px;float:left;border:none;" class="table table-bordered table-hover table-striped">
                        <colgroup>
                          <col style="width:45px"/>
                          <col style="width:100px"/>
                          <col style="width:100px"/>
                          <col v-for="i in tableHead.length" :key="i" style="width:70px" />
                          <col style="width:70px"/>
                          <col style="width:70px"/>
                        </colgroup>
                        <tbody>
                          <tr v-for="(data,$index) in tableData" :key="$index">
                            <td>{{$index+1}}</td>
                            <td>{{data.dept_name}}</td>
                            <td>{{data.total}}</td>
                            <td v-for="(prop,prop_index) in tableProps" :key="prop_index">{{data['grade_'+prop]}}</td>
                            <td>{{data.man}}</td>
                            <td>{{data.feman}}</td>
                          </tr>
                      </tbody>
                    </table>
                </div>
                <table :style="{width:`calc(100% - ${1.4*12 +'px'})`}" style="margin:0px 10px 0 0;position: relative;width:100%;font-size:13px;float:left;border:none;" class="table table-bordered table-hover table-striped">
                  <colgroup>
                    <col style="width:45px"/>
                    <col style="width:100px"/>
                    <col style="width:100px"/>
                    <col v-for="i in tableHead.length" :key="i" style="width:70px" />
                    <col style="width:70px"/>
                    <col style="width:70px"/>
                  </colgroup>
                  <tbody>
                    <tr v-for="$index in 1" :key="$index" style="background:#f9fd17;">
                      <td>统计：</td>
                      <td>{{tableData.length}}</td>
                      <td>{{getTotalByProp('total')}}</td>
                      <td v-for="(prop,prop_index) in tableProps" :key="prop_index">{{getTotalByProp('grade_'+prop)}}</td>
                      <td>{{getTotalByProp('man')}}</td>
                      <td>{{getTotalByProp('feman')}}</td>
                    </tr>
                  </tbody>
                </table>
            </div>
          </div>
        </div>
      </div>
  </div>
</template>

<script>
import { findDeptCount } from '@/api'
export default {
  data(){
    return {
      body_height: 0,
      tableData:[],
      tableHead:[],
      tableProps:[],
      year:"",
      month:"",
      companyGroupId:"",
    }
  },
  methods:{
    getTotalByProp(propName){
      let count = 0;
      this.tableData.forEach(item=>{
        count +=parseInt(item[propName]);
      })
      return count;
    },
    getCompanyName(){
      return Utils.getCompanyNameByGroupId(this.companyGroupId);
    },
    _findDeptCount(){
      findDeptCount({companyGroupId:this.companyGroupId,year:this.year,month:this.month}).then(res=>{
        if(!res.success) return;
        let data = res.data || [];
        this.tableData = data&&data.length>1 ? data[1].deptCountList : [];
        this.tableHead = data&&data.length!=0 ? data[0].positionGradeHeaderList : [];
        this.tableProps = this.tableHead.map(item=>{
          return item.fd_id;
        })
      })
    },
    pageResize(){
      this.body_height = $(window).height();  
    }
  },
  watch:{
    "$route":function(){
      this.pageResize();
    }
  },
  created(){
    const {companyGroupId,year,month} = this.$route.params;
    this.companyGroupId = companyGroupId;
    this.year = year;
    this.month = month;
    this._findDeptCount();
  },
  mounted(){
      $(window).resize(this.pageResize);
      this.pageResize();
  }
}
</script>

<style>

</style>
