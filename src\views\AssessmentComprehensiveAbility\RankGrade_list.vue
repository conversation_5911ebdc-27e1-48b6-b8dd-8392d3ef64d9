<template>
  <div class="benchmarking-personnel-container">
      <div class="row">
        <div class="col-xs-12 text-left">
          <h4 class="col-xs-6">{{this.$route.params.year}}{{this.month==`6`?`半`:``}}年度{{getCompanyName()}}（{{getGradeName()}}）{{$route.name}}</h4>
          <div class="col-xs-6 text-right" style="line-height:40px;">
            <button type="button" class="btn btn-primary btn-xs" @click="showDescrDialog">说明</button>
            <button type="button" class="btn btn-primary btn-xs" :disabled="disabledButton" @click="_updateMeetingLevelToOA()"
                v-if="$store.state.doubleCol.arrAuths.point_ablity_updateMeetingLevelToOA">职级评定发起</button>
            <button class="btn btn-primary btn-xs" @click="$router.push(`/${$route.params.year}/12/menu/1`)">返回目录</button>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-xs-12">
          <div class="list-table">
            <div class="scroll-table" style="height:100%;overflow-x:hidden;">
                <table class="scroll-table-header table-bordered" :style="{width:`calc(100% - ${1.4*12 +'px'})`}">
                  <colgroup>
                    <col style="width:4%"/>
                    <col style="width:8%"/>
                    <col style="width:6%"/>
                    <col style="width:10%"/>
                    <col style="width:10%"/>
                    <col style="width:7%"/>
                    <col style="width:6%"/>
                    <col style="width:10%"/>
                    <col style="width:7%"/>
                    <col style="width:6%"/>
                    <col style="width:10%"/>
                    <col style="width:7%"/>
                    <col style="width:9%"/>
                  </colgroup>
                  <thead>
                    <tr>
                      <th rowspan="2">序号</th>
                      <th rowspan="2">职级评定<br>流程状态</th>
                      <th colspan="4">直接领导</th>
                      <th colspan="3">审核人</th>
                      <th colspan="3">审定人</th>
                      <th rowspan="2">职级评定表<br>查询</th>
                    </tr>
                    <tr>
                      <th>姓名</th>
                      <th>开始评定时间</th>
                      <th>评定完成时间</th>
                      <th>用时（天）</th>
                      <th>姓名</th>
                      <th>审核完成时间</th>
                      <th>用时（天）</th>
                      <th>姓名</th>
                      <th>审定完成时间</th>
                      <th>用时（天）</th>
                    </tr>
                  </thead>
                </table>
                <div class="scroll-table-body" :style="{overflow:'auto',maxHeight:body_height - 160 + 'px','overflow-y':'scroll','overflow-x':'hidden'}">
                    <table style="margin:0px 10px 0 0;position: relative;width:100%;font-size:13px;float:left;border:none;" class="table table-bordered table-hover table-striped">
                        <colgroup>
                          <col style="width:4%"/>
                    <col style="width:8%"/>
                    <col style="width:6%"/>
                    <col style="width:10%"/>
                    <col style="width:10%"/>
                    <col style="width:7%"/>
                    <col style="width:6%"/>
                    <col style="width:10%"/>
                    <col style="width:7%"/>
                    <col style="width:6%"/>
                    <col style="width:10%"/>
                    <col style="width:7%"/>
                    <col style="width:9%"/>
                        </colgroup>
                        <tbody>
                          <tr v-for="(data,$index) in tableData" :key="$index">
                            <td>{{$index+1}}</td>
                            <td>{{data.gradeStatusName}}</td>
                            <td>{{data.leaderName}}</td>
                            <td>{{data.actionGradeDate==null?'':data.actionGradeDate.substring(0,10)}}</td>
                            <td>{{data.leaderDate==null?'':data.leaderDate.substring(0,10)}}</td>
                            <td>{{data.useDay1}}</td>
                            <td>{{data.checkerName}}</td>
                            <td>{{data.checkerDate==null?'':data.checkerDate.substring(0,10)}}</td>
                            <td>{{data.useDay2}}</td>
                            <td>{{data.approveName}}</td>
                            <td>{{data.approveDate==null?'':data.approveDate.substring(0,10)}}</td>
                            <td>{{data.useDay3}}</td>
                            <td ><a @click="showDialog(data)">查看</a></td>
                          </tr>
                      </tbody>
                    </table>
                </div>
            </div>
          </div>
        </div>
      </div>
  </div>
</template>

<script>
import {findMeetingConfirmBy,findMergeListByCompanyGroupId,updateMeetingLevelToOA } from '@/api'
import RankGradeDetails_list from './RankGradeDetails_list'


export default {
  data(){
    return {
      isLoading:true,
      body_height: 0,
      tableData:[],
      year:"",
      month:"12",//固定年度，半年不显示此页面
      companyGroupId:"",
      positionGradeId:"",
      deptArea:Utils.getQueryParams('deptArea')
    }
  },
  computed:{
    disabledButton(){
      if(this.isLoading || this.tableData.length==0){
        return true;
      }
      else if(this.tableData&&this.tableData.length!=0){
        return !(this.tableData[0].isMeetingConfirm==1&&this.tableData[0].actionGradeDate==null);
      }
      return false
    }
  },
  methods:{
    getCompanyName(){
      let companyName=Utils.getCompanyNameByGroupId(this.companyGroupId);
      if (this.positionGradeId=="0006" || this.positionGradeId=="0007" || this.positionGradeId=="0032" || this.positionGradeId=="0033"){
        companyName="全集团";
      }
      return companyName;
    },
    getGradeName(){
      let name=Utils.getGradeLevlByGradeId(this.positionGradeId);
      if (this.deptArea=="A"){
        name=name+"-生产一线"
      }
      else if (this.deptArea=="B"){
        name=name+"-生产辅助"
      }
      else if (this.deptArea=="C"){
        name=name+"-生产支持"
      }
      return name;
    },
    checkStandardSize(){
      let count = 0;
      for(let i=0;i<this.tableData.length;i++){
        let data = this.tableData[i];
        if(data.standardPoint){
          count ++;
        }
        //先不限制评价对象人数
        // if(count>=2){
        //   return false;
        // }
      }
      return true;
    },
    showDescrDialog(){
      Utils.layerBox.layerDialogOpen({
          title:'说明',
          area:['420px','180px'],
          btn:['关闭'],
          content: `<div style="padding:10px 20px;margin:0 auto;">
          <p style="line-height:24px;">
          1、本表作用：（1）用于发起OA的职级评定，（2）用于跟踪查看各评定人的评定完成情况。<br/>
          </p></div>`});
    },
    showStandardName(data){
      // data.standardLevel = data.standardPoint ? data.standardPoint == 90 ? '优' : '标杆' : '';
       data.standardPoint = data.standardLevel=="优" ? 90 : data.standardPoint;
    },
    _updateMeetingLevelToOA(){
      if(this.disabledButton){
        return;
      }
      layer.confirm("确认操作“职级评定发起”，请确认？", {
          title:'提示',
          btn: ['确定','取消'] //按钮
        }, (index)=>{
          this.isLoading = true;
          let confirmLeaderId=this.tableData[0].confirmLeaderId;
          updateMeetingLevelToOA({confirmLeaderId: confirmLeaderId,positionGradeId:this.positionGradeId}).then(res=>{
            let result = res.success;
            layer.msg(`评定${result ? '成功' : '失败'}`, {icon: result ? 1 : 2,shade:0.3,shadeClose:true});
            this._findMeetingConfirmBy();
            layer.close(index)
          }).catch(err=>{
            layer.msg(`${err.msg}`, {icon: 2,shade:0.3,shadeClose:true});
            layer.close(index)
          })
        });
    },

    _findMeetingConfirmBy(){
      /*先判断有权限才可以查询(modify by huangdh@2019-05-24)*/
      if (this.$store.state.doubleCol.arrAuths.point_ablity_findMeetingConfirmBy)
      {
          findMeetingConfirmBy({companyGroupId:this.companyGroupId,year:this.year,month:this.month,positionGradeId:this.positionGradeId,deptArea:this.deptArea}).then(res=>{
            this.tableData = res.data || [];
            this.isLoading = false;
          });    
      }
      else {
          layer.msg(`对不起，您没有权限查询本界面.`, {icon: 2,shade:0.3,shadeClose:true});
      }  
    },

    showDialog(item) {
      let layerDialog = Utils.layerBox.layerDialogOpen({
        title: "【职级评定】表",
        btn: [],
        maxmin: false, //开启最大化最小化按钮
        area: ["900px", "420px"],
        btn1: (index, layero) => {
        },
        btn2: (index, layero) => {
        }
      });
      this.dialogComponent = Utils.layerBox.layerLoadVueComponent({
        layer: layerDialog,
        component: RankGradeDetails_list,
        methods: {
          doConfirm: (params) => {
            layer.close(layerDialog);
          },
          doClose: () => {
            layer.close(layerDialog); //关闭对话框
          }
        },
        props: {
          confirmLeaderId:item.confirmLeaderId,
          leader3Name:item.relationLeader3,
          leaderName:item.leaderName,
          checkerName:item.checkerName,
          approveName:item.approveName
        }
      });
    },
    initPage(){
      this.pageResize();
      const {companyGroupId,year,month,positionGradeId} = this.$route.params;
      this.companyGroupId = companyGroupId;
      this.year = year;
      this.positionGradeId = positionGradeId;
      this._findMeetingConfirmBy();
      //this._findMergeListByCompanyGroupId();
    },
    pageResize(){
      this.body_height = $(window).height();  
    }
  },
  watch:{
    "$route":function(){
      this.initPage();
    }
  },
  created(){
    this.initPage();
    $(window).resize(this.pageResize);
  }
}
</script>

