const doubleCol = {
  state: {  //存储变量
    // commParams:{},//公共参数
    // routerCommParams:{},//路由跳转公共参数
    arrAuths:{},//路由跳转公共参数
  },
  mutations: {  //设置变量方法
    // setCommParams(state, params){
    //   state.commParams = params;
    // },
    // setRouterCommParams(state, params){
    //   state.routerCommParams = params;
    // },
    authsValue(state,arrayValue){
      state.arrAuths = arrayValue;
    }
  }
};

export default doubleCol;  //抛出模块
