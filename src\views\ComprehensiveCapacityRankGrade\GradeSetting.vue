<template>
  <div class="grade-setting-container">
    <div class="row" style="line-height: 40px; max-width: 825px">
      <div class="col-xs-12 text-left">
        <h4 class="col-xs-7" style="padding: 0">
          【{{ getCompanyName() }}】管理人员综合能力【级别】配置表
        </h4>
        <div class="col-xs-5 text-right" style="padding: 0">
          <button
            class="btn btn-primary btn-xs"
            @click="dialogShow(true)"
            v-if="$store.state.doubleCol.arrAuths.base_baseLevel_updateBatch"
          >
            新增级别
          </button>
          <!-- $router.push(`/${$route.params.year}/${$route.params.month}/menu/1`) -->
          <button class="btn btn-primary btn-xs" @click="$router.back()">
            返回目录
          </button>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-xs-12">
        <div class="list-table" style="width: 600px">
          <div
            class="scroll-table"
            style="width: 100%; height: 100%; overflow-x: hidden"
          >
            <table class="scroll-table-header table-bordered">
              <colgroup>
                <col style="width: 8%" />
                <col style="width: 23%" />
                <col style="width: 23%" />
                <col style="width: 23%" />
              </colgroup>
              <thead>
                <tr>
                  <th>序号</th>
                  <th>职等</th>
                  <th>级别</th>
                  <th>操作</th>
                </tr>
              </thead>
            </table>
            <div
              class="scroll-table-body"
              :style="{
                overflow: 'auto',
                maxHeight: body_height - 135 + 'px',
                'overflow-y': 'scroll',
                'overflow-x': 'hidden',
              }"
            >
              <table
                style="
                  margin: 0px 10px 0 0;
                  position: relative;
                  font-size: 13px;
                  float: left;
                  border: none;
                "
                class="table table-bordered table-hover table-striped"
              >
                <colgroup>
                  <col style="width: 8%" />
                  <col style="width: 23%" />
                  <col style="width: 23%" />
                  <col style="width: 23%" />
                </colgroup>
                <tbody>
                  <tr v-for="(data, $index) in tableData" :key="$index">
                    <td v-if="data.start" :rowspan="data.end_index">
                      {{ data.order }}
                    </td>
                    <td v-if="data.start" :rowspan="data.end_index">
                      {{ data.positionGradeName }}
                    </td>
                    <td>{{ data.level }}</td>
                    <td v-if="data.start" :rowspan="data.end_index">
                      <a
                        @click="editHandle(data)"
                        style="padding: 0 15px"
                        v-if="
                          $store.state.doubleCol.arrAuths
                            .base_baseLevel_updateBatch
                        "
                        >编辑</a
                      >
                      <a
                        @click="deleteHandle(data.positionGradeId)"
                        style="padding: 0 15px"
                        v-if="
                          $store.state.doubleCol.arrAuths
                            .base_baseLevel_updateBatch
                        "
                        >删除</a
                      >
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  findListByCompanyGroupId,
  deleteByPositionGradeId,
  updateLevelBatch,
  findListByPositionGradeId,
  findPositionGradeByCompanyGroupId,
  findGradeSettingList,
} from "@/api";
export default {
  data() {
    return {
      isLoading: true,
      body_height: 0,
      tableData: [],
      gradeList: [],
      year: "",
      month: "",
      companyGroupId: "",
    };
  },
  methods: {
    getCompanyName() {
      return Utils.getCompanyNameByGroupId(this.companyGroupId);
    },
    _updateLevelBatch(list = [], isAdd) {
      if (this.isLoading) {
        return;
      }
      this.isLoading = true;
      updateLevelBatch(list).then((res) => {
        let result = res.success;
        let msg = this.isAdd ? "新增" : "编辑";
        layer.msg(`${msg}${result ? "成功" : "失败"}`, {
          icon: result ? 1 : 2,
          shade: 0.3,
          shadeClose: true,
        });
        this.findListByCompanyGroupId();
      });
    },
    dialogShow(isAdd, positionGradeName, levels = [], positionGradeId = "") {
      let that = this;
      Utils.layerBox.layerDialogOpen({
        area: ["400px", "220px"],
        content: `<div style="width:100%;height:100%;text-align:center;line-height:32px;box-sizing: border-box;padding: 12px 0">
            <div class="row" style="margin:0">
              <label class="col-xs-4 text-right">职等：</label>
              ${
                isAdd
                  ? `<div class="col-xs-8 text-left"><select class="input-sm" id="positionGradeId" style="width:160px;">
                  ${this.gradeList
                    .map(
                      (item) =>
                        `<option value="${item.fd_id}">${item.grade_name}</option>`
                    )
                    .join("\n")}
                </select></div>`
                  : `<span class="col-xs-8 text-left">
                  ${positionGradeName}
                  </span><input type="hidden" id="positionGradeId" value="${positionGradeId}"/>`
              }
            </div>
            <div class="row" style="margin:0;line-height: 20px;margin-top:10px;">
              <label class="col-xs-4 text-right">包含级别：</label>
              <div class="col-xs-8 text-left">
              ${["优", "A", "B", "C"]
                .map(
                  (item) => `<label class="checkbox-inline">
                                        <input type="checkbox" name="positionLevel" value="${item}" ${
                    levels.indexOf(item) !== -1 ? "checked" : ""
                  }>${item}
                                     </label>`
                )
                .join("\n")}
              </div>
            </div>
          </div>`,
        btn1: function (index) {
          let checkbox_arr =
            $("input[type='checkbox'][name='positionLevel']:checked") || [];
          let checkbox_vals = [];
          for (let i = 0; i < checkbox_arr.length; i++) {
            let checkbox = checkbox_arr[i];
            checkbox_vals.push($(checkbox).val());
          }
          if (checkbox_vals) {
            let gradeObj_Arr = [];
            checkbox_vals.forEach((val) => {
              gradeObj_Arr.push({
                companyGroupId: that.companyGroupId,
                fdYear: that.year,
                fdMonth: that.month,
                level: val,
                positionGradeId: $("#positionGradeId").val() || "",
              });
            });
            that.isLoading ? null : that._updateLevelBatch(gradeObj_Arr, isAdd);
          }
          layer.close(index);
        },
      });
    },
    editHandle(data = {}, isAdd = false) {
      findListByPositionGradeId({
        PositionGradeId: data.positionGradeId,
        CompanyGroupId: this.companyGroupId,
        Year: this.year,
        Month: this.month,
      }).then((res) => {
        this.dialogShow(
          false,
          data.positionGradeName,
          (res.data || []).map((item) => item.level),
          data.positionGradeId
        );
      });
    },
    deleteHandle(positionGradeId = "") {
      layer.confirm(
        "确认删除此条数据吗？",
        {
          title: "提示",
          btn: ["确定", "取消"], //按钮
        },
        () => {
          deleteByPositionGradeId({
            companyGroupId: this.companyGroupId,
            year: this.year,
            month: this.month,
            positionGradeId,
          }).then((res) => {
            if (res.success) {
              layer.msg("删除成功", { icon: 1 });
              this.findListByCompanyGroupId();
            } else layer.msg("删除失败", { icon: 2 });
          });
        }
      );
    },
    findListByCompanyGroupId() {
      /*先判断有权限才可以查询(modify by huangdh@2019-05-24)*/
      if (
        this.$store.state.doubleCol.arrAuths
          .base_baseLevel_findListByCompanyGroupId
      ) {
        findListByCompanyGroupId({
          CompanyGroupId: this.companyGroupId,
          Year: this.year,
          Month: this.month,
        }).then((res) => {
          this.isLoading = false;
          if (!res.success) return;
          this.eachData(res.data || []);
        });
      } else {
        layer.msg(`对不起，您没有权限查询本界面.`, {
          icon: 2,
          shade: 0.3,
          shadeClose: true,
        });
      }
    },
    eachData(data) {
      let start = 0,
        end = 0;
      data.forEach((d, index) => {
        if (index === 0) {
          d.order = 1;
          d.start = true;
          start = 0;
        } else {
          if (d.positionGradeId == data[start].positionGradeId) {
            d.order = data[index - 1].order;
            if (data.length === index + 1) {
              data[start].end_index = index + 1;
            }
          } else {
            d.start = true;
            d.order = data[index - 1].order + 1;
            end = index;
            data[start].end_index = end - start;
            start = index;
          }
        }
      });
      this.tableData = data;
    },
    _findPositionGradeByCompanyGroupId() {
      if (this.companyGroupId == "1") {
        findGradeSettingList({ companyGroupId: this.companyGroupId }).then(
          (res) => {
            this.gradeList = res.data || [];
          }
        );
      } else {
        findPositionGradeByCompanyGroupId({
          companyGroupId: this.companyGroupId,
        }).then((res) => {
          this.gradeList = res.data || [];
        });
      }
    },
    initPage() {
      const { companyGroupId, year, month } = this.$route.params;
      this.companyGroupId = companyGroupId;
      this.year = year;
      this.month = month;
      this.findListByCompanyGroupId();
      this._findPositionGradeByCompanyGroupId();
    },
    pageResize() {
      this.body_height = $(window).height();
    },
  },
  watch: {
    $route: function () {
      this.pageResize();
      this.initPage();
    },
  },
  created() {
    this.initPage();
  },
  mounted() {
    this.pageResize();
    $(window).resize(this.pageResize);
  },
};
</script>

<style scoped>
/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}
.list-year-data {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
.scroll-table-body {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
</style>
